const express = require('express');
const router = express.Router();
const { createCategory, updateCategory, getAllCategory, getCategoryDetails, getAllCategorySortList, deleteCategory, updateCategoryStatus, restoreCategory } = require('../../controller/cms/category.controller');

const { createCategoryValidation, updateCategoryValidation, updateCategoryStatusValidation } = require('../../middlewares/validations/cms/category/categoryValidation');

router.post('/create', createCategoryValidation, createCategory);
router.put('/update/:id', updateCategoryValidation, updateCategory);
router.post('/list', getAllCategory);
router.get('/details/:id', getCategoryDetails);
router.get('/sort-list', getAllCategorySortList);
router.delete('/delete/:id', deleteCategory);
router.put('/update/status/:id', updateCategoryStatusValidation, updateCategoryStatus);
router.put('/restore/:id', restoreCategory);

module.exports = router;