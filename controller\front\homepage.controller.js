// Service declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');
const { ReS, escapeRegex } = require('../../services/general.helper');
const { componentType, componentState, filterTypes } = require('../../config/component.constant');
const { repositoryState, publishState, gitlabImportStatus } = require('../../config/gitlab.constant');

// Models declaration
const Components = require('../../models/component.model').Components;
const Tags = require('../../models/tag.model').Tags;
const Users = require('../../models/users.model').Users;
const Category = require('../../models/category.model').Category;
const SupportedPlatforms = require('../../models/supported_platforms.model').SupportedPlatforms;
const GitlabRepository = require('../../models/gitlab_repository.model').GitlabRepository;
const Collection = require('../../models/collection.model').Collection;
const CollectionItems = require('../../models/collection_items.model').CollectionItems;

// Npm declaration
const mongoose = require('mongoose');

async function getPopularElements(req, res) {
    try {
        // Set default conditions
        const conditions = {
            parent_id: { $exists: false },
            component_state: componentState.PUBLISHED,
            component_type: {
                $in: [componentType.ELEMENTS]
            },
            created_by_user: { $exists: true }
        };

        // Set default sort
        const sort = {
            'views': -1,
            'created_at': -1
        };

        const limit = (req.query.limit) ? parseInt(req.query.limit) : 15;

        const query = [{
            $match: conditions
        }, {
            '$sort': sort
        }, {
            '$limit': limit
        }, {
            $lookup: {
                from: 'users', // Join with users collection
                let: { creatorId: '$created_by_user' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$creatorId'] }
                        }
                    },
                    {
                        $project: {
                            username: 1,
                            first_name: 1,
                            last_name: 1,
                            avatar: 1
                        }
                    }
                ],
                as: 'created_by_user'
            }
        }, {
            $lookup: {
                from: 'categories', // Join with categories collection
                let: { categoryId: '$category_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$categoryId'] }
                        }
                    },
                    {
                        $project: {
                            category_name: 1,
                            category_slug: 1
                        }
                    }
                ],
                as: 'category_id'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                created_at: 1,
                is_paid: 1,
                orientation: 1,
                views: 1,
                likes: 1,
                bookmarks: 1,
                downloads: 1,
                created_by_user: { $arrayElemAt: ['$created_by_user', 0] },
                category_id: { $arrayElemAt: ['$category_id', 0] },
                component_state: 1,
                component_type: 1,
                elements_data: 1,
                live_preview: 1,
                gif_status: 1,
                gif_url: 1,
                linked_output: 1,
                element_container_meta: 1
            }
        }];

        const elementList = await Components.aggregate(query);

        return ReS(res, constants.success_code, 'Data Fetched', elementList);
    } catch (err) {
        logger.error(`Error at Front Controller getAllComponents${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getPopularProjects(req, res) {
    try {
        // Set default conditions
        const conditions = {
            parent_id: { $exists: false },
            component_state: componentState.PUBLISHED,
            component_type: {
                $in: [componentType.REPOSITORY]
            },
            created_by_user: { $exists: true },
            is_featured: false
        };

        // Set default sort
        const sort = {
            'views': -1,
            'created_at': -1
        };

        const limit = (req.query.limit) ? parseInt(req.query.limit) : 15;

        const query = [{
            $match: conditions
        }, {
            '$sort': sort
        }, {
            '$limit': limit
        }, {
            $lookup: {
                from: 'users', // Join with users collection
                let: { creatorId: '$created_by_user' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$creatorId'] }
                        }
                    },
                    {
                        $project: {
                            username: 1,
                            first_name: 1,
                            last_name: 1,
                            avatar: 1
                        }
                    }
                ],
                as: 'created_by_user'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                video_url: 1,
                created_at: 1,
                is_paid: 1,
                orientation: 1,
                views: 1,
                likes: 1,
                bookmarks: 1,
                downloads: 1,
                created_by_user: {
                    $arrayElemAt: ['$created_by_user', 0]
                },
                component_state: 1,
                component_type: 1,
                short_description: 1,
                is_featured: 1
            }
        }];

        const elementList = await Components.aggregate(query);

        return ReS(res, constants.success_code, 'Data Fetched', elementList);
    } catch (err) {
        logger.error(`Error at Front Controller getPopularProjects ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getFeaturedProjects(req, res) {
    try {
        // Set default conditions
        const conditions = {
            parent_id: { $exists: false },
            component_state: componentState.PUBLISHED,
            component_type: {
                $in: [componentType.REPOSITORY]
            },
            created_by_user: { $exists: true },
            is_featured: true
        };

        // Set default sort
        const sort = {
            'views': -1,
            'created_at': -1
        };

        const limit = (req.query.limit) ? parseInt(req.query.limit) : 10;

        const query = [{
            $match: conditions
        }, {
            '$sort': sort
        }, {
            '$limit': limit
        }, {
            $lookup: {
                from: 'users', // Join with users collection
                let: { creatorId: '$created_by_user' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$creatorId'] }
                        }
                    },
                    {
                        $project: {
                            username: 1,
                            first_name: 1,
                            last_name: 1,
                            avatar: 1
                        }
                    }
                ],
                as: 'created_by_user'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                video_url: 1,
                created_at: 1,
                is_paid: 1,
                orientation: 1,
                views: 1,
                likes: 1,
                bookmarks: 1,
                downloads: 1,
                created_by_user: {
                    $arrayElemAt: ['$created_by_user', 0]
                },
                component_state: 1,
                component_type: 1,
                is_featured: 1,
                short_description: 1
            }
        }];

        const elementList = await Components.aggregate(query);

        return ReS(res, constants.success_code, 'Data Fetched', elementList);
    } catch (err) {
        logger.error(`Error at Front Controller getFeaturedProjects ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getAllElements(req, res) {
    try {
        const totalDocuments = await Components.countDocuments();
        // Set default conditions
        const conditions = {
            parent_id: { $exists: false },
            component_state: componentState.PUBLISHED,
            component_type: {
                $in: [componentType.ELEMENTS]
            },
            created_by_user: { $exists: true }
        };
        // Set default sort
        let sort = {
            'created_at': -1
        };

        if (req.body.title) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.title);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        if (req.body.category_slug) {

            const categoryData = await Category.findOne({
                category_slug: req.body.category_slug
            }, 'category_name category_slug image_url short_description').lean();

            if (categoryData && categoryData?._id) {
                conditions['category_id'] = categoryData._id;
            }
        }

        if (req.body.is_paid) {
            conditions['is_paid'] = req.body.is_paid;
        }

        if (req.body.tag) {
            conditions['identification_tag'] = req.body.tag
        }
        // Check if sort_by property exists in the request body and set sort criteria accordingly
        if (req.body.sort_by) {
            if (req.body.sort_by === filterTypes.MOST_LIKED) {
                // Sort by likes in descending order
                sort = {
                    likes: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.MOST_POPULAR) {
                // Sort by views in descending order
                sort = {
                    views: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_ADDITIONS) {
                // Sort by created_at in descending order
                sort = {
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.PREMIUM) {
                sort = {
                    is_paid: -1,
                    created_at: -1
                };
            }
        }

        const limit = (req.body.limit) ? req.body.limit : 15;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await Components.countDocuments(conditions);

        const query = [{
            $match: conditions
        }, {
            '$sort': sort
        }, {
            '$skip': skip
        }, {
            '$limit': limit
        }, {
            $lookup: {
                from: 'users', // Join with users collection
                let: { creatorId: '$created_by_user' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$creatorId'] }
                        }
                    },
                    {
                        $project: {
                            username: 1,
                            first_name: 1,
                            last_name: 1,
                            avatar: 1
                        }
                    }
                ],
                as: 'created_by_user'
            }
        }, {
            $lookup: {
                from: 'categories', // Join with categories collection
                let: { categoryId: '$category_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$categoryId'] }
                        }
                    },
                    {
                        $project: {
                            category_name: 1,
                            category_slug: 1
                        }
                    }
                ],
                as: 'category_id'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                created_at: 1,
                is_paid: 1,
                orientation: 1,
                views: 1,
                likes: 1,
                bookmarks: 1,
                downloads: 1,
                component_state: 1,
                component_type: 1,
                created_by_user: { $arrayElemAt: ['$created_by_user', 0] },
                category_id: { $arrayElemAt: ['$category_id', 0] },
                identification_tag: 1,
                elements_data: 1,
                live_preview: 1,
                gif_status: 1,
                gif_url: 1,
                linked_output: 1,
                element_container_meta: 1
            }
        }];

        const componentList = await Components.aggregate(query);
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at Front Controller getAllElementList ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getPopularElementTags(req, res) {
    try {
        // Define the filter to select active tags only
        const filter = {
            is_active: true,
            elements_popularity: {
                $ne: 0
            }
        };

        // Default sort order: by the number of project_popularity in descending order
        const sort = {
            elements_popularity: -1,
            updated_at: -1
        };

        // Default tags limit
        const limit = (req.query.limit) ? parseInt(req.query.limit) : 50;

        // Aggregation pipeline
        const pipelines = [
            {
                $match: filter // Filter tags by is_active status
            },
            {
                $lookup: {
                    from: 'components', // Join with users collection
                    let: { title: '$title' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [{
                                        $in: ['$$title', '$identification_tag']
                                    }, {
                                        $eq: ['$component_state', componentState.PUBLISHED]
                                    }, {
                                        $eq: ['$component_type', componentType.ELEMENTS]
                                    }]
                                }
                            }
                        },
                        {
                            $limit: 1 // Stop searching after finding the first published component
                        },
                        {
                            $project: { _id: 1 } // Only fetch the necessary field (_id)
                        }
                    ],
                    as: 'components'
                }
            },
            {
                $match: {
                    'components': { $ne: [] } // Only keep tags with at least one published component
                }
            },
            {
                $sort: sort // Sort by the number of popularity
            },
            {
                $limit: limit
            },
            {
                $project: {
                    title: 1, // Include title
                    slug: 1, // Include slug
                    is_active: 1, // Include is_active status
                    elements_popularity: 1 // Calculate the number of components,
                }
            }
        ];

        // Execute the aggregation query on the Tags collection
        const tagList = await Tags.aggregate(pipelines);

        // Return the successful response with the fetched data
        return ReS(res, constants.success_code, 'Data Fetched', tagList);
    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at Front Controller getPopularElementTags: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getTopCreators(req, res) {
    try {

        const conditions = {
            is_creator: true,
            is_active: true,
            username: {
                $exists: true
            }
        };

        const limit = (req.query.limit) ? parseInt(req.query.limit) : 50;

        const pipelines = [{
            $match: conditions
        }, {
            $lookup: {
                from: 'components', // Join with components collection
                let: { creatorId: '$_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: {
                                $and: [
                                    { $eq: ['$created_by_user', '$$creatorId'] },
                                    { $eq: ['$component_state', componentState.PUBLISHED] }
                                ]
                            }
                        }
                    },
                    {
                        $group: {
                            _id: null,
                            total_posts: { $sum: 1 }
                        }
                    }
                ],
                as: 'components_count'
            }
        }, {
            $project: {
                first_name: 1,
                last_name: 1,
                username: 1,
                email: 1,
                avatar: 1,
                total_views: 1,
                total_likes: 1,
                followers_count: 1,
                published_files: {
                    $ifNull: [{ $arrayElemAt: ['$components_count.total_posts', 0] }, 0]
                }
            }
        }, {
            $match: {
                published_files: { $gt: 0 } // Filter out records where totalPosts is 0
            }
        }, {
            $sort: {
                total_views: -1,
                published_files: -1
            }
        }, {
            $limit: limit
        }];

        const topCreators = await Users.aggregate(pipelines);

        // Return the successful response with the fetched data
        return ReS(res, constants.success_code, 'Data Fetched', topCreators);
    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at Front Controller getTopCreators: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getElementsSearchSuggestions(req, res) {
    try {
        // Set Default conditions to find the component
        const conditions = {
            parent_id: { $exists: false },
            component_state: componentState.PUBLISHED,
            component_type: {
                $in: [componentType.ELEMENTS]
            },
            created_by_user: { $exists: true }
        };

        // If there's a variantId provided, add it to the conditions
        if (req.query && req.query.variant_id) {
            conditions['$or'] = [{
                variant_id: new mongoose.Types.ObjectId(req.query.variant_id)
            }, {
                _id: new mongoose.Types.ObjectId(req.query.variant_id)
            }];
        };

        // If there's a search text provided, add it to the conditions
        if (req.query.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.query.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        // Find the distinct titles of components based on the conditions
        const suggestionList = await Components.distinct('title', conditions).lean();

        // Return success response with component titles
        return ReS(res, constants.success_code, 'Data Fetched', suggestionList);
    } catch (err) {
        // Log any errors that occur
        logger.error(`Error at Front Controller getElementsSearchSuggestions ${err}`);
        // Return a server error response
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getAllElementVariations(req, res) {
    try {
        const { slug } = req.params;

        const element = await Components.findOne({
            slug: slug,
            component_type: {
                $in: [componentType.ELEMENTS]
            },
            component_state: componentState.PUBLISHED
        }).lean();

        if (!element) {
            return ReS(res, constants.resource_not_found, 'Oops! Element Not Found.');
        }

        const variantId = (element?.variant_id) ? element?.variant_id : element?._id;

        const totalDocuments = await Components.countDocuments();
        // Set default conditions
        const conditions = {
            parent_id: { $exists: false },
            component_state: componentState.PUBLISHED,
            component_type: {
                $in: [componentType.ELEMENTS]
            },
            created_by_user: { $exists: true },
            $or: [{
                variant_id: new mongoose.Types.ObjectId(variantId)
            }, {
                _id: new mongoose.Types.ObjectId(variantId)
            }]
        };
        // Set default sort
        let sort = {
            'created_at': -1
        };

        if (req.body.title) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.title);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        if (req.body.category_slug) {

            const categoryData = await Category.findOne({
                category_slug: req.body.category_slug
            }, 'category_name category_slug image_url short_description').lean();

            if (categoryData && categoryData?._id) {
                conditions['category_id'] = categoryData._id;
            }
        }

        if (req.body.is_paid) {
            conditions['is_paid'] = req.body.is_paid;
        }

        if (req.body.tag) {
            conditions['identification_tag'] = req.body.tag
        }
        // Check if sort_by property exists in the request body and set sort criteria accordingly
        if (req.body.sort_by) {
            if (req.body.sort_by === filterTypes.MOST_LIKED) {
                // Sort by likes in descending order
                sort = {
                    likes: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.MOST_POPULAR) {
                // Sort by views in descending order
                sort = {
                    views: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_ADDITIONS) {
                // Sort by created_at in descending order
                sort = {
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.PREMIUM) {
                sort = {
                    is_paid: -1,
                    created_at: -1
                };
            }
        }

        const limit = (req.body.limit) ? req.body.limit : 12;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await Components.countDocuments(conditions);

        const query = [{
            $match: conditions
        }, {
            '$sort': sort
        }, {
            '$skip': skip
        }, {
            '$limit': limit
        }, {
            $lookup: {
                from: 'users', // Join with users collection
                let: { creatorId: '$created_by_user' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$creatorId'] }
                        }
                    },
                    {
                        $project: {
                            username: 1,
                            first_name: 1,
                            last_name: 1,
                            avatar: 1
                        }
                    }
                ],
                as: 'created_by_user'
            }
        }, {
            $lookup: {
                from: 'categories', // Join with categories collection
                let: { categoryId: '$category_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$categoryId'] }
                        }
                    },
                    {
                        $project: {
                            category_name: 1,
                            category_slug: 1
                        }
                    }
                ],
                as: 'category_id'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                created_at: 1,
                is_paid: 1,
                orientation: 1,
                views: 1,
                likes: 1,
                bookmarks: 1,
                created_by_user: { $arrayElemAt: ['$created_by_user', 0] },
                category_id: { $arrayElemAt: ['$category_id', 0] },
                component_state: 1,
                component_type: 1,
                identification_tag: 1,
                elements_data: 1,
                live_preview: 1,
                gif_status: 1,
                gif_url: 1,
                linked_output: 1,
                element_container_meta: 1,
                is_master: {
                    $cond: {
                        if: { $eq: ['$_id', new mongoose.Types.ObjectId(variantId)] },
                        then: true,
                        else: false
                    }
                }

            }
        }];

        const componentList = await Components.aggregate(query);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at Front Controller getAllElementVariations ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getAllCreators(req, res) {
    try {

        // Set default conditions
        const conditions = {
            is_creator: true,
            is_active: true,
            username: {
                $exists: true
            }
        };

        const totalDocuments = await Users.countDocuments(conditions);

        // Set default sort
        let sort = {
            total_views: -1,
            created_at: -1
        };

        const limit = (req.body.limit) ? req.body.limit : 15;
        const skip = (req.body.skip) ? req.body.skip : 0;

        // Check if sort_by property exists in the request body and set sort criteria accordingly
        if (req.body.sort_by) {
            if (req.body.sort_by === filterTypes.MOST_LIKED) {
                // Sort by likes in descending order
                sort = {
                    total_likes: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.MOST_POPULAR) {
                // Sort by views in descending order
                sort = {
                    total_views: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_ADDITIONS) {
                // Sort by created_at in descending order
                sort = {
                    created_at: -1
                };
            }
        }

        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            conditions['$or'] = [{
                'username': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }, {
                'first_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }, {
                'last_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }, {
                'email': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        const filterDocuments = await Users.countDocuments(conditions);

        const pipelines = [
            {
                $match: conditions // Apply restrictive filters upfront
            },
            {
                $sort: sort // Sort early in the pipeline
            },
            {
                $skip: skip
            },
            {
                $limit: limit
            },
            {
                $lookup: {
                    from: 'components',
                    let: { creatorId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$created_by_user', '$$creatorId'] },
                                        { $eq: ['$component_state', componentState.PUBLISHED] }
                                    ]
                                }
                            }
                        },
                        {
                            $group: {
                                _id: null,
                                total_posts: { $sum: 1 }
                            }
                        }
                    ],
                    as: 'components_count'
                }
            },
            {
                $project: {
                    first_name: 1,
                    last_name: 1,
                    username: 1,
                    email: 1,
                    avatar: 1,
                    total_views: 1,
                    total_likes: 1,
                    total_bookmarks: 1,
                    biography: 1,
                    followers_count: 1,
                    following_count: 1,
                    created_at: 1,
                    country: 1,
                    published_files: {
                        $ifNull: [{ $arrayElemAt: ['$components_count.total_posts', 0] }, 0]
                    }
                }
            }
        ];

        const topCreators = await Users.aggregate(pipelines);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: topCreators
        };

        // Return the successful response with the fetched data
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at Front Controller getAllCreators: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getTopTechnologies(req, res) {
    try {

        // Set default conditions
        const conditions = {
            is_active: true
        };

        const totalTechnologies = await SupportedPlatforms.countDocuments(conditions).lean();

        // Set default sort
        const sort = {
            totalViews: -1,
            totalPosts: -1
        };

        const limit = (req.query.limit) ? parseInt(req.query.limit) : 8;

        const pipelines = [{
            $match: conditions
        },
        {
            $lookup: {
                from: 'components',
                let: {
                    technology_id: '$_id'
                },
                pipeline: [
                    {
                        $match: {
                            $expr: {
                                $and: [{
                                    $in: ['$$technology_id', { $ifNull: ['$detected_technologies', []] }] // Use $ifNull to ensure an array
                                }]
                            },
                            component_state: componentState.PUBLISHED,
                            component_type: {
                                $in: [componentType.REPOSITORY, componentType.MOBILE]
                            }
                        }
                    },
                    {
                        $lookup: {
                            from: 'users',
                            let: {
                                user_id: '$created_by_user'
                            },
                            pipeline: [{
                                $match: {
                                    $expr: {
                                        $and: [{
                                            $eq: ['$_id', '$$user_id']
                                        }]
                                    }
                                }
                            }, {
                                $project: {
                                    'email': 1,
                                    'username': 1
                                }
                            }],
                            as: 'created_by_user'
                        }
                    },
                    {
                        $lookup: {
                            from: 'categories', // Join with categories collection
                            let: { categoryId: '$category_id' },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: { $eq: ['$_id', '$$categoryId'] }
                                    }
                                },
                                {
                                    $project: {
                                        category_name: 1,
                                        category_slug: 1
                                    }
                                }
                            ],
                            as: 'category_id'
                        }
                    },
                    {
                        $project: {
                            title: 1,
                            slug: 1,
                            image_url: 1,
                            video_url: 1,
                            thumbnail_url: 1,
                            component_type: 1,
                            short_description: 1,
                            detected_technologies: 1,
                            views: 1,
                            likes: 1,
                            downloads: 1,
                            bookmarks: 1,
                            created_by_user: {
                                $arrayElemAt: ['$created_by_user', 0]
                            },
                            category_id: {
                                $arrayElemAt: ['$category_id', 0]
                            }
                        }
                    },
                    {
                        $sort: {
                            views: -1
                        }
                    },
                    {
                        $limit: 10
                    }
                ],
                as: 'components'
            }
        }, {
            $set: {
                totalViews: { $sum: '$components.views' },
                totalPosts: { $size: '$components' }
            }
        }, {
            $match: {
                totalPosts: { $gt: 0 }
            }
        }, {
            $sort: sort
        }, {
            $limit: limit
        }];

        pipelines.push({
            $project: {
                title: 1,
                slug: 1,
                description: 1,
                image_url: 1,
                totalViews: 1,
                totalPosts: 1,
                components: 1
            }
        });

        const topTechnologies = await SupportedPlatforms.aggregate(pipelines);

        // Return the successful response with the fetched data
        return ReS(res, constants.success_code, 'Data Fetched', {
            total_technologies: totalTechnologies,
            top_technologies: topTechnologies
        });
    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at Front Controller getTopTechnologies: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getAllTechnologies(req, res) {
    try {
        const { limit = 10, skip = 0 } = req.body;

        const conditions = { is_active: true };
        const sort = { totalViews: -1, totalPosts: -1, created_at: -1 };

        const pipelines = [{
            $match: conditions
        }, {
            $lookup: {
                from: 'components',
                let: { technology_id: '$_id' },
                pipeline: [{
                    $match: {
                        $expr: { $in: ['$$technology_id', { $ifNull: ['$detected_technologies', []] }] },
                        component_state: componentState.PUBLISHED,
                        component_type: {
                            $in: [componentType.REPOSITORY, componentType.CODESPACE, componentType.MOBILE]
                        }
                    }
                }, {
                    $lookup: {
                        from: 'users',
                        localField: 'created_by_user',
                        foreignField: '_id',
                        as: 'created_by_user'
                    }
                }, {
                    $set: {
                        created_by_user: {
                            $arrayElemAt: ['$created_by_user', 0]
                        }
                    }
                }, {
                    $lookup: {
                        from: 'gitlab_repositories',
                        let: {
                            public_repository_id: '$public_repository_id'
                        },
                        pipeline: [{
                            $match: {
                                $expr: {
                                    $and: [{
                                        $eq: ['$_id', '$$public_repository_id']
                                    }]
                                }
                            }
                        }, {
                            $project: {
                                stars: 1,
                                forks: 1,
                                gitlab_languages: 1,
                                last_pushed_at: 1
                            }
                        }],
                        as: 'public_repository_id'
                    }
                }, {
                    $lookup: {
                        from: 'categories', // Join with categories collection
                        let: { categoryId: '$category_id' },
                        pipeline: [
                            {
                                $match: {
                                    $expr: { $eq: ['$_id', '$$categoryId'] }
                                }
                            },
                            {
                                $project: {
                                    category_name: 1,
                                    category_slug: 1
                                }
                            }
                        ],
                        as: 'category_id'
                    }
                }, {
                    $project: {
                        title: 1,
                        slug: 1,
                        image_url: 1,
                        thumbnail_url: 1,
                        video_url: 1,
                        component_type: 1,
                        short_description: 1,
                        detected_technologies: 1,
                        views: 1,
                        likes: 1,
                        downloads: 1,
                        bookmarks: 1,
                        is_paid: 1,
                        created_by_user: {
                            email: 1,
                            username: 1,
                            first_name: 1,
                            last_name: 1,
                            avatar: 1,
                            biography: 1
                        },
                        public_repository_id: {
                            $arrayElemAt: ['$public_repository_id', 0]
                        },
                        category_id: {
                            $arrayElemAt: ['$category_id', 0]
                        },
                    }
                }, {
                    $sort: {
                        views: -1
                    }
                }, {
                    $limit: 10
                }],
                as: 'components'
            }
        },
        {
            $set: {
                totalViews: { $sum: '$components.views' },
                totalPosts: { $size: '$components' }
            }
        },
        { $match: { totalPosts: { $gt: 0 } } },
        {
            $facet: {
                metadata: [{ $count: 'recordsFiltered' }],
                data: [
                    { $sort: sort },
                    { $skip: skip },
                    { $limit: limit },
                    {
                        $project: {
                            title: 1,
                            slug: 1,
                            description: 1,
                            image_url: 1,
                            totalViews: 1,
                            totalPosts: 1,
                            components: 1
                        }
                    }
                ]
            }
        }
        ];

        const [result] = await SupportedPlatforms.aggregate(pipelines);

        const responseObj = {
            recordsFiltered: result.metadata[0]?.recordsFiltered || 0,
            list: result.data
        };

        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at Front Controller getAllTechnologies: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getTechnologyWithProjects(req, res) {
    try {

        const { slug } = req.params;

        const technology = await SupportedPlatforms.findOne({
            slug: slug
        }).lean();

        if (!technology) {
            return ReS(res, constants.resource_not_found, 'Oops! Technology not found');
        }

        // Set default conditions
        const conditions = {
            detected_technologies: new mongoose.Types.ObjectId(technology._id),
            component_state: componentState.PUBLISHED,
            component_type: {
                $in: [componentType.REPOSITORY, componentType.CODESPACE, componentType.MOBILE]
            }
        };

        const filterDocuments = await Components.countDocuments(conditions);

        // Set default sorting
        let sort = {
            'created_at': -1
        };

        // Check if sort_by property exists in the request body and set sort criteria accordingly
        if (req.body.sort_by) {
            if (req.body.sort_by === filterTypes.MOST_LIKED) {
                // Sort by likes in descending order
                sort = {
                    likes: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.MOST_POPULAR) {
                // Sort by views in descending order
                sort = {
                    views: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_ADDITIONS) {
                // Sort by created_at in descending order
                sort = {
                    created_at: -1
                };
            }
        }

        const limit = (req.body.limit) ? req.body.limit : 10;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const pipelines = [{
            $match: conditions
        }, {
            $sort: sort
        }, {
            $skip: skip
        }, {
            $limit: limit
        }, {
            $lookup: {
                from: 'users',
                let: {
                    user_id: '$created_by_user'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$user_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'email': 1,
                        'username': 1,
                        'first_name': 1,
                        'last_name': 1,
                        'avatar': 1,
                        'biography': 1
                    }
                }],
                as: 'created_by_user'
            }
        },
        {
            $lookup: {
                from: 'gitlab_repositories',
                let: {
                    public_repository_id: '$public_repository_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$public_repository_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        stars: 1,
                        forks: 1,
                        gitlab_languages: 1,
                        last_pushed_at: 1
                    }
                }],
                as: 'public_repository_id'
            }
        },
        {
            $lookup: {
                from: 'categories', // Join with categories collection
                let: { categoryId: '$category_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$categoryId'] }
                        }
                    },
                    {
                        $project: {
                            category_name: 1,
                            category_slug: 1
                        }
                    }
                ],
                as: 'category_id'
            }
        },
        {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                video_url: 1,
                component_type: 1,
                short_description: 1,
                likes: 1,
                views: 1,
                downloads: 1,
                bookmarks: 1,
                detected_technologies: 1,
                is_paid: 1,
                created_by_user: {
                    $arrayElemAt: ['$created_by_user', 0]
                },
                public_repository_id: {
                    $arrayElemAt: ['$public_repository_id', 0]
                },
                category_id: {
                    $arrayElemAt: ['$category_id', 0]
                }
            }
        }];

        const componentList = await Components.aggregate(pipelines);

        const responseObj = {
            recordsFiltered: filterDocuments,
            list: componentList,
            technology: technology
        };

        // Return the successful response with the fetched data
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at Front Controller getTechnologyWithProjects: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getPopularProjectTags(req, res) {
    try {
        // Define the filter to select active tags only
        const filter = {
            is_active: true,
            project_popularity: {
                $ne: 0
            }
        };

        // Default sort order: by the number of project_popularity in descending order
        const sort = {
            project_popularity: -1,
            updated_at: -1
        };

        // Default tags limit
        const limit = (req.query.limit) ? parseInt(req.query.limit) : 50;

        // Aggregation pipeline
        const pipelines = [
            {
                $match: filter // Filter tags by is_active status
            },
            {
                $lookup: {
                    from: 'components', // Join with components collection
                    let: { title: '$title' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [{
                                        $in: ['$$title', '$identification_tag']
                                    }, {
                                        $eq: ['$component_state', componentState.PUBLISHED]
                                    }, {
                                        $eq: ['$component_type', componentType.REPOSITORY]
                                    }]
                                }
                            }
                        },
                        {
                            $limit: 1 // Stop searching after finding the first published component
                        },
                        {
                            $project: { _id: 1 } // Only fetch the necessary field (_id)
                        }
                    ],
                    as: 'components'
                }
            },
            {
                $match: {
                    'components': { $ne: [] } // Only keep tags with at least one published component
                }
            },
            {
                $sort: sort // Sort by the number of popularity
            },
            {
                $limit: limit
            },
            {
                $project: {
                    title: 1, // Include title
                    slug: 1, // Include slug
                    is_active: 1, // Include is_active status
                    project_popularity: 1
                }
            }
        ];

        // Execute the aggregation query on the Tags collection
        const tagList = await Tags.aggregate(pipelines);

        // Return the successful response with the fetched data
        return ReS(res, constants.success_code, 'Data Fetched', tagList);
    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at Front Controller getPopularProjectTags: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getPublicCodeSpaces(req, res) {
    try {

        // Set default conditions
        const conditions = {
            state: repositoryState.PUBLIC,
            published_state: publishState.PUBLISHED,
            import_status: gitlabImportStatus.FINISHED,
            is_active: true
        };

        // Set default sort
        let sort = {
            stars: -1,
            last_pushed_at: -1
        };

        const limit = (req.body.limit) ? req.body.limit : 10;
        const skip = (req.body.skip) ? req.body.skip : 0;

        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            conditions['$or'] = [{
                'project_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        if (req.body.sort_by) {
            if (req.body.sort_by === filterTypes.MOST_POPULAR) {
                // Sort by forks in descending order
                sort = {
                    forks: -1,
                    last_pushed_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_UPDATES) {
                // Sort by last_pushed_at in descending order
                sort = {
                    last_pushed_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_ADDITIONS) {
                // Sort by created_at in descending order
                sort = {
                    created_at: -1
                };
            }
        }

        const totalDocuments = await GitlabRepository.countDocuments(conditions);

        const filterDocuments = await GitlabRepository.countDocuments(conditions);

        const pipelines = [
            {
                $match: conditions // Apply restrictive filters upfront
            },
            {
                $sort: sort // Sort early in the pipeline
            },
            {
                $skip: skip
            },
            {
                $limit: limit
            },
            {
                $lookup: {
                    from: 'users',
                    let: {
                        user_id: '$user_id'
                    },
                    pipeline: [{
                        $match: {
                            $expr: {
                                $and: [{
                                    $eq: ['$_id', '$$user_id']
                                }]
                            }
                        }
                    }, {
                        $project: {
                            'first_name': 1,
                            'last_name': 1,
                            'email': 1,
                            'username': 1,
                            'avatar': 1,
                            'biography': 1,
                            'country': 1
                        }
                    }],
                    as: 'user_id'
                }
            },
            // Join with components collection
            {
                $lookup: {
                    from: 'components',
                    let: { component_id: '$component_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$_id', '$$component_id'] }
                            }
                        },
                        {
                            $project: {
                                views: 1,
                                likes: 1,
                                bookmarks: 1
                            }
                        }
                    ],
                    as: 'component_id'
                }
            },
            {
                $project: {
                    project_name: 1,
                    description: 1,
                    state: 1,
                    forks: 1,
                    http_url_to_repo: 1,
                    user_id: 1,
                    gitlab_languages: 1,
                    created_at: 1,
                    last_pushed_at: 1,
                    published_state: 1,
                    user_id: {
                        $arrayElemAt: ['$user_id', 0]
                    },
                    detected_platforms: 1,
                    platform_id: 1,
                    import_status: 1,
                    component_id: {
                        $arrayElemAt: ['$component_id', 0]
                    }
                }
            }
        ];

        const codeSpaces = await GitlabRepository.aggregate(pipelines);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: codeSpaces
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at Front Controller getPublicCodeSpaces: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getCollectionItems(req, res) {
    try {

        const { slug } = req.params;

        const collection = await Collection.findOne({
            slug: slug
        }).lean();

        if (!collection) {
            return ReS(res, constants.bad_request_code, 'Oops! Invalid collection provided.');
        }

        const collectionItems = await CollectionItems.distinct('component_id', { collection_id: collection._id }).lean();

        // Set default conditions
        const conditions = {
            _id: {
                $in: collectionItems
            },
            component_state: componentState.PUBLISHED
        };

        const filterDocuments = await Components.countDocuments(conditions);

        // Set default sorting
        let sort = {
            'created_at': -1
        };

        // Check if sort_by property exists in the request body and set sort criteria accordingly
        if (req.body.sort_by) {
            if (req.body.sort_by === filterTypes.MOST_LIKED) {
                // Sort by likes in descending order
                sort = {
                    likes: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.MOST_POPULAR) {
                // Sort by views in descending order
                sort = {
                    views: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_ADDITIONS) {
                // Sort by created_at in descending order
                sort = {
                    created_at: -1
                };
            }
        }

        const limit = (req.body.limit) ? req.body.limit : 10;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const pipelines = [{
            $match: conditions
        }, {
            $sort: sort
        }, {
            $skip: skip
        }, {
            $limit: limit
        }, {
            $lookup: {
                from: 'users',
                let: {
                    user_id: '$created_by_user'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$user_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'email': 1,
                        'username': 1,
                        'first_name': 1,
                        'last_name': 1,
                        'avatar': 1
                    }
                }],
                as: 'created_by_user'
            }
        },
        {
            $project: {
                title: 1,
                views: 1,
                slug: 1,
                image_url: 1,
                live_preview: 1,
                gif_status: 1,
                gif_url: 1,
                linked_output: 1,
                component_type: 1,
                short_description: 1,
                likes: 1,
                detected_technologies: 1,
                created_by_user: {
                    $arrayElemAt: ['$created_by_user', 0]
                }
            }
        }];

        const componentList = await Components.aggregate(pipelines);

        const responseObj = {
            recordsFiltered: filterDocuments,
            list: componentList
        };

        // Return the successful response with the fetched data
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at Front Controller getCollectionItems: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getAllPublishedItems(req, res) {
    try {
        const totalDocuments = await Components.countDocuments();
        // Set default conditions
        const conditions = {
            component_state: componentState.PUBLISHED,
            created_by_user: { $exists: true }
        };
        // Set default sort
        let sort = {
            'created_at': -1
        };

        if (req.body.title) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.title);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        if (req.body.category_slug) {

            const categoryData = await Category.findOne({
                category_slug: req.body.category_slug
            }, 'category_name category_slug image_url short_description').lean();

            if (categoryData && categoryData?._id) {
                conditions['category_id'] = categoryData._id;
            }
        }

        if (req.body.is_paid) {
            conditions['is_paid'] = req.body.is_paid;
        }

        if (req.body.tag) {
            conditions['identification_tag'] = req.body.tag
        }
        // Check if sort_by property exists in the request body and set sort criteria accordingly
        if (req.body.sort_by) {
            if (req.body.sort_by === filterTypes.MOST_LIKED) {
                // Sort by likes in descending order
                sort = {
                    likes: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.MOST_POPULAR) {
                // Sort by views in descending order
                sort = {
                    views: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_ADDITIONS) {
                // Sort by created_at in descending order
                sort = {
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.PREMIUM) {
                sort = {
                    is_paid: -1,
                    created_at: -1
                };
            }
        }

        const limit = (req.body.limit) ? req.body.limit : 15;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await Components.countDocuments(conditions);

        const query = [{
            $match: conditions
        }, {
            '$sort': sort
        }, {
            '$skip': skip
        }, {
            '$limit': limit
        }, {
            $lookup: {
                from: 'users', // Join with users collection
                let: { creatorId: '$created_by_user' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$creatorId'] }
                        }
                    },
                    {
                        $project: {
                            username: 1,
                            first_name: 1,
                            last_name: 1,
                            avatar: 1
                        }
                    }
                ],
                as: 'created_by_user'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                video_url: 1,
                created_at: 1,
                short_description: 1,
                long_description: 1,
                is_paid: 1,
                orientation: 1,
                views: 1,
                likes: 1,
                bookmarks: 1,
                downloads: 1,
                created_by_user: {
                    $arrayElemAt: ['$created_by_user', 0]
                },
                component_state: 1,
                component_type: 1,
                category_id: 1,
                identification_tag: 1,
                elements_data: 1,
                live_preview: 1,
                gif_status: 1,
                gif_url: 1,
                linked_output: 1,
                element_container_meta: 1
            }
        }];

        const componentList = await Components.aggregate(query);
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at Front Controller getAllPublishedItems ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getAllPopularTags(req, res) {
    try {
        // Define the filter to select active tags only
        const filter = {
            is_active: true
        };

        // Default sort order: by the number of project_popularity in descending order
        const sort = {
            total_popularity: -1,
            updated_at: -1
        };

        // Aggregation pipeline
        const pipelines = [{
            $match: filter // Filter tags by is_active status
        },
        {
            $lookup: {
                from: 'components', // Join with users collection
                let: { title: '$title' },
                pipeline: [
                    {
                        $match: {
                            $expr: {
                                $and: [{
                                    $in: ['$$title', '$identification_tag']
                                }, {
                                    $eq: ['$component_state', componentState.PUBLISHED]
                                }]
                            }
                        }
                    },
                    {
                        $limit: 1 // Stop searching after finding the first published component
                    },
                    {
                        $project: { _id: 1 } // Only fetch the necessary field (_id)
                    }
                ],
                as: 'components'
            }
        },
        {
            $match: {
                'components': { $ne: [] } // Only keep tags with at least one published component
            }
        },
        {
            $project: {
                title: 1,
                slug: 1,
                is_active: 1,
                project_popularity: 1,
                elements_popularity: 1,
                mobile_popularity: 1,
                total_popularity: { $add: ['$project_popularity', '$elements_popularity', '$mobile_popularity'] } // Add the two fields
            }
        },
        {
            $sort: sort // Sort by the number of popularity
        }];
        // Execute the aggregation query on the Tags collection
        const tagList = await Tags.aggregate(pipelines);

        // Return the successful response with the fetched data
        return ReS(res, constants.success_code, 'Data Fetched', tagList);
    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at Front Controller getAllPopularTags: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getAllMobileElements(req, res) {
    try {
        const totalDocuments = await Components.countDocuments();
        // Set default conditions
        const conditions = {
            parent_id: { $exists: false },
            component_state: componentState.PUBLISHED,
            component_type: {
                $in: [componentType.MOBILE]
            },
            created_by_user: { $exists: true }
        };
        // Set default sort
        let sort = {
            'created_at': -1
        };

        if (req.body.title) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.title);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        if (req.body.category_slug) {

            const categoryData = await Category.findOne({
                category_slug: req.body.category_slug
            }, 'category_name category_slug image_url short_description').lean();

            if (categoryData && categoryData?._id) {
                conditions['category_id'] = categoryData._id;
            }
        }

        if (req.body.tag) {
            conditions['identification_tag'] = req.body.tag
        }

        if (req.body.is_paid) {
            conditions['is_paid'] = req.body.is_paid;
        }

        // Check if sort_by property exists in the request body and set sort criteria accordingly
        if (req.body.sort_by) {
            if (req.body.sort_by === filterTypes.MOST_LIKED) {
                // Sort by likes in descending order
                sort = {
                    likes: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.MOST_POPULAR) {
                // Sort by views in descending order
                sort = {
                    views: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_ADDITIONS) {
                // Sort by created_at in descending order
                sort = {
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.FEATURED) {
                // Sort by created_at in descending order
                sort = {
                    is_featured: -1,
                    views: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.PREMIUM) {
                conditions['is_paid'] = true;
            }
        }

        const limit = (req.body.limit) ? req.body.limit : 12;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await Components.countDocuments(conditions);

        const query = [{
            $match: conditions
        }, {
            '$sort': sort
        }, {
            '$skip': skip
        }, {
            '$limit': limit
        }, {
            $lookup: {
                from: 'users', // Join with users collection
                let: { creatorId: '$created_by_user' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$creatorId'] }
                        }
                    },
                    {
                        $project: {
                            username: 1,
                            first_name: 1,
                            last_name: 1,
                            avatar: 1
                        }
                    }
                ],
                as: 'created_by_user'
            }
        }, {
            $lookup: {
                from: 'categories', // Join with categories collection
                let: { categoryId: '$category_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$categoryId'] }
                        }
                    },
                    {
                        $project: {
                            category_name: 1,
                            category_slug: 1
                        }
                    }
                ],
                as: 'category_id'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                video_url: 1,
                created_at: 1,
                is_paid: 1,
                orientation: 1,
                views: 1,
                likes: 1,
                bookmarks: 1,
                downloads: 1,
                created_by_user: { $arrayElemAt: ['$created_by_user', 0] },
                category_id: { $arrayElemAt: ['$category_id', 0] },
                component_state: 1,
                component_type: 1,
                identification_tag: 1,
                short_description: 1,
                is_featured: 1
            }
        }];

        const componentList = await Components.aggregate(query);
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at Front Controller getAllComponentsForHomePage${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getPopularMobileTags(req, res) {
    try {
        // Define the filter to select active tags only
        const filter = {
            is_active: true,
            mobile_popularity: {
                $ne: 0
            }
        };

        // Default sort order: by the number of project_popularity in descending order
        const sort = {
            mobile_popularity: -1,
            updated_at: -1
        };

        // Default tags limit
        const limit = (req.query.limit) ? parseInt(req.query.limit) : 50;

        // Aggregation pipeline
        const pipelines = [
            {
                $match: filter // Filter tags by is_active status
            },
            {
                $lookup: {
                    from: 'components', // Join with components collection
                    let: { title: '$title' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [{
                                        $in: ['$$title', '$identification_tag']
                                    }, {
                                        $eq: ['$component_state', componentState.PUBLISHED]
                                    }, {
                                        $eq: ['$component_type', componentType.MOBILE]
                                    }]
                                }
                            }
                        },
                        {
                            $limit: 1 // Stop searching after finding the first published component
                        },
                        {
                            $project: { _id: 1 } // Only fetch the necessary field (_id)
                        }
                    ],
                    as: 'components'
                }
            },
            {
                $match: {
                    'components': { $ne: [] } // Only keep tags with at least one published component
                }
            },
            {
                $sort: sort // Sort by the number of popularity
            },
            {
                $limit: limit
            },
            {
                $project: {
                    title: 1, // Include title
                    slug: 1, // Include slug
                    is_active: 1, // Include is_active status
                    mobile_popularity: 1 // Calculate the number of components,
                }
            }
        ];

        // Execute the aggregation query on the Tags collection
        const tagList = await Tags.aggregate(pipelines);

        // Return the successful response with the fetched data
        return ReS(res, constants.success_code, 'Data Fetched', tagList);
    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at Front Controller getPopularMobileTags: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getTechnologiesOnlyList(req, res) {
    try {
        const activeTechnologies = await Components.distinct('detected_technologies', {
            component_state: componentState.PUBLISHED,
            component_type: {
                $in: [componentType.REPOSITORY, componentType.CODESPACE, componentType.MOBILE]
            }
        });
        const conditions = {
            is_active: true,
            _id: {
                $in: activeTechnologies
            }
        };
        const technologyList = await SupportedPlatforms.find(conditions, {
            title: 1,
            slug: 1,
            description: 1,
            image_url: 1,
            is_active: 1
        }).sort({ title: 1 });
        return ReS(res, constants.success_code, 'Data Fetched', technologyList);
    } catch (err) {
        logger.error(`Error at Front Controller getTechnologiesOnlyList: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

module.exports = {
    getPopularElements,
    getPopularProjects,
    getFeaturedProjects,
    getAllElements,
    getPopularElementTags,
    getTopCreators,
    getElementsSearchSuggestions,
    getAllElementVariations,
    getAllCreators,
    getTopTechnologies,
    getAllTechnologies,
    getTechnologyWithProjects,
    getPopularProjectTags,
    getPublicCodeSpaces,
    getCollectionItems,
    getAllPublishedItems,
    getAllPopularTags,
    getAllMobileElements,
    getPopularMobileTags,
    getTechnologiesOnlyList
};