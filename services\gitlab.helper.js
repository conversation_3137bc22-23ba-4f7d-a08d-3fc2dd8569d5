/* eslint-disable indent */
const axios = require('axios');

const { gitlabAccessLevel, gitlabDefaultBranch, gitlabProductionBranch, overrideFileExtension, mergeRequestStates, mergeRequestActivities, projectVisibility } = require('../config/gitlab.constant');

const constants = require('../config/constants');

const { getGitlabMasterAccessToken, getUserGitlabAccessToken } = require('../services/settings.service');

const { GitlabRepository } = require('../models/gitlab_repository.model');
const path = require('path');
const fs = require('fs');
const gitIgnoreFilePath = path.join(__dirname, './../config/initial_data/default_gitignore.txt'); // Replace with the path to your file

/**
 * Asynchronously creates a new GitLab user.
 *
 * @param {string} email - The email of the new user.
 * @param {string} name - The name of the new user.
 * @param {string} username - The username for the new user.
 * @param {string} password - The password for the new user.
 * @throws Will throw an error if the user creation fails.
 */
async function createGitLabUser(email, name, username, password) {
    try {
        // Retrieve access token and base URL from environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;
        const gitlabApiUrl = `${baseUrl}/users`;  // Construct the API URL for creating users

        // Prepare the data payload for the API request
        const data = {
            email: email,
            name: name,
            username: username,
            password: password
        };
        // Configure the request parameters for axios
        const config = {
            method: 'post',
            url: gitlabApiUrl,
            headers: {
                'PRIVATE-TOKEN': accessToken,
                'Content-Type': 'application/json'
            },
            data: data  // Convert data object to JSON string
        };

        // Send the request to the GitLab API to create the user
        const response = await axios(config);
        return response.data;
    } catch (err) {
        // Log any errors that occur during the request
        console.error('Error creating GitLab user:', err);
        const config = err.response.config;
        const errLogObj = {
            message: err.message,
            method: config.method,
            url: config.url,
            status: err.response.status,
            resData: err.response.data
        };
        const error = new Error(errLogObj?.resData?.message || errLogObj?.message || 'Unknown');
        error.statusCode = errLogObj.status || constants.server_error_code;
        throw error;
    }
}

/**
 * Creates a personal access token for a specified GitLab user.
 * 
 * @param {string} user_id - The ID of the user to create the token for.
 * @param {string} name - The name of the personal access token.
 * @param {string} expires_at - The expiration date of the token.
 * @param {Array<string>} scopes - The scopes for the personal access token.
 */
async function createUserAccessToken(user_id, name, scopes) {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for creating a personal access token
        const gitlabApiUrl = `${baseUrl}/users/${user_id}/personal_access_tokens`;

        // Prepare the data payload for the API request
        const data = JSON.stringify({
            // 'expires_at': expires_at,
            'name': name,
            'scopes': scopes
        });

        // Configuration object for the API request
        const config = {
            method: 'post',
            url: gitlabApiUrl,
            headers: {
                'PRIVATE-TOKEN': accessToken,
                'Content-Type': 'application/json'
            },
            data: data
        };

        // Make the API request to create the personal access token
        const response = await axios(config);
        return response.data;
    } catch (error) {
        // Log and rethrow the error if the API request fails
        console.error('Error creating user access token:', error);
        throw new Error(error);
    }
}

/**
 * Fetches the content of a GitLab repository's tree.
 * 
 * @param {string} project_id - The ID of the project to fetch the content for.
 * @param {string} [path=''] - The path within the repository to fetch content from. Defaults to the root directory.
 * @param {string} [branchName='development'] - The name of the branch to fetch the content from. Defaults to 'development'.
 * @returns {Promise<Object>} - A promise that resolves to the repository content data.
 */
async function fetchRepoContent(projectId, path = '', perPage, branchName = 'development') {
    try {
        // Retrieve the necessary environment variables and construct the base URL
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for fetching the repository tree
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/repository/tree`;

        // Fetch the repository content (tree structure) from GitLab
        const response = await axios.get(gitlabApiUrl, {
            params: {
                path: path,
                per_page: perPage, // Number of items per page
                ref: branchName // Branch name
            },
            headers: {
                'PRIVATE-TOKEN': accessToken // Access token for authentication
            }
        });

        // Store the repository tree, defaulting to an empty array if data is not available
        const repositoryTree = response.data || [];

        // Prepare an array to store all the Axios calls for fetching commit information
        const axiosCalls = repositoryTree.map((tree) =>
            axios.get(`${baseUrl}/projects/${projectId}/repository/commits`, {
                params: {
                    path: tree.path,
                    per_page: 1, // Limit to one commit
                    ref_name: branchName // Branch name
                },
                headers: {
                    'PRIVATE-TOKEN': accessToken // Access token for authentication
                }
            })
        );

        // Wait for all commit information requests to complete
        const commitMessages = await Promise.all(axiosCalls);

        // Create a mapping of file paths to their latest commit messages
        const commitMapObj = commitMessages.reduce((acc, commit) => {
            acc[commit.config.params.path] = { title: commit.data[0]?.title || 'No commit message', committed_date: commit.data[0]?.committed_date };
            return acc;
        }, {});

        // Return the repository tree along with their corresponding commit messages
        return {
            commitMessages: commitMapObj,
            repositoryTree: repositoryTree
        };
    } catch (error) {
        // Handle a 404 error specifically by returning a file_not_exists flag
        if (error.response && error.response.status === constants.resource_not_found) {
            return { file_not_exists: true };
        }
        // Log any other errors that occur
        console.error('Error fetching repository content:', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
}



/**
 * Fetches information about the latest commit in a GitLab repository.
 * 
 * @param {string} project_id - The ID of the project to fetch commit information for.
 * @param {string} [path=''] - The path within the repository to fetch commit information from. Defaults to the root directory.
 * @param {string} [branchName='development'] - The name of the branch to fetch commit information from. Defaults to 'development'.
 * @returns {Promise<Object|null>} - A promise that resolves to the latest commit information or null if no commits are found.
 */
async function fetchCommitInfo(project_id, path = '', per_page, branchName = 'development') {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for fetching commit information
        const gitlabApiUrl = `${baseUrl}/projects/${project_id}/repository/commits`;

        // Make the API request to fetch commit information
        const response = await axios.get(gitlabApiUrl, {
            params: {
                path: path,
                per_page: per_page, // Limit to one commit
                ref_name: branchName // Branch name
            },
            headers: {
                'PRIVATE-TOKEN': accessToken // Access token for authentication
            }
        });

        // Extract the commits array from the response data
        const commits = response.data;

        // Return the latest commit if there are commits, otherwise return null
        return commits.length ? commits[0] : null;

    } catch (error) {
        // Log the error if the API request fails
        console.error('Error fetching commit info:', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
}

/**
 * Fetches the raw content of a file from a GitLab repository.
 * 
 * @param {string} project_id - The ID of the project to fetch file content from.
 * @param {string} path - The path to the file within the repository.
 * @param {string} [branchName='development'] - The name of the branch containing the file. Defaults to 'development'.
 * @returns {Promise<string>} - A promise that resolves to the raw content of the file.
 */
async function fetchFileContent(project_id, path, branchName = 'development') {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for fetching raw file content
        const gitlabApiUrl = `${baseUrl}/projects/${project_id}/repository/files/${encodeURIComponent(path)}/raw`;

        // Make the API request to fetch the raw file content
        const response = await axios.get(gitlabApiUrl, {
            params: {
                ref: branchName // Specify the branch name
            },
            responseType: 'arraybuffer',
            headers: {
                'PRIVATE-TOKEN': accessToken
            }
        });
        // Return the raw content of the file
        return response;
    } catch (error) {
        // Log the error if the API request fails
        console.error('Error fetching file content:', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
}

async function downloadRepoContent(project_id, branchName = 'development') {
    try {
        // Retrieve the necessary environment variables 
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;
        // Construct the GitLab API URL for fetching raw file content
        const gitlabApiUrl = `${baseUrl}/projects/${project_id}/repository/archive.zip`;

        // Make the API request to fetch the raw file content
        const response = await axios.get(gitlabApiUrl, {
            params: {
                sha: branchName // Specify the branch name
            },
            headers: {
                'PRIVATE-TOKEN': accessToken,
                'content-type': 'application/zip'
            },
            responseType: 'stream'
        });
        // Return the raw content of the file
        return response;
    } catch (error) {
        // Log the error if the API request fails
        console.error('Error from function downloadRepoContent:', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
}

/**
 * Checks the availability of a project name on GitLab.
 * 
 * @param {string} projectName - The name of the project to check.
 * @returns {boolean} - True if the project name is available, false otherwise.
 */
async function checkProjectNameAvailability(projectName) {
    // Retrieve the necessary environment variables
    const accessToken = await getGitlabMasterAccessToken();
    const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

    // Construct the GitLab API URL for searching projects
    const gitlabApiUrl = `${baseUrl}/projects/`;

    try {
        // Make the API request to search for projects with the specified name
        const response = await axios.get(gitlabApiUrl, {
            params: {
                search: projectName // Specify the project name to search for
            },
            headers: {
                'PRIVATE-TOKEN': accessToken // Provide the access token for authentication
            }
        });
        return response.data.length === 0;
    } catch (error) {
        // Log the error if the API request fails
        console.error('Error fetching project data:', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
};

async function createNewGitlabProject(projectName, default_branch, initialize_with_readme, visibility = projectVisibility.PRIVATE) {
    // Retrieve the necessary environment variables
    const accessToken = await getGitlabMasterAccessToken();
    const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

    // Construct the GitLab API URL for creating a new project
    const gitlabApiUrl = `${baseUrl}/projects/`;

    try {
        // Prepare the data payload for the API request
        const data = {
            name: projectName,
            default_branch: default_branch,
            initialize_with_readme: initialize_with_readme,
            visibility: visibility
        };
        // Configure the request parameters for axios
        const config = {
            method: 'post',
            url: gitlabApiUrl,
            headers: {
                'PRIVATE-TOKEN': accessToken,
                'Content-Type': 'application/json'
            },
            data: data  // Data object to be sent in the request body
        };

        // Make the API request to create the project
        const response = await axios(config);

        // Extract the created project from the response
        const project = response.data;

        // Commit .gitignore file to GitLab
        try {
            await commitGitIgnoreFile(project.id, default_branch)
        } catch (err) {
            console.error('Error while committing general .gitignore file', error);
        }
        return project;
    } catch (error) {
        // Log the error if the API request fails
        console.error('Error creating new project:', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
}

async function commitGitIgnoreFile(projectId, defaultBranch) {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Step 2: Read .gitignore content from local file
        const gitignoreContent = fs.readFileSync(gitIgnoreFilePath, 'utf-8');
        // Construct the GitLab API URL for creating a new project
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/repository/commits`;
        await axios.post(
            gitlabApiUrl,
            {
                branch: defaultBranch,
                commit_message: 'Added universal .gitignore',
                actions: [
                    {
                        action: 'create',
                        file_path: '.gitignore',
                        content: gitignoreContent
                    }
                ]
            },
            {
                headers: {
                    'PRIVATE-TOKEN': accessToken,
                    'Content-Type': 'application/json'
                },
            }
        );
    } catch (error) {
        // Log the error if the API request fails
        console.error('Error while committing general .gitignore file', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
}

async function createGitLabMergeRequest(projectId, sourceBranch, targetBranch, title, description) {
    try {
        console.log('Create merge request Gitlab helper function');
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for creating a merge request
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/merge_requests`;

        // Send a POST request to the GitLab API to create the merge request
        const response = await axios.post(
            gitlabApiUrl,
            {
                source_branch: sourceBranch,
                target_branch: targetBranch,
                title: title || `Merge ${sourceBranch} into ${targetBranch}`,
                description: description || ''
            },
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );

        // Return the response data from the API
        return response.data;
    } catch (error) {
        // Check if the error response status is 409 (Conflict)
        if (error.response && error.response.status === constants.conflict_code) {
            // Extract the merge request ID from the error message
            const errorMessage = error.response.data.message[0];
            const mergeRequestIdMatch = errorMessage.match(/!([0-9]+)/);

            if (mergeRequestIdMatch) {
                const iid = mergeRequestIdMatch[1];
                return { message: 'Merge request already exists', iid };
            }
        }
        // Rethrow the error to ensure the calling function is aware of the failure
        throw error;
    }
};

async function acceptMergeRequest(projectId, mergeRequestId) {
    return new Promise((resolve, reject) => {
        setTimeout(async () => {
            try {
                console.log('Accept merge request Gitlab helper function');
                // Retrieve the necessary environment variables
                const accessToken = await getGitlabMasterAccessToken();
                const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

                // Construct the GitLab API URL for merging the specified merge request
                const gitlabApiUrl = `${baseUrl}/projects/${projectId}/merge_requests/${mergeRequestId}/merge`;

                // Send a PUT request to the GitLab API to accept the merge request
                const response = await axios.put(
                    gitlabApiUrl,
                    {},
                    {
                        headers: {
                            'Private-Token': accessToken
                        }
                    }
                );
                // Return the response data from the API
                resolve(response.data);

            } catch (error) {
                // Log the error if the API request fails
                // Check if the error response status is 405 (Conflict)
                if (error.response && error.response.status === 405) {
                    const err = new Error('No changes to publish.');
                    err.statusCode = constants.method_not_allowed;
                    resolve({});
                }
                // Rethrow the error to ensure the calling function is aware of the failure
                reject(error);
            }
        }, 2000);

    });
};

async function createGitlabBranch(projectId, branchName, ref) {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for creating a new branch
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/repository/branches`;

        // Make a POST request to the GitLab API to create the new branch
        const response = await axios.post(
            gitlabApiUrl,
            {
                branch: branchName,
                ref: ref
            },
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );
        console.log(`Gitlab function calling for new branch ${branchName}`);
        // Return the response data from the API
        return response.data;
    } catch (error) {
        // Log the error if the API request fails
        console.error('Error creating branch:', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
};

async function protectGitlabBranch(projectId, branchName) {
    try {
        console.log(`Gitlab function calling for protect branch ${branchName}`);
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for creating a new branch
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/protected_branches`;

        const response = await axios.post(
            gitlabApiUrl,
            {
                name: branchName,
                push_access_level: gitlabAccessLevel.MAINTAINER, // Maintainers
                merge_access_level: gitlabAccessLevel.MAINTAINER // Maintainers
            },
            {
                headers: {
                    'PRIVATE-TOKEN': accessToken
                }
            }
        );
        // Return the response data from the API
        return response.data;
    } catch (error) {
        // Log the error if the API request fails
        console.error('Error protect branch:', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
}

async function addMemberToProject(projectId, userId, accessLevel) {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for creating a new branch
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/members`;
        const response = await axios.post(
            gitlabApiUrl,
            {
                user_id: userId,
                access_level: accessLevel
            },
            {
                headers: {
                    'PRIVATE-TOKEN': accessToken
                }
            }
        );
        // Return the response data from the API
        return response.data;
    } catch (error) {
        // Log the error if the API request fails
        console.error('Error add member to project:', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
}

// Function to restrict branch creation to Maintainers and Owners
async function restrictBranchCreation(projectId) {
    try {

        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for creating a new branch
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/protected_branches`;

        const response = await axios.post(gitlabApiUrl, {
            name: '*',
            push_access_level: gitlabAccessLevel.MAINTAINER, // Maintainer access level
            merge_access_level: gitlabAccessLevel.MAINTAINER, // Maintainer access level
            unprotect_access_level: gitlabAccessLevel.MAINTAINER // Maintainer access level
        }, {
            headers: {
                'PRIVATE-TOKEN': accessToken
            }
        });
        // Return the response data from the API
        console.log(`Branch creation restricted to Maintainers and Owners for project: ${projectId}`);
        return response.data;
    } catch (error) {
        console.error(`Error restricting branch creation: ${error.response.data.message}`);
        throw error;
    }
}

// Function to protect the branch with push permissions for MAINTAINER
async function protectBranch(projectId, branchName) {
    try {

        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for creating a new branch
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/protected_branches`;

        const response = await axios.post(gitlabApiUrl, {
            name: branchName,
            push_access_level: gitlabAccessLevel.MAINTAINER, // Developer access level
            merge_access_level: gitlabAccessLevel.MAINTAINER, // Developer access level
            unprotect_access_level: gitlabAccessLevel.MAINTAINER // Maintainer access level
        }, {
            headers: {
                'PRIVATE-TOKEN': accessToken
            }
        });
        console.log(`Branch protected: ${branchName}`);
        // Return the response data from the API
        return response.data;
    } catch (error) {
        console.error(`Error protecting branch: ${error.response.data.message}`);
        throw error;
    }
}

async function fetchBranches(projectId) {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for creating a new branch
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/repository/branches`;

        const response = await axios.get(gitlabApiUrl, {
            headers: {
                'PRIVATE-TOKEN': accessToken
            }
        });
        // Return the response data from the API
        return response.data;
    } catch (error) {
        console.error('Error fetching branches:', error);
        throw error;
    }
}

async function createCommit(projectId, branchName, commitMessage, actions, author_email, author_name, gitlabUserId) {
    // Retrieve the necessary environment variables
    const accessToken = await getUserGitlabAccessToken(gitlabUserId);
    const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

    // Construct the GitLab API URL for creating a new branch
    const gitlabApiUrl = `${baseUrl}/projects/${projectId}/repository/commits`;

    // Commit payload
    const payload = {
        branch: branchName,
        commit_message: commitMessage,
        actions: actions,
        author_name: author_name,
        author_email: author_email
    };

    try {
        const response = await axios.post(
            gitlabApiUrl,
            payload,
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );
        console.log('Commit created:', response.data.id);
        return response.data;
    } catch (err) {
        console.error('error while create commit on Gitlab', err);
        const config = err.response.config;
        const errLogObj = {
            message: err.message,
            method: config.method,
            url: config.url,
            status: err.response.status,
            resData: err.response.data
        };
        const error = new Error(errLogObj?.resData?.message || errLogObj?.message || 'Unknown');
        error.statusCode = errLogObj.status || constants.server_error_code;
        throw error;
    }
}

async function checkUserNameAvailability(username) {
    // Retrieve the necessary environment variables
    const accessToken = await getGitlabMasterAccessToken();
    const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

    // Construct the GitLab API URL
    const gitlabApiUrl = `${baseUrl}/users?username=${username}`;


    try {
        const response = await axios.get(
            gitlabApiUrl,
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );

        return response.data.length === 0 ? true : false;
    } catch (error) {
        console.error('Error while checking username availability:', error);
        throw error;
    }
}

async function fetchProjectDetails(project_id) {
    // Retrieve the necessary environment variables
    const accessToken = await getGitlabMasterAccessToken();
    const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

    // Construct the GitLab API URL
    const gitlabApiUrl = `${baseUrl}/projects/${project_id}?simple=true`;


    try {
        const response = await axios.get(
            gitlabApiUrl,
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );

        return response.data;
    } catch (error) {
        console.error('Error while checking username availability:', error);
        throw error;
    }
}

async function addReadmeFileToRepository(project_id, branchName, customReadme) {
    try {
        // Retrieve the necessary environment variables 
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL
        const gitlabApiUrl = `${baseUrl}/projects/${project_id}/repository/files/README.md`;

        const response = await axios.post(
            gitlabApiUrl,
            {
                branch: branchName, // Default branch
                content: customReadme, // Your custom README content
                commit_message: 'Initial commit'
            },
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );

        return response.data;

    } catch (error) {
        console.error('Error while checking username addReadmeFileToRepository:', error);
        throw error;
    }
}

async function fetchProjectsGraphQl(projectIds) {
    try {
        // / Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const gitlabApiUrl = `${process.env.GIT_LAB_BASE_URL}/api/graphql`;

        // Convert project IDs to GitLab GraphQL global ID format
        const globalIds = projectIds.map((id) => `gid://gitlab/Project/${id}`);

        // GraphQL query to fetch projects by IDs
        const query = `
            query {
                projects(ids: ${JSON.stringify(globalIds)}) {
                    nodes {
                        id
                        name
                        lastActivityAt
                    }
                }
            }`;

        const response = await axios.post(
            gitlabApiUrl,
            { query },
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );

        return response.data.data.projects.nodes;
    } catch (error) {
        console.error('Error fetching fetchProjectsGraphQl', error);
        throw error;
    }
}

async function fileExistsInRepository(project_id, filePath, gitlabBranch) {
    try {
        console.log(`File exists or not checking for path: ${filePath}`);
        // / Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        const gitlabApiUrl = `${baseUrl}/projects/${project_id}/repository`;

        const response = await axios.get(`${gitlabApiUrl}/files/${encodeURIComponent(filePath)}?ref=${gitlabBranch}`, {
            headers: {
                'Private-Token': accessToken
            }
        });
        return response.status === 200;
    } catch (error) {
        if (error.response) {
            if (error.response.status === 404) {
                // File does not exist, so return false
                return false;
            } else if (error.response.status === 400 && error.response.data.message === 'A file with this name doesn\'t exist') {
                // Explicit message check for "file doesn't exist"
                return false;
            }
        }
        throw error;
    }
};

// Function to get all files in the repository for a branch
const getRepositoryFilesRecursively = async (project_id, gitlabBranch) => {
    try {
        // / Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        const gitlabApiUrl = `${baseUrl}/projects/${project_id}/repository`;

        const response = await axios.get(`${gitlabApiUrl}/tree?ref=${gitlabBranch}`, {
            headers: {
                'Private-Token': accessToken
            }
        });
        console.log('response.data', response.data.length);
        return response.data.map((file) => file.path);
    } catch (error) {
        console.error('Error fetching repository files:', error);
        throw error;
    }
};

async function fetchCommitList(project_id, path = '', branchName = 'development', allCommits = [], nextPageUrl) {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // If nextPageUrl is not provided, construct the initial URL for the first request
        const gitlabApiUrl = nextPageUrl || `${baseUrl}/projects/${project_id}/repository/commits`;

        // Define the parameters for the initial API request (used only on the first call)
        const params = nextPageUrl
            ? {} // Empty params for subsequent requests using nextPageUrl
            : {
                pagination: 'keyset',
                per_page: 50,
                order_by: 'created_at',
                sort: 'asc',
                path: path,
                ref_name: branchName // Branch name
            };

        // Make the API request to fetch commit information
        const response = await axios.get(gitlabApiUrl, {
            params: params,
            headers: {
                'PRIVATE-TOKEN': accessToken // Access token for authentication
            }
        });

        // Extract the commits array from the response data
        const commits = response.data;

        // Add the current page of commits to the list of all commits
        allCommits = allCommits.concat(commits);

        // Check if there is a "Link" header for the next page
        const linkHeader = response.headers['link'];

        if (linkHeader) {
            // Extract the URL for the next page from the "Link" header if available
            const nextLinkMatch = linkHeader.match(/<([^>]+)>;\s*rel="next"/);
            if (nextLinkMatch) {
                const nextUrl = nextLinkMatch[1]; // Extract the URL for the next page
                // Recursively fetch the next set of commits using the next page URL
                return fetchCommitList(project_id, path, branchName, allCommits, nextUrl);
            }
        }

        // Return the latest commit if there are commits, otherwise return null
        return allCommits;

    } catch (error) {
        // Log the error if the API request fails
        console.error('Error fetching fetchCommitList', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
}

async function findRepositoryFile(projectId, branch = 'development') {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the URL to fetch the repository tree
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/repository/tree?ref=${branch}&recursive=true&pagination=none`;

        // Make the request to the GitLab API
        const response = await axios.get(gitlabApiUrl, {
            headers: {
                'PRIVATE-TOKEN': accessToken
            }
        });

        // Process and return the filtered results
        return response.data;
    } catch (error) {
        console.error('Error searching files and directories:', error.message);
        throw error;
    }
}

async function downloadFileContent(project_id, path, branchName = 'development') {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for fetching raw file content
        const gitlabApiUrl = `${baseUrl}/projects/${project_id}/repository/files/${encodeURIComponent(path)}/raw`;

        // Make the API request to fetch the raw file content
        const response = await axios.get(gitlabApiUrl, {
            params: {
                ref: branchName // Specify the branch name
            },
            headers: {
                'PRIVATE-TOKEN': accessToken
            },
            responseType: 'stream'
        });

        const fileExtension = path?.split('.').pop().toLowerCase();

        // Attach modified headers to the response object
        if (Object.values(overrideFileExtension).includes(fileExtension)) {
            const filename = path.split('/').pop(); // Extract actual filename
            const contentDisposition = `attachment; filename="${filename}"`;
            response.headers['content-disposition'] = contentDisposition;
        }
        // Return the raw content of the file
        return response;
    } catch (error) {
        // Log the error if the API request fails
        console.error('Error download file content:', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
}

async function fetchCommitDiff(project_id, sha, branchName = 'development') {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for fetching raw file content
        const gitlabApiUrl = `${baseUrl}/projects/${project_id}/repository/commits/${sha}/diff`;

        // Make the API request to fetch the raw file content
        const response = await axios.get(gitlabApiUrl, {
            params: {
                ref: branchName // Specify the branch name
            },
            headers: {
                'PRIVATE-TOKEN': accessToken
            }
        });
        // Return the raw content of the file
        return response.data;
    } catch (error) {
        // Log the error if the API request fails
        console.error('Error fetch commit difference:', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
}

async function fetchProjectLanguages(project_id) {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for fetching raw file content
        const gitlabApiUrl = `${baseUrl}/projects/${project_id}/languages/`;

        // Make the API request to fetch the raw file content
        const response = await axios.get(gitlabApiUrl, {
            headers: {
                'PRIVATE-TOKEN': accessToken
            }
        });
        // Return the raw content of the file
        return response.data;
    } catch (error) {
        // Log the error if the API request fails
        console.error('Error fetch repository languages:', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
}

async function fetchLastCommitDateOnBranch(project_id, branch_name) {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for fetching raw file content
        const gitlabApiUrl = `${baseUrl}/projects/${project_id}/repository/branches/${branch_name}`;

        // Make the API request to fetch the raw file content
        const response = await axios.get(gitlabApiUrl, {
            headers: {
                'PRIVATE-TOKEN': accessToken
            }
        });
        return response.data.commit.committed_date;
    } catch (error) {
        // Log the error if the API request fails
        console.error('Error fetch branch last commit date & time:', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
}

async function fetchRepositoryTreeCommits(project_id, repository_tree, branch_name = 'development') {
    try {
        // Retrieve the necessary environment variables and construct the base URL
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Prepare an array to store all the Axios calls for fetching commit information
        const axiosCalls = repository_tree.map((tree) =>
            axios.get(`${baseUrl}/projects/${project_id}/repository/commits`, {
                params: {
                    path: tree,
                    per_page: 1, // Limit to one commit
                    ref_name: branch_name // Branch name
                },
                headers: {
                    'PRIVATE-TOKEN': accessToken // Access token for authentication
                }
            })
        );

        // Wait for all commit information requests to complete
        const commitMessages = await Promise.all(axiosCalls);

        // Create a mapping of file paths to their latest commit messages
        const commitMapObj = commitMessages.reduce((acc, commit) => {
            acc[commit.config.params.path] = { title: commit.data[0]?.title || 'No commit message', committed_date: commit.data[0]?.committed_date };
            return acc;
        }, {});

        // Return the repository tree along with their corresponding commit messages
        return {
            commitMessages: commitMapObj
        };
    } catch (error) {
        // Log any other errors that occur
        console.error('Error fetching repository content:', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
}

async function fetchProjectLanguagesMultiple(projectIds) {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        const axiosCalls = projectIds.map((project_id) =>

            axios.get(`${baseUrl}/projects/${project_id}/languages/`, {
                headers: {
                    'PRIVATE-TOKEN': accessToken // Access token for authentication
                }
            })
        );

        // Wait for all commit information requests to complete
        const projectLanguages = await Promise.all(axiosCalls);

        const languages = {};

        for (const project of projectLanguages) {
            languages[project.config.url] = project.data;
        }
        // Return the raw content of the file
        return languages;
    } catch (error) {
        // Log the error if the API request fails
        console.error('Error fetch repository languages multiple:', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
}

async function checkBranchDifferences(project_id) {
    try {

        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        const response = await axios.get(`${baseUrl}/projects/${project_id}/repository/compare`, {
            headers: { 'Private-Token': accessToken },
            params: {
                from: gitlabProductionBranch,
                to: gitlabDefaultBranch,
                straight: true
            }
        });

        const { commits } = response.data;

        if (commits && commits.length > 0) {
            console.log(`There are ${commits.length} new commits in the 'development' branch.`);
            return commits.length;
        } else {
            console.log('No new commits to publish.');
            return 0;
        }
    } catch (error) {
        console.error('Error fetching branch comparison:', error);
        return 0;
    }
}

async function checkBranchDifferencesForProjects(projectIds) {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Create an array of promises for each project ID
        const promises = projectIds.map((projectId) =>
            axios.get(`${baseUrl}/projects/${projectId}/repository/compare`, {
                headers: { 'Private-Token': accessToken },
                params: {
                    from: gitlabProductionBranch,
                    to: gitlabDefaultBranch,
                    straight: true
                }
            }).then((response) => ({ projectId, commits: response.data.commits }))
                .catch((error) => ({ projectId, error: error.message }))
        );

        // Wait for all promises to resolve
        const results = await Promise.all(promises);

        results.forEach(({ projectId, commits, error }) => {
            if (error) {
                console.error(`Error fetching branch comparison for project ${projectId}: ${error}`);
            } else if (commits && commits.length > 0) {
                console.log(`Project ${projectId}: There are ${commits.length} new commits in the 'development' branch.`);
            } else {
                console.log(`Project ${projectId}: No new commits to publish.`);
            }
        });

        // Return a summary of commit counts per project
        return results.reduce((acc, { projectId, commits }) => {
            acc[projectId] = commits ? commits.length : 0;
            return acc;
        }, {});

    } catch (error) {
        console.error('Error fetching branch comparisons:', error);
        return {};
    }
}

async function createProjectFork(projectId, project_name, targetBranch, description = '', visibility = projectVisibility.INTERNAL) {
    try {
        console.log('Create project fork Gitlab helper function', projectId);
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for creating a merge request
        const gitlabApiUrl = `${baseUrl}/projects/${encodeURIComponent(projectId)}/fork`;

        // Send a POST request to the GitLab API to create the merge request
        const response = await axios.post(
            gitlabApiUrl,
            {
                name: project_name,
                branches: targetBranch,
                path: '',
                description: description,
                visibility: visibility
            },
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );

        // Return the response data from the API
        return response.data;
    } catch (error) {
        console.log('Error during create project fork', error.response);
        // Rethrow the error to ensure the calling function is aware of the failure
        if (error.response && error.response.status === 409) {
            const err = new Error('The project name is already in use. Please choose a different name.');
            err.statusCode = constants.conflict_code;
            throw err;
        }
        throw error;
    }
};

async function setProjectDefaultBranch(projectId, default_branch) {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for creating a merge request
        const gitlabApiUrl = `${baseUrl}/projects/${encodeURIComponent(projectId)}`;

        // Send a POST request to the GitLab API to create the merge request
        const response = await axios.put(
            gitlabApiUrl,
            {
                default_branch: default_branch
            },
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );

        // Return the response data from the API
        return response.data;
    } catch (error) {
        console.log('Error during set default branch for project', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw error;
    }
};

async function deleteProjectBranch(projectId, branch_name) {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for creating a merge request
        const gitlabApiUrl = `${baseUrl}/projects/${encodeURIComponent(projectId)}/repository/branches/${branch_name}`;

        // Send a POST request to the GitLab API to create the merge request
        const response = await axios.delete(
            gitlabApiUrl,
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );

        // Return the response data from the API
        return response.data;
    } catch (error) {
        console.log('Error during delete project branch', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw error;
    }
};

async function syncProjectFork(forkedProjectId) {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;
        // Construct the API endpoint
        const gitlabApiUrl = `${baseUrl}/projects/${encodeURIComponent(forkedProjectId)}/fork/sync`;

        // Prepare request headers
        const headers = {
            'PRIVATE-TOKEN': accessToken,
            'Content-Type': 'application/json'
        };

        // Make the POST request to sync the fork
        const response = await axios.post(gitlabApiUrl, null, { headers });
        console.log('Sync initiated successfully:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error during fork synchronization:', error.response?.data || error.message);
        throw error;
    }
}

async function checkBranchDifferencesForSyncWithFork(projectIds) {
    if (!Array.isArray(projectIds) || projectIds.length === 0) return {};
    try {
        const results = await Promise.all(
            projectIds.map(async (projectId) => {
                const project = await GitlabRepository.findOne({ project_id: projectId })
                    .populate({ path: 'fork_id', select: 'project_id project_name' })
                    .lean();

                const forkProjectId = project?.fork_id?.project_id;
                if (!forkProjectId) return { projectId, diffCount: 0 };

                const diffs = await getRepositoryAndForkCommitDifference(projectId, forkProjectId);
                return { projectId, diffCount: diffs?.length || 0 };
            })
        );
        return results.reduce((summary, { projectId, diffCount }) => {
            summary[projectId] = diffCount;
            return summary;
        }, {});
    } catch (error) {
        console.error('Error fetching branch comparisons for forked projects:', error);
        return {};
    }
}

async function updateCollaboratorAccess(projectId, userId, accessLevel) {
    try {
        const accessToken = await getGitlabMasterAccessToken();
        const url = `${process.env.GIT_LAB_BASE_URL}/api/v4/projects/${projectId}/members/${userId}`;
        const headers = {
            'PRIVATE-TOKEN': accessToken
        };
        const data = {
            access_level: accessLevel
        };

        const response = await axios.put(url, data, { headers });
        console.log('Collaborator access updated successfully:', response.data);
        return response.data;
    } catch (error) {
        console.error('Failed to update collaborator access:', error.response?.data || error.message);
        throw error;
    }
};

async function removeCollaboratorFromGitlab(projectId, userId) {
    try {
        const accessToken = await getGitlabMasterAccessToken();
        const url = `${process.env.GIT_LAB_BASE_URL}/api/v4/projects/${projectId}/members/${userId}`;
        const headers = {
            'PRIVATE-TOKEN': accessToken
        };

        const response = await axios.delete(url, { headers });
        console.log('Collaborator removed successfully:', response.data);
        return response.data;
    } catch (error) {
        console.error('Failed to remove collaborator:', error.response?.data || error.message);
        throw error;
    }
};

async function createProjectBranch(projectId, branchName, ref = 'development', user_id) {
    try {
        console.log(user_id);
        const accessToken = await getUserGitlabAccessToken(user_id);
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for branch creation
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/repository/branches`;
        console.log(accessToken);
        const response = await axios.post(
            gitlabApiUrl,
            null,
            {
                headers: {
                    'Private-Token': accessToken
                },
                params: {
                    branch: branchName,
                    ref: ref
                }
            }
        );

        // Return the created branch data
        return response.data;
    } catch (error) {
        console.error('Error creating project branch:', {
            projectId,
            branchName,
            ref,
            error: error.response?.data || error.message
        });

        // Enhance error information before rethrowing
        const enhancedError = new Error(`Failed to create branch: ${error.message}`);
        enhancedError.status = error.response?.status || 500;
        enhancedError.details = error.response?.data || {};
        throw enhancedError;
    }
}

async function listMergeRequests(projectId, state = mergeRequestStates.OPENED) {
    try {
        console.log('List merge requests GitLab helper function');

        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/merge_requests`;

        const response = await axios.get(gitlabApiUrl, {
            headers: {
                'Private-Token': accessToken
            },
            params: { state }
        });

        return response.data;
    } catch (error) {
        console.error('Error listing GitLab merge requests:', error.response?.data || error.message);
        throw error;
    }
}

async function getGitlabMergeRequest(projectId, mergeRequestId) {
    try {
        console.log('List merge requests GitLab helper function');

        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/merge_requests/${mergeRequestId}`;

        const response = await axios.get(gitlabApiUrl, {
            headers: {
                'Private-Token': accessToken
            }
        });

        return response.data;
    } catch (error) {
        console.error('Error listing GitLab merge requests:', error.response?.data || error.message);
        throw error;
    }
}

async function addCommentToGitlabMergeRequest(projectId, mergeRequestId, comment) {
    try {
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/merge_requests/${mergeRequestId}/notes`;

        const response = await axios.post(
            gitlabApiUrl,
            {
                body: comment
            },
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );

        return response.data;
    } catch (error) {
        console.error('Error adding comment to GitLab merge request:', error.response?.data || error.message);
        throw error;
    }
}

async function updateProjectDefaultBranch(projectId, default_branch) {
    try {
        console.log(`Set default branch Gitlab helper function => ${projectId}`);
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for creating a merge request
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}`;

        // Send a POST request to the GitLab API to create the merge request
        const response = await axios.put(
            gitlabApiUrl,
            {
                default_branch: default_branch
            },
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );

        // Return the response data from the API
        return response.data;
    } catch (error) {
        console.error('Error while updating default branch of the project:', error.response?.data || error.message);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw error;
    }
};

async function getGitlabMergeRequestChanges(projectId, mergeRequestId) {
    try {
        console.log('List merge requests changes GitLab helper function');

        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/merge_requests/${mergeRequestId}/changes`;

        const response = await axios.get(gitlabApiUrl, {
            headers: {
                'Private-Token': accessToken
            }
        });

        return response.data;
    } catch (error) {
        console.error('Error listing GitLab merge requests changes:', error.response?.data || error.message);
        throw error;
    }
}

async function getGitlabMergeRequestDifferences(projectId, mergeRequestId, page = 1, perPage = 20) {
    console.log('Fetching GitLab merge request differences...');
    try {
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/merge_requests/${mergeRequestId}/diffs?page=${page}&per_page=${perPage}`;

        const response = await axios.get(gitlabApiUrl, {
            headers: {
                'Private-Token': accessToken
            }
        });

        return {
            perPage: Number(response.headers['x-per-page']),
            totalPages: Number(response.headers['x-total-pages']),
            diffs: response.data
        };
    } catch (error) {
        console.error('Failed to fetch GitLab merge request differences:', error.response?.data || error.message);
        throw error;
    }
}

async function getGitlabMergeRequestCommits(projectId, mergeRequestId) {
    try {
        console.log('List merge requests GitLab helper function');

        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/merge_requests/${mergeRequestId}/commits`;

        const response = await axios.get(gitlabApiUrl, {
            headers: {
                'Private-Token': accessToken
            }
        });

        return response.data;
    } catch (error) {
        console.error('Error listing GitLab merge requests commits:', error.response?.data || error.message);
        throw error;
    }
}

async function getGitlabBranchCommitsDifferences(projectId, from, to) {
    console.log('Fetching GitLab merge request differences...');
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/repository/compare`;

        console.log('gitlabApiUrl', gitlabApiUrl);

        const response = await axios.get(gitlabApiUrl, {
            headers: { 'Private-Token': accessToken },
            params: {
                from: from,
                to: to,
                straight: true
            }
        });

        return response.data.commits;
    } catch (error) {
        console.error('Failed to compare branches for commits differences:', error.response?.data || error.message);
        throw error;
    }
}

async function getGitlabBranchFilesDifferences(projectId, from, to) {
    console.log('Fetching compare branch files differences...');
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/repository/compare`;

        console.log('gitlabApiUrl', gitlabApiUrl);

        const response = await axios.get(gitlabApiUrl, {
            headers: { 'Private-Token': accessToken },
            params: {
                from: from,
                to: to,
                straight: true
            }
        });

        return response.data.diffs;
    } catch (error) {
        console.error('Failed to compare branches for files differences:', error.response?.data || error.message);
        throw error;
    }
}

async function closeGitlabMergeRequest(projectId, mergeRequestId) {
    try {
        console.log('Close merge request Gitlab helper function');
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for merging the specified merge request
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/merge_requests/${mergeRequestId}`;

        // Send a PUT request to the GitLab API to accept the merge request
        const response = await axios.put(
            gitlabApiUrl,
            {
                state_event: 'close'
            },
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );
        // Return the response data from the API
        return response.data;
    } catch (error) {
        console.error('Failed to close the open merge request:', error.response?.data || error.message);
        throw error;
    }
};

async function getGitlabMergeRequestActivity(projectId, mergeRequestId, type = mergeRequestActivities.COMMENTS) {
    try {
        console.log('merge request activity Gitlab helper function');
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for merging the specified merge request
        let gitlabApiUrl = '';
        switch (type) {
            case mergeRequestActivities.COMMENTS:
                gitlabApiUrl = `${baseUrl}/projects/${projectId}/merge_requests/${mergeRequestId}/notes`;
                break;
            case mergeRequestActivities.EVENTS:
                gitlabApiUrl = `${baseUrl}/projects/${projectId}/merge_requests/${mergeRequestId}/resource_state_events`;
                break;
            case mergeRequestActivities.COMMITS:
                gitlabApiUrl = `${baseUrl}/projects/${projectId}/merge_requests/${mergeRequestId}/commits`;
                break;
        }
        // Send a PUT request to the GitLab API to accept the merge request
        const response = await axios.get(
            gitlabApiUrl,
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );
        // Return the response data from the API
        return response.data;
    } catch (error) {
        console.error('Failed to fetch merge request activity:', error.response?.data || error.message);
        throw error;
    }
};

async function getGitlabMergeRequestDifferenceFileList(projectId, mergeRequestId) {
    try {
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}/merge_requests/${mergeRequestId}/changes`;

        const response = await axios.get(gitlabApiUrl, {
            headers: {
                'Private-Token': accessToken
            }
        });
        const changes = response.data.changes;
        const changedFiles = changes.map((change) => change.new_path);
        return changedFiles;
    } catch (error) {
        console.error('Failed to fetch GitLab merge request differences:', error.response?.data || error.message);
        throw error;
    }
}

async function fetchGitlabProjectStatistics(projectId) {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for fetch project statistics
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}?statistics=true`;

        // Send a GET request to the GitLab API to fetch project statistics
        const response = await axios.get(
            gitlabApiUrl,
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );
        // Return the response data from the API
        return response.data.statistics;
    } catch (error) {
        console.error('Failed to fetch project specific statistics:', error.response?.data || error.message);
        throw error;
    }
};

async function createGitLabMergeRequestWithFork(targetProjectId, sourceBranch, targetBranch, title, description, sourceProjectId = '') {
    try {
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for creating a merge request
        const gitlabApiUrl = `${baseUrl}/projects/${sourceProjectId}/merge_requests`;

        const payload = {
            source_branch: sourceBranch,
            target_branch: targetBranch,
            title: title || `Merge ${sourceBranch} into ${targetBranch}`,
            description: description || '',
            target_project_id: targetProjectId
        };
        // Send a POST request to the GitLab API to create the merge request
        const response = await axios.post(
            gitlabApiUrl,
            payload,
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );
        // Return the response data from the API
        return { message: 'Sync merge request created successfully.', iid: response.data.iid };
    } catch (error) {
        // Check if the error response status is 409 (Conflict)
        if (error.response && error.response.status === constants.conflict_code) {
            // Extract the merge request ID from the error message
            const errorMessage = error.response.data.message[0];
            const mergeRequestIdMatch = errorMessage.match(/!([0-9]+)/);

            if (mergeRequestIdMatch) {
                const iid = mergeRequestIdMatch[1];
                return { message: 'Sync merge request already exists.', iid };
            }
        }
        // Rethrow the error to ensure the calling function is aware of the failure
        throw error;
    }
};

async function updateProjectVisibility(projectId, visibility) {
    try {
        console.log(`Update project visibility Gitlab helper function => ${projectId}`);
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        // Construct the GitLab API URL for creating a merge request
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}`;

        // Send a POST request to the GitLab API to create the merge request
        const response = await axios.put(
            gitlabApiUrl,
            {
                visibility: visibility
            },
            {
                headers: {
                    'Private-Token': accessToken
                }
            }
        );

        // Return the response data from the API
        return response.data;
    } catch (error) {
        console.error('Error while updating visibility of the project:', error.response?.data || error.message);
        throw error;
    }
};

async function getRepositoryAndForkCommitDifference(fork_id, source_id) {
    try {
        const [forkCommitsRaw, sourceCommitsRaw] = await Promise.all([
            fetchCommitList(fork_id, '', gitlabDefaultBranch),
            fetchCommitList(source_id, '', gitlabProductionBranch)
        ]);
        const forkCommitsSet = new Set(forkCommitsRaw.map(commit => commit.id));
        const missingInFork = sourceCommitsRaw
            .map(commit => commit.id)
            .filter(commitId => !forkCommitsSet.has(commitId));

        return missingInFork;
    } catch (err) {
        console.error(`Error in getRepositoryAndForkCommitDifference: ${err}`);
        throw error;
    }
}

async function deleteGitlabProject(projectId) {
    try {
        console.log(`Gitlab helper function called for delete project => ${projectId}`);
        // Retrieve the necessary environment variables
        const accessToken = await getGitlabMasterAccessToken();
        const baseUrl = `${process.env.GIT_LAB_BASE_URL}/api/v4`;

        console.log("accessToken", accessToken);
        // Construct the GitLab API URL for creating a new project
        const gitlabApiUrl = `${baseUrl}/projects/${projectId}`;

        const config = {
            method: 'delete',
            url: gitlabApiUrl,
            headers: {
                'PRIVATE-TOKEN': accessToken
            }
        };

        // Send the request to the GitLab API to delete project
        const response = await axios(config);

        return response;
    } catch (error) {
        // Log the error if the API request fails
        console.error('Error while deleting gitlab project', error);
        // Rethrow the error to ensure the calling function is aware of the failure
        throw new Error(error);
    }
}

module.exports = {
    createGitLabUser,
    createUserAccessToken,
    fetchRepoContent,
    fetchCommitInfo,
    fetchFileContent,
    downloadRepoContent,
    checkProjectNameAvailability,
    createNewGitlabProject,
    createGitLabMergeRequest,
    acceptMergeRequest,
    createGitlabBranch,
    protectGitlabBranch,
    addMemberToProject,
    restrictBranchCreation,
    protectBranch,
    fetchBranches,
    createCommit,
    checkUserNameAvailability,
    fetchProjectDetails,
    addReadmeFileToRepository,
    fetchProjectsGraphQl,
    fileExistsInRepository,
    getRepositoryFilesRecursively,
    fetchCommitList,
    findRepositoryFile,
    downloadFileContent,
    fetchCommitDiff,
    fetchProjectLanguages,
    fetchLastCommitDateOnBranch,
    fetchRepositoryTreeCommits,
    fetchProjectLanguagesMultiple,
    checkBranchDifferences,
    checkBranchDifferencesForProjects,
    createProjectFork,
    syncProjectFork,
    setProjectDefaultBranch,
    deleteProjectBranch,
    checkBranchDifferencesForSyncWithFork,
    updateCollaboratorAccess,
    removeCollaboratorFromGitlab,
    createProjectBranch,
    addCommentToGitlabMergeRequest,
    listMergeRequests,
    getGitlabMergeRequest,
    updateProjectDefaultBranch,
    getGitlabMergeRequestChanges,
    getGitlabMergeRequestCommits,
    getGitlabMergeRequestDifferences,
    getGitlabBranchCommitsDifferences,
    getGitlabBranchFilesDifferences,
    closeGitlabMergeRequest,
    getGitlabMergeRequestActivity,
    getGitlabMergeRequestDifferenceFileList,
    fetchGitlabProjectStatistics,
    createGitLabMergeRequestWithFork,
    updateProjectVisibility,
    getRepositoryAndForkCommitDifference,
    deleteGitlabProject
};