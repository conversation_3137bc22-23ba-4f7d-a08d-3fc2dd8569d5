<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirect Test</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="form-header">
                <h1>🔄 Redirect Test</h1>
                <p>Testing redirection functionality</p>
            </div>

            <div class="card">
                <h3>Test Redirections</h3>
                
                <div class="button-group">
                    <button onclick="testSignupRedirect()" class="success">Test Signup Redirect</button>
                    <button onclick="testLoginRedirect()" class="secondary">Test Login Redirect</button>
                </div>
                
                <div class="button-group">
                    <button onclick="testPrivateShareRedirect()" class="primary">Test Private Share Redirect</button>
                    <button onclick="clearSession()" class="danger">Clear Session</button>
                </div>
                
                <div id="redirectInfo" style="margin-top: 20px;"></div>
                
                <h3>Current Session Info</h3>
                <div id="sessionInfo"></div>
                
                <h3>URL Parameters</h3>
                <div id="urlParams"></div>
            </div>

            <div class="logs">
                <h3>📋 Test Logs</h3>
                <div id="logContainer"></div>
            </div>
        </div>
    </div>

    <script src="config.js"></script>
    <script>
        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type}] ${message}`);
        }
        
        function testSignupRedirect() {
            const returnUrl = 'private-share/336569f3fa873ab0d831c8a3d45c937cf4786e27a4d328ffc3d7a8fd7deecaf8';
            const redirectUrl = `signup.html?returnUrl=${encodeURIComponent(returnUrl)}`;
            
            logMessage(`Testing signup redirect to: ${redirectUrl}`, 'info');
            
            document.getElementById('redirectInfo').innerHTML = `
                <div class="test-result info">
                    <p><strong>Redirect URL:</strong> ${redirectUrl}</p>
                    <p>Redirecting in 3 seconds...</p>
                    <button onclick="window.location.href='${redirectUrl}'" class="success">Go Now</button>
                </div>
            `;
            
            setTimeout(() => {
                window.location.href = redirectUrl;
            }, 3000);
        }
        
        function testLoginRedirect() {
            const returnUrl = 'private-share/336569f3fa873ab0d831c8a3d45c937cf4786e27a4d328ffc3d7a8fd7deecaf8';
            const redirectUrl = `login.html?returnUrl=${encodeURIComponent(returnUrl)}`;
            
            logMessage(`Testing login redirect to: ${redirectUrl}`, 'info');
            
            document.getElementById('redirectInfo').innerHTML = `
                <div class="test-result info">
                    <p><strong>Redirect URL:</strong> ${redirectUrl}</p>
                    <p>Redirecting in 3 seconds...</p>
                    <button onclick="window.location.href='${redirectUrl}'" class="success">Go Now</button>
                </div>
            `;
            
            setTimeout(() => {
                window.location.href = redirectUrl;
            }, 3000);
        }
        
        function testPrivateShareRedirect() {
            const privateShareUrl = 'private-share/336569f3fa873ab0d831c8a3d45c937cf4786e27a4d328ffc3d7a8fd7deecaf8';
            
            logMessage(`Testing private share redirect to: ${privateShareUrl}`, 'info');
            
            document.getElementById('redirectInfo').innerHTML = `
                <div class="test-result info">
                    <p><strong>Private Share URL:</strong> ${privateShareUrl}</p>
                    <p>Redirecting in 3 seconds...</p>
                    <button onclick="window.location.href='${privateShareUrl}'" class="success">Go Now</button>
                </div>
            `;
            
            setTimeout(() => {
                window.location.href = privateShareUrl;
            }, 3000);
        }
        
        function clearSession() {
            if (typeof clearUserSession === 'function') {
                clearUserSession();
                logMessage('Session cleared', 'success');
            } else {
                localStorage.clear();
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });
                logMessage('Session cleared manually', 'success');
            }
            updateSessionInfo();
        }
        
        function updateSessionInfo() {
            const token = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');
            const userId = localStorage.getItem('userId');
            const userEmail = localStorage.getItem('userEmail');
            
            let sessionHtml = '';
            sessionHtml += `<p><strong>Token:</strong> ${token ? '✅ Present' : '❌ Missing'}</p>`;
            sessionHtml += `<p><strong>User Info:</strong> ${userInfo ? '✅ Present' : '❌ Missing'}</p>`;
            sessionHtml += `<p><strong>User ID:</strong> ${userId || 'Not set'}</p>`;
            sessionHtml += `<p><strong>User Email:</strong> ${userEmail || 'Not set'}</p>`;
            
            if (userInfo) {
                try {
                    const user = JSON.parse(userInfo);
                    sessionHtml += `<p><strong>User Name:</strong> ${user.first_name} ${user.last_name}</p>`;
                } catch (e) {
                    sessionHtml += `<p><strong>Error:</strong> ${e.message}</p>`;
                }
            }
            
            document.getElementById('sessionInfo').innerHTML = sessionHtml;
        }
        
        function updateUrlParams() {
            const params = new URLSearchParams(window.location.search);
            let paramsHtml = '';
            
            if (params.toString()) {
                for (const [key, value] of params) {
                    paramsHtml += `<p><strong>${key}:</strong> ${value}</p>`;
                }
            } else {
                paramsHtml = '<p>No URL parameters</p>';
            }
            
            document.getElementById('urlParams').innerHTML = paramsHtml;
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            logMessage('Redirect test page loaded', 'info');
            updateSessionInfo();
            updateUrlParams();
            
            // Update session info every 5 seconds
            setInterval(updateSessionInfo, 5000);
        });
    </script>
</body>
</html>
