const { componentType } = require("../component.constant")

module.exports = [
    {
        "version": 2,
        "item_type": componentType.REPOSITORY,
        "price": {
            "min": 1,
            "max": 99
        },
        "fees": {
            "author": {
                "percentage": 5
            },
            "buyer": {
                "percentage": 2,
                "fixed_min": 4
            }
        }
    },
    {
        "version": 2,
        "item_type": componentType.MOBILE,
        "price": {
            "min": 1,
            "max": 99
        },
        "fees": {
            "author": {
                "percentage": 5
            },
            "buyer": {
                "percentage": 2,
                "fixed_min": 4
            }
        }
    },
    {
        "version": 2,
        "item_type": componentType.ELEMENTS,
        "price": {
            "min": 1,
            "max": 25
        },
        "fees": {
            "author": {
                "percentage": 5
            },
            "buyer": {
                "percentage": 2,
                "fixed_min": 4
            }
        }
    }
]