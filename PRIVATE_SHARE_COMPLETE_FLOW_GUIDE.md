# Component Private Share - Complete Flow & Testing Guide

## 🔄 **Complete User Flow Visualization**

### **Scenario 1: Sharing a Free Private Component**

```
[Component Owner] 
    ↓
📝 Creates Private Component (component_state: 'private', is_paid: false)
    ↓
🔗 Generates Shareable Link OR 📧 Invites by Email
    ↓
[Recipient] 
    ↓
🔗 Clicks Link/Token → ✅ Direct Access (No Payment Required)
    ↓
📱 Component appears in "Shared with Me" → "Free" section
```

### **Scenario 2: Sharing a Paid Private Component (New User)**

```
[Component Owner]
    ↓
📝 Creates Private Component (component_state: 'private', is_paid: true)
    ↓
🔗 Generates Shareable Link OR 📧 Invites by Email
    ↓
[New Recipient - Not Registered]
    ↓
🔗 Clicks Link → 🚪 Signup Required
    ↓
✅ Signs Up → 🔐 Token Validation
    ↓
💰 Payment Required Page (Component is Paid)
    ↓
💳 Makes Payment → 🔓 Component Unlocked
    ↓
📱 Component appears in "Shared with Me" → "Unlocked" section
```

### **Scenario 3: Sharing a Paid Private Component (Existing User)**

```
[Component Owner]
    ↓
📝 Creates Private Component (component_state: 'private', is_paid: true)
    ↓
🔗 Generates Shareable Link OR 📧 Invites by Email
    ↓
[Existing User - Already Registered]
    ↓
🔗 Clicks Link → 🔐 Token Validation
    ↓
❓ Check: Has user unlocked this component?
    ├── ✅ YES → Direct Access
    └── ❌ NO → 💰 Payment Required Page
                    ↓
                💳 Makes Payment → 🔓 Component Unlocked
                    ↓
                📱 Component moves to "Unlocked" section
```

### **Scenario 4: Managing Shared Components**

```
[Component Owner - Management]
    ↓
📊 Views "My Private Shares" 
    ↓
🔄 Refreshes Shareable Links (New Token Generated)
    ↓
❌ Revokes Individual/Bulk Shares
    ↓
📈 Views Share Analytics

[Recipient - Viewing]
    ↓
📱 Opens "Shared with Me" Section
    ↓
🔘 Toggles: [Free] [Unlocked] [All]
    ↓
📊 Views Statistics: "8 Free, 5 Unlocked, 2 Locked"
    ↓
🔗 Accesses Components Based on Payment Status
```

## 🧪 **Detailed Postman Testing Guide**

### **Prerequisites Setup**

1. **Environment Variables**
```json
{
  "base_url": "http://localhost:3000",
  "auth_token": "your_jwt_token",
  "user_id": "user_object_id",
  "component_id": "component_object_id",
  "share_id": "share_object_id"
}
```

2. **Authentication Header**
```
Cookie: authToken={{auth_token}}
```

---

## 📝 **1. CREATE PRIVATE COMPONENT (Prerequisite)**

**Endpoint:** `POST {{base_url}}/v1/component/create`

**Headers:**
```
Content-Type: application/json
Cookie: authToken={{auth_token}}
```

**Body:**
```json
{
  "title": "My Private Paid Component",
  "short_description": "A premium private component",
  "component_state": "private",
  "is_paid": true,
  "purchase_price": {
    "fiat": 10,
    "mpn_points": 100
  },
  "item_price": {
    "fiat": 8,
    "mpn_points": 80
  },
  "buyer_fee": {
    "fiat": 2,
    "mpn_points": 20
  }
}
```

**Expected Response:**
```json
{
  "status": 200,
  "message": "Component created successfully",
  "data": {
    "_id": "component_id_here",
    "slug": "my-private-paid-component"
  }
}
```

---

## 🔗 **2. GENERATE SHAREABLE LINK**

**Endpoint:** `POST {{base_url}}/v1/component/{{component_id}}/generate-link`

**Headers:**
```
Content-Type: application/json
Cookie: authToken={{auth_token}}
```

**Body:**
```json
{
  "link_name": "Premium Component Share",
  "access_duration": "days",
  "duration_days": 30,
  "access_controls": ["fork", "download"]
}
```

**Expected Response:**
```json
{
  "status": 200,
  "message": "Shareable link generated successfully",
  "data": {
    "success": true,
    "share": {
      "_id": "share_id_here",
      "access_token": "64_char_hex_token",
      "link_name": "Premium Component Share",
      "expires_at": "2024-02-15T10:30:00.000Z"
    },
    "shareableUrl": "http://frontend.com/private-share/64_char_hex_token"
  }
}
```

---

## 📧 **3. SHARE BY EMAIL INVITE**

**Endpoint:** `POST {{base_url}}/v1/component/{{component_id}}/share-privately`

**Headers:**
```
Content-Type: application/json
Cookie: authToken={{auth_token}}
```

**Body:**
```json
{
  "emails": [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
  ],
  "personal_message": "Check out this amazing component I built!",
  "access_duration": "undefined",
  "access_controls": ["copy"]
}
```

**Expected Response:**
```json
{
  "status": 200,
  "message": "Component shared successfully",
  "data": {
    "success": true,
    "shares": [
      {
        "_id": "share_id_1",
        "shared_with_email": "<EMAIL>",
        "access_token": "token_1"
      }
    ],
    "summary": {
      "total": 3,
      "successful": 3,
      "invalid": [],
      "duplicates": []
    }
  }
}
```

---

## 🔐 **4. VALIDATE PRIVATE SHARE TOKEN**

**Endpoint:** `GET {{base_url}}/component/private-share/{{access_token}}/validate`

**Headers:**
```
Cookie: authToken={{auth_token}} (optional for signed-in users)
```

**Expected Response (Existing User):**
```json
{
  "status": 200,
  "message": "Access granted successfully",
  "data": {
    "component": {
      "_id": "component_id",
      "title": "My Private Paid Component",
      "is_paid": true,
      "username": "creator_username"
    },
    "sharedBy": {
      "username": "creator_username",
      "first_name": "Creator"
    },
    "accessType": "private_share",
    "requiresPayment": true
  }
}
```

**Expected Response (New User):**
```json
{
  "status": 200,
  "message": "Please sign up to access this component",
  "data": {
    "requiresSignup": true,
    "signupUrl": "http://frontend.com/auth/signup?returnUrl=private-share%2Ftoken"
  }
}
```

---

## 📱 **5. GET COMPONENT WITH PRIVATE TOKEN**

**Endpoint:** `GET {{base_url}}/component/private/{{component_slug}}?token={{access_token}}`

**Headers:**
```
Cookie: authToken={{auth_token}}
```

**Expected Response (Paid - Not Unlocked):**
```json
{
  "status": 200,
  "message": "Data fetched successfully",
  "data": {
    "title": "My Private Paid Component",
    "slug": "my-private-paid-component",
    "access_type": "private_share_requires_payment",
    "requires_payment": true,
    "payment_info": {
      "purchase_price": {
        "fiat": 10,
        "mpn_points": 100
      }
    },
    "shared_by": {
      "username": "creator_username"
    },
    "personal_message": "Check out this amazing component!"
  }
}
```

**Expected Response (Free or Unlocked):**
```json
{
  "status": 200,
  "message": "Data fetched successfully", 
  "data": {
    "title": "My Private Paid Component",
    "access_type": "private_share",
    "requires_payment": false,
    "platform_data": {
      // Full component data available
    }
  }
}
```

---

## 📋 **6. GET SHARED WITH ME (WITH FILTERS)**

### **Get All Shared Components**
**Endpoint:** `GET {{base_url}}/v1/component/shared-with-me?limit=12&skip=0`

### **Get Only Free Components**
**Endpoint:** `GET {{base_url}}/v1/component/shared-with-me?payment_filter=free&limit=12`

### **Get Only Unlocked Components**
**Endpoint:** `GET {{base_url}}/v1/component/shared-with-me?payment_filter=unlocked&limit=12`

### **Get Only Locked Components**
**Endpoint:** `GET {{base_url}}/v1/component/shared-with-me?payment_filter=locked&limit=12`

**Headers:**
```
Cookie: authToken={{auth_token}}
```

**Expected Response:**
```json
{
  "status": 200,
  "message": "Data fetched successfully",
  "data": {
    "recordsTotal": 25,
    "recordsFiltered": 8,
    "list": [
      {
        "_id": "share_id",
        "status": "accepted",
        "payment_status": "unlocked",
        "is_unlocked": true,
        "component": {
          "title": "Premium Component",
          "slug": "premium-component",
          "is_paid": true,
          "purchase_price": {
            "fiat": 10,
            "mpn_points": 100
          }
        },
        "shared_by_user": {
          "username": "creator_username",
          "first_name": "Creator"
        }
      }
    ]
  }
}
```

---

## 📊 **7. GET SHARED WITH ME STATISTICS**

**Endpoint:** `GET {{base_url}}/v1/component/shared-with-me/statistics`

**Headers:**
```
Cookie: authToken={{auth_token}}
```

**Expected Response:**
```json
{
  "status": 200,
  "message": "Statistics fetched successfully",
  "data": {
    "total": 15,
    "free": 8,
    "unlocked": 5,
    "locked": 2
  }
}
```

---

## 🔄 **8. REFRESH SHAREABLE LINK**

**Endpoint:** `PUT {{base_url}}/v1/component/shareable-link/{{share_id}}/refresh`

**Headers:**
```
Cookie: authToken={{auth_token}}
```

**Expected Response:**
```json
{
  "status": 200,
  "message": "Shareable link refreshed successfully. Previous link is no longer valid.",
  "data": {
    "success": true,
    "share": {
      "_id": "share_id",
      "access_token": "new_64_char_hex_token",
      "updated_at": "2024-01-15T10:30:00.000Z"
    },
    "shareableUrl": "http://frontend.com/private-share/new_64_char_hex_token"
  }
}
```

---

## 📈 **9. GET SHARE ANALYTICS**

**Endpoint:** `GET {{base_url}}/v1/component/share/{{share_id}}/analytics`

**Headers:**
```
Cookie: authToken={{auth_token}}
```

**Expected Response:**
```json
{
  "status": 200,
  "message": "Analytics fetched successfully",
  "data": {
    "_id": "share_id",
    "access_count": 15,
    "created_at": "2024-01-01T10:00:00.000Z",
    "accessed_at": "2024-01-14T15:30:00.000Z",
    "component_id": {
      "title": "Premium Component",
      "slug": "premium-component"
    },
    "analytics": {
      "daysSinceCreated": 14,
      "isExpired": false,
      "daysUntilExpiry": 16,
      "accessRate": "1.07",
      "lastAccessedDaysAgo": 1
    }
  }
}
```

---

## ❌ **10. REVOKE PRIVATE SHARE**

**Endpoint:** `DELETE {{base_url}}/v1/component/private-share/{{share_id}}/revoke`

**Headers:**
```
Cookie: authToken={{auth_token}}
```

**Expected Response:**
```json
{
  "status": 200,
  "message": "Share revoked successfully"
}
```

---

## 📦 **11. BULK REVOKE SHARES**

**Endpoint:** `POST {{base_url}}/v1/component/bulk-revoke-shares`

**Headers:**
```
Content-Type: application/json
Cookie: authToken={{auth_token}}
```

**Body:**
```json
{
  "shareIds": [
    "share_id_1",
    "share_id_2",
    "share_id_3"
  ]
}
```

**Expected Response:**
```json
{
  "status": 200,
  "message": "3 share(s) revoked successfully",
  "data": {
    "success": true,
    "revokedCount": 3
  }
}
```

---

## 📋 **12. GET MY PRIVATE SHARES**

**Endpoint:** `GET {{base_url}}/v1/component/my-private-shares?limit=12&skip=0&status=accepted`

**Headers:**
```
Cookie: authToken={{auth_token}}
```

**Expected Response:**
```json
{
  "status": 200,
  "message": "Data fetched successfully",
  "data": {
    "recordsTotal": 50,
    "recordsFiltered": 25,
    "list": [
      {
        "_id": "share_id",
        "access_type": "by_invite",
        "status": "accepted",
        "shared_with_email": "<EMAIL>",
        "access_count": 5,
        "created_at": "2024-01-01T10:00:00.000Z",
        "component": {
          "title": "My Component",
          "slug": "my-component"
        }
      }
    ]
  }
}
```

---

## 📋 **13. GET MY SHAREABLE LINKS**

**Endpoint:** `GET {{base_url}}/v1/component/my-shareable-links?limit=12&skip=0`

**Headers:**
```
Cookie: authToken={{auth_token}}
```

**Expected Response:**
```json
{
  "status": 200,
  "message": "Data fetched successfully",
  "data": {
    "recordsTotal": 20,
    "recordsFiltered": 20,
    "list": [
      {
        "_id": "share_id",
        "link_name": "Premium Component Share",
        "access_token": "64_char_hex_token",
        "status": "accepted",
        "access_count": 12,
        "created_at": "2024-01-01T10:00:00.000Z",
        "shareableUrl": "http://frontend.com/private-share/64_char_hex_token",
        "component": {
          "title": "Premium Component",
          "slug": "premium-component"
        }
      }
    ]
  }
}
```

---

## 🔍 **Testing Scenarios & Expected Behaviors**

### **Scenario A: Free Component Access**
1. Create free private component (`is_paid: false`)
2. Generate shareable link
3. Access with token → Should get direct access
4. Check "Shared with Me" → Should appear in "Free" filter

### **Scenario B: Paid Component - New User**
1. Create paid private component (`is_paid: true`)
2. Share via email to non-registered email
3. Access link → Should require signup
4. After signup → Should show payment required
5. After payment → Should appear in "Unlocked" section

### **Scenario C: Paid Component - Existing User**
1. Share paid component to existing user
2. Access link → Should show payment required
3. After payment → Should get full access
4. Component should move to "Unlocked" section

### **Scenario D: Link Management**
1. Create shareable link
2. Refresh link → Old token should become invalid
3. Revoke share → Access should be denied
4. Bulk revoke → Multiple shares should be revoked

---

## 🚨 **Common Error Responses**

### **Invalid Token**
```json
{
  "status": 400,
  "message": "Invalid access token format"
}
```

### **Expired Share**
```json
{
  "status": 400,
  "message": "This share has expired"
}
```

### **Component Not Private**
```json
{
  "status": 400,
  "message": "Component must be private to share privately"
}
```

### **Payment Required**
```json
{
  "status": 402,
  "message": "Payment required to access this component"
}
```

### **Unauthorized Access**
```json
{
  "status": 403,
  "message": "You do not have access to this component"
}
```

---

## 🎯 **Frontend Integration Points**

### **Component Access Check**
```javascript
// Check if component requires payment
if (response.data.requires_payment) {
  // Redirect to payment page
  window.location.href = `/payment/${componentSlug}?token=${accessToken}`;
} else {
  // Show component content
  displayComponent(response.data);
}
```

### **Shared With Me Section**
```javascript
// Toggle between filters
const filters = ['all', 'free', 'unlocked', 'locked'];
const currentFilter = 'free';

// API call with filter
fetch(`/v1/component/shared-with-me?payment_filter=${currentFilter}`)
  .then(response => response.json())
  .then(data => displaySharedComponents(data.list));
```

### **Statistics Display**
```javascript
// Get and display statistics
fetch('/v1/component/shared-with-me/statistics')
  .then(response => response.json())
  .then(stats => {
    document.getElementById('free-count').textContent = stats.free;
    document.getElementById('unlocked-count').textContent = stats.unlocked;
    document.getElementById('locked-count').textContent = stats.locked;
  });
```
