# 🔗 Private Share Complete Flow Documentation

## **📋 Overview**

This document explains the complete flow of private share functionality and how it integrates with the existing component and codespace systems.

## **🔄 Complete User Journey**

### **1. Component Discovery & Access**

#### **A. Regular Components (Homepage/Browse)**
```
Frontend Request: GET /api/front/component/homepage/list
↓
Response includes: { is_paid: true/false, ... }
↓
Frontend shows: 
- Free components: Direct access
- Paid components: Unlock button → Payment flow
```

#### **B. Shared Components (Shared With Me)**
```
Frontend Request: POST /api/front/component/shared-with-me
↓
Response includes: { 
  component: { is_paid: true/false, ... },
  payment_status: "free"/"unlocked"/"locked",
  is_unlocked: true/false
}
↓
Frontend shows:
- Free components: Direct access
- Unlocked components: Direct access  
- Locked components: Unlock button → Payment flow
```

### **2. Component Detail Access**

#### **A. Regular Component Access**
```
Frontend Request: GET /api/front/component/get/:slug
↓
Backend checks:
- Component exists & published
- User unlock history (for paid components)
↓
Response: Full component data (locked if paid & not unlocked)
```

#### **B. Private Share Component Access**
```
Frontend Request: GET /api/front/component/private/:slug/details?token=xxx
↓
Backend checks:
- Token validity & expiration
- Component access permissions
- User unlock history (for paid components)
↓
Response: Full component data + private share metadata
```

### **3. Codespace Access**

#### **A. Regular Codespace Access**
```
Frontend Request: GET /api/front/v1/published/code-space/get-details/:repositoryId
↓
Middleware: checkIsAccessible
- Checks if repository is public/published
- Checks component ownership
- Checks unlock history for paid components
↓
Response: Repository details
```

#### **B. Private Share Codespace Access**
```
Frontend Request: GET /api/front/v1/published/code-space/get-details/:repositoryId?token=xxx
↓
Middleware: checkCodeSpaceAccessWithPrivateShare
- Checks regular access first
- If fails, checks private share token
- Validates component unlock for paid components
↓
Response: Repository details + private share context
```

## **🔧 API Endpoints & Flow**

### **1. Component Listing APIs**

#### **Homepage List** ✅ (Already includes `is_paid`)
```javascript
GET /api/front/component/homepage/list
Response: {
  list: [{
    title: "Component Name",
    slug: "component-slug", 
    is_paid: true,
    // ... other fields
  }]
}
```

#### **Shared With Me** ✅ (Now includes `is_paid`)
```javascript
POST /api/front/component/shared-with-me
Response: {
  list: [{
    component: {
      title: "Component Name",
      slug: "component-slug",
      is_paid: true,  // ✅ Now included
      // ... other fields
    },
    payment_status: "free|unlocked|locked",
    is_unlocked: true,
    // ... share metadata
  }]
}
```

### **2. Component Detail APIs**

#### **Regular Component Detail** ✅ (Existing)
```javascript
GET /api/front/component/get/:slug
Response: {
  // Full component data
  // Locked fields if paid & not unlocked
}
```

#### **Private Share Component Detail** ✅ (New)
```javascript
GET /api/front/component/private/:slug/details?token=xxx
Response: {
  // Full component data
  // + private share metadata
  access_type: "private_share",
  shared_by: { username: "...", ... },
  access_controls: ["copy", "download"],
  requires_payment: true/false,
  is_private_share: true
}
```

### **3. Codespace APIs**

#### **Repository Details** ✅ (Enhanced)
```javascript
GET /api/front/v1/published/code-space/get-details/:repositoryId?token=xxx
Middleware: checkCodeSpaceAccessWithPrivateShare
Response: {
  // Repository details
  // Access granted via regular ownership OR private share
}
```

#### **File Tree** ✅ (Enhanced)
```javascript
GET /api/front/v1/published/code-space/get-tree/:repositoryId?token=xxx
Middleware: addPrivateShareContext (lightweight)
Response: {
  // File tree data
  // Context added if private share token valid
}
```

## **🛡️ Security & Access Control**

### **Access Validation Hierarchy**

1. **Component Owner** - Full access always
2. **Public Published** - Access if component is public
3. **Paid Component Unlocked** - Access if user has unlock history
4. **Private Share Token** - Access if valid token provided
5. **Denied** - No access

### **Payment Flow Integration**

```javascript
// Frontend Logic
if (component.is_paid && !is_unlocked) {
  // Show unlock button
  // Redirect to payment flow
  // After payment: redirect back to component
} else {
  // Direct access to component/codespace
}
```

## **🎯 Frontend Integration Points**

### **1. Component Cards (Homepage/Browse)**
```javascript
// Check is_paid field
if (component.is_paid) {
  showUnlockButton();
} else {
  showDirectAccess();
}
```

### **2. Shared With Me Page**
```javascript
// Check payment_status field
switch(share.payment_status) {
  case 'free':
  case 'unlocked':
    showDirectAccess();
    break;
  case 'locked':
    showUnlockButton();
    break;
}
```

### **3. Component Detail Page**
```javascript
// For private shares, include token in all API calls
const token = getTokenFromURL();

// Component details
fetch(`/api/front/component/private/${slug}/details?token=${token}`)

// Codespace APIs  
fetch(`/api/front/v1/published/code-space/get-details/${repoId}?token=${token}`)
fetch(`/api/front/v1/published/code-space/get-tree/${repoId}?token=${token}`)
```

## **🔄 Token Flow**

### **Token Validation Process**
```
1. User clicks private share link
2. Frontend extracts token from URL
3. Validates token: GET /api/front/component/private-share/:token/validate
4. If valid: Access component with token
5. If requires auth: Redirect to login/signup
6. After auth: Return to component with token
```

### **Token Usage in APIs**
```
All private share API calls include: ?token=xxx
- Component details
- Codespace details  
- File tree
- File content
- Downloads
- etc.
```

## **✅ Implementation Status**

### **Completed ✅**
- [x] Private share token validation
- [x] Shared-with-me API includes `is_paid` field
- [x] Private component detail API
- [x] Enhanced codespace middleware for private shares
- [x] Auto-expiration of previous links
- [x] Complete manage shares frontend

### **Frontend Integration Required 🔧**
- [ ] Update shared-with-me page to handle `is_paid` field
- [ ] Add token parameter to codespace API calls
- [ ] Update component detail page for private shares
- [ ] Integrate payment flow for shared paid components

## **🚀 Next Steps**

1. **Frontend Updates**: Update shared-with-me page to show unlock buttons for paid components
2. **Token Integration**: Include token in all codespace API calls for private shares
3. **Payment Integration**: Ensure payment flow works for shared components
4. **Testing**: Test complete flow from share link to codespace access

## **📊 Summary**

The private share functionality now fully integrates with the existing component and codespace systems:

- ✅ **Component listing** includes payment status
- ✅ **Component details** work with private tokens  
- ✅ **Codespace access** validates private shares
- ✅ **Payment flow** integrates seamlessly
- ✅ **Security** maintains proper access control

The frontend can now treat private shared components exactly like regular components, with the addition of token-based access for private shares.
