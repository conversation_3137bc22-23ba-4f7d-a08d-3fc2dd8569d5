// Models declaration
const { UserMpcBalance } = require('../models/user_mpc_balance.model');
const { UserCoinsHistory } = require('../models/user_coins_history.model');
const { UserMpnPointLogs } = require('../models/user_mpn_point_logs.model');
const { AdminMpnPointLogs } = require('../models/admin_mpn_point_logs.model');
const { AdminMpnPointWallet } = require('../models/admin_mpn_point_wallet.model');
const { UserBalanceLocks } = require('../models/user_balance_locks.model');
const { PaymentTransactions } = require('../models/payment_transactions.model');
const { Components } = require('../models/component.model');
const { UserFiatHistory } = require('../models/user_fiat_history.model');
const { AdminFiatWallet } = require('../models/admin_fiat_wallet.model');
const { AdminFiatLogs } = require('../models/admin_fiat_logs.model');

// Service declaration
const { getWelcomeBonusCoinsConfig, getAuthorFeeConfig } = require('../services/settings.service');
const { coinTypes, pointActivity, pointActivityDescription, componentTitle, balanceLockStatus, balanceLockType, transactionStatus, componentCurrency } = require('../config/component.constant');
const constants = require('../config/constants');
const logger = require('../config/logger');

// Npm declaration
const moment = require('moment');
const { v4: uuidv4 } = require('uuid');


/**
 * Adds sign-up bonus to the user's wallet and updates default site settings.
 * @param {string} user_id - The ID of the user.
 */
const addSignUpBonusToWallet = async (user_id) => {
    try {
        // Check if bonus already allotted
        const existingBonus = await UserCoinsHistory.findOne({
            user_id: user_id,
            type: coinTypes.BONUS
        });

        if (existingBonus) {
            console.log(`Bonus already allotted for user_id: ${user_id}`);
            return; // Exit early if bonus already given
        }
        // Retrieve welcome bonus coins configuration   
        const settingsConfig = await getWelcomeBonusCoinsConfig();

        // Get the current date
        const currentDate = moment();

        // Calculate the expiry date by adding the configured duration to the current date
        const expiredOn = currentDate.add(settingsConfig.expiry_duration, 'days');

        // Create user coins history object
        const userCoinsHistoryObj = {
            user_id: user_id,
            points: settingsConfig.bonus_coins,
            pending_points: settingsConfig.bonus_coins,
            type: coinTypes.BONUS,
            expired_on: expiredOn,
            is_expired: false
        };

        // Create user coins history record
        await UserCoinsHistory.create(userCoinsHistoryObj);

        const userPointLogsObj = {
            user_id: user_id,
            points: settingsConfig.bonus_coins,
            activity: pointActivity.EARNING,
            description: pointActivityDescription.SIGNUP_BONUS
        };
        // Create user mpn point logs record
        await UserMpnPointLogs.create(userPointLogsObj);
        // Create MPC balance object
        const mpcBalanceObj = {
            user_id: user_id,
            mpn_points: 0,
            mpn_bonus_points: settingsConfig.bonus_coins,
            fiat: 0
        };
        // Create user MPC balance record
        await UserMpcBalance.create(mpcBalanceObj);
    } catch (error) {
        // Log error if any
        console.error('Error from MPC balance service function addSignUpBonusToWallet', error);
        throw error;
    }
};

const manageComponentPurchase = async (purchaserId, componentId, component, componentType, usePointBalance, useFiatBalance) => {
    try {

        const { mpn_points_applied, fiat_applied, net_payable_fiat } = await calculateComponentUnlock(purchaserId, component, usePointBalance, useFiatBalance);

        if (net_payable_fiat != 0) {
            const err = new Error('User does not have sufficient balance to unlock the component, as the remaining fiat payment is required.');
            err.statusCode = constants.bad_request_code;
            throw err;
        }
        // Extract necessary component details
        const { item_price: itemPrice, buyer_fee: buyerFee, created_by_user: creatorId, mpn_parity: mpnParity } = component;
        // Get author fee configuration (percentage of item price that goes to the author)
        const authorFeePercent = await getAuthorFeeConfig(creatorId, componentType);
        // Calculate how much to credit to admin, creator, and deduct from user
        const { creditToAdmin, creditToCreator, deductFromUser } = await calculatePaymentDistribution(
            itemPrice?.fiat,
            buyerFee?.fiat,
            mpnParity,
            authorFeePercent,
            mpn_points_applied,
            fiat_applied
        );
        // Settle the distribution: deduct from user and credit creator/admin
        await settlePaymentDistribution(
            creatorId,
            purchaserId,
            creditToAdmin,
            creditToCreator,
            deductFromUser,
            componentId,
            componentType // Pass component ID for logging
        );

        return {
            creditToAdmin,
            creditToCreator,
            deductFromUser,
            mpn_points_applied,
            fiat_applied,
            net_payable_fiat
        }
    } catch (error) {
        // Propagate any errors
        throw error;
    }
};


/**
 * Deducts points from the master balance of a user.
 * @param {string} user_id - The ID of the user whose balance is to be deducted.
 * @param {number} points - The number of points to deduct.
 * @param {string} type - The type of points being deducted (e.g., 'BONUS', 'NORMAL').
 */
const deductPointFromMasterBalance = async (user_id, points, type) => {
    try {
        // Determine the key for deduction based on the type of points
        const deductionKey = (type == coinTypes.BONUS) ? 'mpn_bonus_points' : 'mpn_points';

        // Prepare update query
        const updateQuery = {
            $inc: {}
        };

        // Specify the deduction amount for the corresponding key
        updateQuery['$inc'][deductionKey] = parseInt(`-${points}`);

        // Update the user's balance
        await UserMpcBalance.updateOne({ user_id: user_id }, updateQuery);
        console.log(`Deducted ${type} ${points} from master balance for user ${user_id} successfully`);
    } catch (error) {
        // Log and rethrow any errors that occur during the process
        logger.error(`Error while deducting points from master balance${error}`);
        throw error;
    }
};

const deductFiatFromMasterBalance = async (user_id, amount) => {
    try {
        // Update the user's balance
        await UserMpcBalance.updateOne({
            user_id: user_id
        }, {
            $inc: {
                fiat: `-${amount}`
            }
        });
        console.log(`Deducted fiat ${amount} from master balance for user ${user_id} successfully`);
    } catch (error) {
        // Log and rethrow any errors that occur during the process
        logger.error(`Error while deducting fiat from master balance${error}`);
        throw error;
    }
};

const creditFiatToMasterBalance = async (user_id, amount) => {
    try {
        // Update the user's balance
        await UserMpcBalance.updateOne({
            user_id: user_id
        }, {
            $inc: {
                fiat: `${amount}`
            }
        });
        console.log(`Credited fiat ${amount} to master balance for user ${user_id} successfully`);
    } catch (error) {
        // Log and rethrow any errors that occur during the process
        logger.error(`Error while credit fiat from master balance${error}`);
        throw error;
    }
};

const creditPointIntoMasterBalance = async (user_id, points, type) => {
    try {
        // Determine the key for credit based on the type of points
        const creditKey = (type == coinTypes.BONUS) ? 'mpn_bonus_points' : 'mpn_points';

        // Prepare update query
        const updateQuery = {
            $inc: {}
        };

        // Specify the deduction amount for the corresponding key
        updateQuery['$inc'][creditKey] = parseInt(points);

        // Update the user's balance
        await UserMpcBalance.updateOne({ user_id: user_id }, updateQuery);
        console.log(`Credited ${points} in master balance for user ${user_id} successfully`);
    } catch (error) {
        // Log and rethrow any errors that occur during the process
        logger.error(`Error while credit points into master balance ${error}`);
        throw error;
    }
};

const createUserMpnPointLog = async (userId, points, activity, componentId, description, componentType) => {
    try {
        // Replace {{component_type}} in the description with actual componentType value
        const finalDescription = description.replace(/{{component_type}}/g, componentTitle[componentType]);
        const userPointLogsObj = {
            user_id: userId,
            points,
            activity,
            component_id: componentId,
            description: finalDescription
        };
        await UserMpnPointLogs.create(userPointLogsObj);
        console.log(`MPN point log was successfully created for user ${userId} for activity ${activity}.`);
    } catch (error) {
        // Log and rethrow any errors that occur during the process
        logger.error(`Error while create user mpn point log ${error}`);
        throw error;
    }
};

const createUserFiatLog = async (userId, amount, activity, componentId, description, componentType) => {
    try {
        // Replace {{component_type}} in the description with actual componentType value
        const finalDescription = description.replace(/{{component_type}}/g, componentTitle[componentType]);
        const userPointLogsObj = {
            user_id: userId,
            amount,
            activity,
            component_id: componentId,
            description: finalDescription
        };
        await UserFiatHistory.create(userPointLogsObj);
        console.log(`Fiat log was successfully created for user ${userId} for activity ${activity}.`);
    } catch (error) {
        // Log and rethrow any errors that occur during the process
        logger.error(`Error while create user fiat log ${error}`);
        throw error;
    }
};

const createUserCoinHistory = async (userId, points, coinType) => {
    try {
        const creatorCoinsHistory = {
            user_id: userId,
            points: points,
            pending_points: points,
            type: coinType,
            is_expired: false
        };
        await UserCoinsHistory.create(creatorCoinsHistory);
        console.log(`MPN coin history created successfully for user ${userId} with points ${points}.`);
    } catch (error) {
        // Log and rethrow any errors that occur during the process
        logger.error(`Error while credit points into master balance ${error}`);
        throw error;
    }
};

const creditPointToAdminMasterBalance = async (userId, points, componentId) => {
    try {
        // Perform both operations in parallel to optimize execution time
        await Promise.all([
            AdminMpnPointWallet.updateOne({},
                { $inc: { points: points } },
                { upsert: true } // Creates the document if it doesn't exist
            ),
            AdminMpnPointLogs.create({
                user_id: userId,
                points,
                component_id: componentId,
                activity: pointActivity.EARNING
            })
        ]);
    } catch (error) {
        // Enhanced error logging with function parameters for better debugging
        logger.error(`Error while crediting points to admin master balance. User: ${userId}, Points: ${points}, Component: ${componentId}, Error: ${error}`);
        throw error;
    }
};

const creditFiatToAdminMasterBalance = async (userId, amount, componentId) => {
    try {
        // Perform both operations in parallel to optimize execution time
        await Promise.all([
            AdminFiatWallet.updateOne({},
                { $inc: { amount: amount } },
                { upsert: true } // Creates the document if it doesn't exist
            ),
            AdminFiatLogs.create({
                user_id: userId,
                amount,
                component_id: componentId,
                activity: pointActivity.EARNING
            })
        ]);
    } catch (error) {
        // Enhanced error logging with function parameters for better debugging
        logger.error(`Error while crediting points to admin master balance. User: ${userId}, Points: ${points}, Component: ${componentId}, Error: ${error}`);
        throw error;
    }
};


const acceptBonusRequests = async (user_id, mpc_bonus) => {
    try {
        // Retrieve welcome bonus coins configuration from database
        const settingsConfig = await getWelcomeBonusCoinsConfig();

        // Get the current date and time
        const currentDate = moment();

        // Calculate the expiry date for the bonus coins by adding the configured duration to the current date
        const expiredOn = currentDate.add(settingsConfig.expiry_duration, 'days');

        // Prepare an object to store user coins history
        const userCoinsHistoryObj = {
            user_id: user_id,
            points: mpc_bonus,
            pending_points: mpc_bonus,
            type: coinTypes.BONUS,
            expired_on: expiredOn,
            is_expired: false
        };

        // Create a record for user coins history in the database
        await UserCoinsHistory.create(userCoinsHistoryObj);

        // Update the user's MPC balance by adding the bonus coins
        await UserMpcBalance.updateOne({
            user_id: user_id
        }, {
            $inc: {
                mpc_bonus_coins: mpc_bonus
            }
        });
    } catch (error) {
        // If any error occurs during the process, log it and rethrow the error
        logger.error(`Error while accepting bonus requests${error}`);
        throw error;
    }
};

const calculateComponentUnlock = async (purchaserId, component, usePointBalance, useFiatBalance) => {
    try {
        // Normalize flag values
        const usePoint = usePointBalance === true || usePointBalance === 'true';
        const useFiat = useFiatBalance === true || useFiatBalance === 'true';

        const walletBalance = await fetchUserMpnWalletBalance(purchaserId);

        const totalFiatPrice = parseFloat(component?.purchase_price?.fiat) || 0;
        const parity = parseFloat(component?.mpn_parity || 20);

        const formatToTwoDecimals = (n) => parseFloat(n.toFixed(2));

        let mpnPointsUsed = 0; let fiatToApply = 0;

        // === Apply MPN Points if allowed ===
        if (usePoint) {
            const totalPointsAvailable = walletBalance?.total_available_points || 0;
            const pointsAsFiat = totalPointsAvailable / parity;

            const pointsValueToApply = Math.min(pointsAsFiat, totalFiatPrice);
            mpnPointsUsed = pointsValueToApply * parity;
        }

        // Calculate remaining amount after applying points
        const remainingAfterPoints = totalFiatPrice - (mpnPointsUsed / parity);

        // === Apply Fiat Wallet if allowed ===
        if (useFiat) {
            const fiatAvailable = walletBalance?.total_available_fiats || 0;
            fiatToApply = Math.min(fiatAvailable, remainingAfterPoints);
        }

        const netPayableFiat = Math.max(totalFiatPrice - (mpnPointsUsed / parity) - fiatToApply, 0);

        return {
            project_price: component?.purchase_price,
            wallet_balance: walletBalance,
            mpn_points_applied: mpnPointsUsed,
            fiat_applied: formatToTwoDecimals(fiatToApply),
            net_payable_fiat: formatToTwoDecimals(netPayableFiat)
        };

    } catch (error) {
        console.error("Error in calculateComponentUnlock:", error);
        throw error;
    }
};



const calculateAuthorFees = async (creatorId, componentType, itemPrice) => {
    try {
        // Find Author fees 
        const { mpn_points, fiat } = itemPrice;

        // Get author's fee percentage
        const authorFeesPercentage = await getAuthorFeeConfig(creatorId, componentType);

        // Calculate fee amounts
        const mpnPointsFee = (mpn_points * authorFeesPercentage) / 100;
        const fiatFee = (fiat * authorFeesPercentage) / 100;

        // Calculate earnings after fee deduction
        const mpnPointsEarning = mpn_points - mpnPointsFee;
        const fiatEarning = fiat - fiatFee;
        return {
            percentage: authorFeesPercentage,
            mpn_points_fee: mpnPointsFee,
            fiat_fee: fiatFee,
            mpn_points_earning: mpnPointsEarning,
            fiat_earning: fiatEarning
        }
    } catch (error) {
        // If any error occurs during the process, log it and rethrow the error
        logger.error(`Error while calculation author fees${error}`);
        throw error;
    }
}

const fetchUserMpnWalletBalance = async (userId) => {
    try {
        // Retrieve user's MPN balance document
        const userMpcBalance = await UserMpcBalance.findOne({ user_id: userId }).lean();

        // Structure wallet balance with default fallback values
        const walletBalance = {
            fiat: userMpcBalance?.fiat || 0,
            mpn_points: userMpcBalance?.mpn_points || 0,
            mpn_bonus_points: userMpcBalance?.mpn_bonus_points || 0
        };

        // Fetch all locked MPN point entries for the user
        const pointLocks = await UserBalanceLocks.find(
            { user_id: userId, status: balanceLockStatus.LOCKED, lock_type: balanceLockType.POINTS },
            "value"
        );

        // Sum all locked points, defaulting undefined/null value to 0
        const totalLockedPoints = pointLocks.reduce(
            (sum, doc) => sum + (doc.value || 0),
            0
        );

        // Fetch all locked fiat entries for the user
        const fiatLocks = await UserBalanceLocks.find(
            { user_id: userId, status: balanceLockStatus.LOCKED, lock_type: balanceLockType.FIAT },
            "value"
        );

        // Sum all locked fiats, defaulting undefined/null value to 0
        const totalLockedFiats = fiatLocks.reduce(
            (sum, doc) => sum + (doc.value || 0),
            0
        );

        // Calculate total available MPN points (excluding locked points)
        const totalAvailablePoints = (
            walletBalance.mpn_points + walletBalance.mpn_bonus_points
        ) - totalLockedPoints;

        // Calculate total available Fiats (excluding locked fiats)
        const totalAvailableFiats = walletBalance.fiat - totalLockedFiats;

        // Return full wallet data with computed available points
        return {
            fiat: parseFloat(userMpcBalance?.fiat || 0),
            mpn_points: parseFloat(userMpcBalance?.mpn_points || 0),
            mpn_bonus_points: parseFloat(userMpcBalance?.mpn_bonus_points || 0),
            total_available_points: parseFloat(totalAvailablePoints || 0),
            total_locked_points: parseFloat(totalLockedPoints || 0),
            total_locked_fiats: parseFloat(totalLockedFiats || 0),
            total_available_fiats: parseFloat(totalAvailableFiats || 0)
        };
    } catch (error) {
        console.error("Error while fetching user wallet balance:", error);
        throw error; // Re-throw to propagate error upstream
    }
};

const initiateComponentFiatPurchase = async (purchaserId, pointsToUse, fiatToUse, amountToPay, component) => {
    try {
        // Simulate creating an order ID from payment gateway (e.g., Stripe/Razorpay)
        const orderId = await uuidv4();

        // Lock user points for this transaction (if any)
        if (pointsToUse > 0) {
            await UserBalanceLocks.create({
                user_id: purchaserId,
                value: pointsToUse,
                order_id: orderId,
                status: balanceLockStatus.LOCKED,
                lock_type: balanceLockType.POINTS
            });
        }

        // Lock user fiat balance for this transaction (if any)
        if (fiatToUse > 0) {
            await UserBalanceLocks.create({
                user_id: purchaserId,
                value: fiatToUse,
                order_id: orderId,
                status: balanceLockStatus.LOCKED,
                lock_type: balanceLockType.FIAT
            });
        }

        const transaction = await PaymentTransactions.create({
            user_id: purchaserId,
            component_id: component._id,
            points_locked: pointsToUse,
            fiat_locked: fiatToUse,
            total_price: component.purchase_price,
            amount_to_pay: amountToPay,
            currency: componentCurrency.USD,
            order_id: orderId,
            status: transactionStatus.PENDING
        });

        // Return order details to client
        return {
            order_id: orderId,
            amountToPay,
            currency: componentCurrency.USD,
            pointsToUse,
            fiatToUse,
            transactionId: transaction._id
        };

    } catch (error) {
        // Forward error to calling function or global error handler
        throw error;
    }
};

const processComponentFiatPurchase = async (orderId) => {
    try {
        const order = await PaymentTransactions.findOne({
            order_id: orderId
        });

        const component = await Components.findOne({ _id: order.component_id }).lean()

        // Extract necessary component details
        const {
            item_price: itemPrice,
            buyer_fee: buyerFee,
            created_by_user: creatorId,
            mpn_parity: mpnParity,
            component_type: componentType,
            _id: componentId
        } = component;

        // Extract necessary order details
        const { points_locked: pointsLocked, fiat_locked: fiatLocked, amount_to_pay: amountToPay, user_id: purchaserId } = order;
        const totalFiatPaid = fiatLocked + amountToPay;

        // Get author fee configuration (percentage of item price that goes to the author)
        const authorFeePercent = await getAuthorFeeConfig(creatorId, componentType);

        // Calculate how much to credit to admin, creator, and deduct from user
        const {
            creditToAdmin,
            creditToCreator,
            deductFromUser
        } = await calculatePaymentDistribution(
            itemPrice?.fiat,
            buyerFee?.fiat,
            mpnParity,
            authorFeePercent,
            pointsLocked,
            totalFiatPaid
        );

        // Ensure deductFromUser reflects actual fiat locked from wallet
        const actualDeductFromUser = { fiat: fiatLocked, mpn_points: deductFromUser.mpn_points }

        // Settle the distribution: deduct from user and credit creator/admin
        await settlePaymentDistribution(
            creatorId,
            purchaserId,
            creditToAdmin,
            creditToCreator,
            actualDeductFromUser,
            componentId,
            componentType // Pass component ID for logging
        );

        // Mark user's locked balance as used for this order
        await UserBalanceLocks.updateMany(
            { order_id: orderId },
            { $set: { status: balanceLockStatus.USED } }
        );

        // Update order status to reflect completion
        await PaymentTransactions.updateOne(
            { order_id: orderId },
            {
                $set: {
                    is_component_fulfilled: true,
                    fulfilled_at: new Date(),
                    status: transactionStatus.PAID
                }
            }
        );

        return {
            creditToAdmin,
            creditToCreator,
            deductFromUser,
            fiatLocked,
            pointsLocked,
            amountToPay
        }
    } catch (error) {
        // Forward error to calling function or global error handler
        console.error("Error while processing component fiat purchase", error);
        throw error;
    }
};

const calculatePaymentDistribution = async (itemPrice, buyerFees, pointParity, authorFeePercent, pointsLocked, fiatLocked) => {
    try {
        const toCents = val => Math.round(val * 100);
        const fromCents = val => parseFloat((val / 100).toFixed(2));

        // Convert locked points to dollar equivalent
        const pointsAsDollars = pointsLocked / pointParity;
        const totalPaid = fiatLocked + pointsAsDollars;

        // Calculate fee shares
        const authorFee = itemPrice * (authorFeePercent / 100);
        const adminAmount = buyerFees + authorFee;
        const creatorAmount = itemPrice - authorFee;

        // Convert to cents for precision
        const totalCents = toCents(totalPaid);
        const pointsCents = toCents(pointsAsDollars);
        const fiatCents = toCents(fiatLocked);
        const adminCents = toCents(adminAmount);
        const creatorCents = toCents(creatorAmount);

        const pointRatio = pointsCents / totalCents;
        const fiatRatio = fiatCents / totalCents;

        // Admin split
        const adminFiatCents = Math.round(adminCents * fiatRatio);
        const adminPointCents = adminCents - adminFiatCents;
        const adminPoints = Math.round(adminPointCents / 100 * pointParity);

        // Creator split
        const creatorFiatCents = Math.round(creatorCents * fiatRatio);
        const creatorPointCents = creatorCents - creatorFiatCents;
        const creatorPoints = Math.round(creatorPointCents / 100 * pointParity);

        return {
            deductFromUser: {
                fiat: parseFloat(fiatLocked.toFixed(2)),
                mpn_points: pointsLocked
            },
            creditToAdmin: {
                fiat: fromCents(adminFiatCents),
                mpn_points: adminPoints
            },
            creditToCreator: {
                fiat: fromCents(creatorFiatCents),
                mpn_points: creatorPoints
            }
        };
    } catch (error) {
        // Forward error to calling function or global error handler
        console.error("Error from service function calculatePaymentDistribution", error);
        throw error;
    }
}

/**
 * Handles distribution of payments and point deductions between purchaser, creator, and admin.
 * @param {string} creatorId - ID of the content creator.
 * @param {string} purchaserId - ID of the user making the purchase.
 * @param {Object} creditToAdmin - Object containing fiat and/or mpn_points to credit to admin.
 * @param {Object} creditToCreator - Object containing fiat and/or mpn_points to credit to creator.
 * @param {Object} deductFromUser - Object containing fiat and/or mpn_points to deduct from user.
 */
const settlePaymentDistribution = async (creatorId, purchaserId, creditToAdmin, creditToCreator, deductFromUser, componentId, componentType) => {
    try {
        // === Deduct from User ===
        if (deductFromUser) {
            const { fiat, mpn_points } = deductFromUser;

            // Deduct fiat from purchaser's master balance
            if (fiat > 0) {
                await deductFiatFromMasterBalance(purchaserId, fiat);
                await createUserFiatLog(
                    purchaserId,
                    fiat,
                    pointActivity.EXPENSE,
                    componentId,
                    pointActivityDescription.UNLOCK_EXPENSE,
                    componentType
                );
            }

            // Deduct mpn points from user's available points (FIFO-style)
            if (mpn_points > 0) {
                let remainingPoints = mpn_points;

                const userCoins = await UserCoinsHistory.find({
                    user_id: purchaserId,
                    is_expired: false,
                    pending_points: { $gt: 0 }
                }).sort({ type: -1, expired_on: 1 }).lean();

                if (!userCoins.length) {
                    const err = new Error('User has no available point balance.');
                    err.statusCode = constants.payment_required_code;
                    throw err;
                }

                for (const coin of userCoins) {
                    if (remainingPoints === 0) break;

                    const deduction = Math.min(remainingPoints, coin.pending_points);
                    remainingPoints -= deduction;
                    const updatedPending = coin.pending_points - deduction;

                    await deductPointFromMasterBalance(purchaserId, deduction, coin.type);
                    await UserCoinsHistory.updateOne({ _id: coin._id }, { $set: { pending_points: updatedPending } });
                }

                await createUserMpnPointLog(
                    purchaserId,
                    mpn_points,
                    pointActivity.EXPENSE,
                    componentId,
                    pointActivityDescription.UNLOCK_EXPENSE,
                    componentType
                );
            }
        }

        // === Credit to Creator ===
        if (creditToCreator) {
            const { fiat, mpn_points } = creditToCreator;

            if (fiat > 0) {
                await creditFiatToMasterBalance(creatorId, fiat);
                await createUserFiatLog(
                    creatorId,
                    fiat,
                    pointActivity.EARNING,
                    componentId,
                    pointActivityDescription.UNLOCK_EARNING,
                    componentType
                );
            }

            if (mpn_points > 0) {
                await creditPointIntoMasterBalance(creatorId, mpn_points, coinTypes.NORMAL);
                await createUserCoinHistory(creatorId, mpn_points, coinTypes.NORMAL);
                await createUserMpnPointLog(
                    creatorId,
                    mpn_points,
                    pointActivity.EARNING,
                    componentId,
                    pointActivityDescription.UNLOCK_EARNING,
                    componentType
                );
            }
        }

        // === Credit to Admin ===
        if (creditToAdmin) {
            const { fiat, mpn_points } = creditToAdmin;

            if (fiat > 0) {
                await creditFiatToAdminMasterBalance(purchaserId, fiat, componentId);
            }

            if (mpn_points > 0) {
                await creditPointToAdminMasterBalance(purchaserId, mpn_points, componentId);
            }
        }
    } catch (error) {
        console.error("Error in settlePaymentDistribution:", error);
        throw error;
    }
};

module.exports = {
    addSignUpBonusToWallet,
    manageComponentPurchase,
    deductPointFromMasterBalance,
    acceptBonusRequests,
    creditPointIntoMasterBalance,
    createUserMpnPointLog,
    calculateComponentUnlock,
    createUserCoinHistory,
    creditPointToAdminMasterBalance,
    initiateComponentFiatPurchase,
    processComponentFiatPurchase
};