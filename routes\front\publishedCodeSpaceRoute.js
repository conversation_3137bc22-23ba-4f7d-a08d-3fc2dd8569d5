const express = require('express');
const router = express.Router();

const { searchRepositoryFiles, getRepositoryContentRecursively, downloadRepositoryContent, downloadRepositoryFileContent, getRepositoryDetails, getRepositoryBranches, getRepositoryCommitDiff, getRepositoryCommitInfo, getRepositoryFileContent, getRepositoryCommitList, getRepositoryLanguages, getRepositoryTreeCommitDetails, getRepositoryLanguagesCombined, AddStarToRepository, RemoveStarFromRepository, increaseRepositoryViews, fetchRepositoryForksList } = require('../../controller/front/publishedCodeSpace.controller');

const { checkIsAccessible } = require('../../middlewares/validateCodeSpace');
const { checkCodeSpaceAccessWithPrivateShare, addPrivateShareContext } = require('../../middlewares/validatePrivateShareCodeSpace');

router.get('/search-files/:id', checkCodeSpaceAccessWithPrivateShare, searchRepositoryFiles);
router.get('/get-tree/:id', addPrivateShareContext, getRepositoryContentRecursively);
router.get('/get-commits/:id', checkCodeSpaceAccessWithPrivateShare, getRepositoryCommitInfo);
router.get('/get-file-content/:id/:path/raw', checkCodeSpaceAccessWithPrivateShare, getRepositoryFileContent);
router.get('/download/file/:id', checkCodeSpaceAccessWithPrivateShare, downloadRepositoryFileContent);
router.get('/download/:id', checkCodeSpaceAccessWithPrivateShare, downloadRepositoryContent);
router.get('/get-branches/:id', checkCodeSpaceAccessWithPrivateShare, getRepositoryBranches);
router.get('/get-details/:id', checkCodeSpaceAccessWithPrivateShare, getRepositoryDetails);
router.get('/:id/commit/:sha/diff', checkCodeSpaceAccessWithPrivateShare, getRepositoryCommitDiff);
router.get('/get-commits-list/:id', checkCodeSpaceAccessWithPrivateShare, getRepositoryCommitList);
router.get('/get-languages/:id', checkCodeSpaceAccessWithPrivateShare, getRepositoryLanguages);
router.post('/get-tree/commits/:id', getRepositoryTreeCommitDetails);
router.get('/get-languages/combine/:slug', getRepositoryLanguagesCombined);
router.put('/:id/star', AddStarToRepository);
router.put('/:id/star/remove', RemoveStarFromRepository);
router.put('/:id/increase/views', increaseRepositoryViews);
router.post('/:id/fork/list', fetchRepositoryForksList);

module.exports = router;