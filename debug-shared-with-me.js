// Debug script to test shared-with-me security
require('dotenv').config();
const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

const { ComponentPrivateShares } = require('./models/component_private_shares.model');
const { Users } = require('./models/users.model');

async function debugSharedWithMe() {
    try {
        console.log('🔍 Debugging Shared-With-Me Security...\n');
        
        // Get two different users
        const users = await Users.find({ is_verified: true }).limit(2).lean();
        if (users.length < 2) {
            console.log('❌ Need at least 2 verified users to test');
            return;
        }
        
        const userA = users[0];
        const userB = users[1];
        
        console.log(`👤 User A: ${userA.first_name} ${userA.last_name} (${userA.email})`);
        console.log(`👤 User B: ${userB.first_name} ${userB.last_name} (${userB.email})`);
        console.log('');
        
        // Test the exact conditions used in getComponentsSharedWithMe for User A
        console.log('📋 Testing User A conditions:');
        const conditionsA = {
            $and: [
                {
                    $or: [
                        { shared_with_user: userA._id },
                        { 
                            shared_with_email: userA.email,
                            shared_with_user: null
                        }
                    ]
                },
                {
                    is_active: true,
                    status: { $in: ['pending', 'accepted'] },
                    $or: [
                        { expires_at: { $gt: new Date() } },
                        { expires_at: null }
                    ]
                }
            ]
        };
        
        const userAShares = await ComponentPrivateShares.find(conditionsA).lean();
        console.log(`✅ User A has ${userAShares.length} shares`);
        
        if (userAShares.length > 0) {
            console.log('   Sample shares for User A:');
            userAShares.slice(0, 3).forEach((share, index) => {
                console.log(`   ${index + 1}. Type: ${share.access_type}, Status: ${share.status}`);
                console.log(`      Shared with user: ${share.shared_with_user ? 'YES' : 'NO'}`);
                console.log(`      Shared with email: ${share.shared_with_email || 'NONE'}`);
                console.log(`      Token: ${share.access_token ? share.access_token.substring(0, 8) + '...' : 'NONE'}`);
            });
        }
        
        console.log('');
        
        // Test the exact conditions used in getComponentsSharedWithMe for User B
        console.log('📋 Testing User B conditions:');
        const conditionsB = {
            $and: [
                {
                    $or: [
                        { shared_with_user: userB._id },
                        { 
                            shared_with_email: userB.email,
                            shared_with_user: null
                        }
                    ]
                },
                {
                    is_active: true,
                    status: { $in: ['pending', 'accepted'] },
                    $or: [
                        { expires_at: { $gt: new Date() } },
                        { expires_at: null }
                    ]
                }
            ]
        };
        
        const userBShares = await ComponentPrivateShares.find(conditionsB).lean();
        console.log(`✅ User B has ${userBShares.length} shares`);
        
        if (userBShares.length > 0) {
            console.log('   Sample shares for User B:');
            userBShares.slice(0, 3).forEach((share, index) => {
                console.log(`   ${index + 1}. Type: ${share.access_type}, Status: ${share.status}`);
                console.log(`      Shared with user: ${share.shared_with_user ? 'YES' : 'NO'}`);
                console.log(`      Shared with email: ${share.shared_with_email || 'NONE'}`);
                console.log(`      Token: ${share.access_token ? share.access_token.substring(0, 8) + '...' : 'NONE'}`);
            });
        }
        
        console.log('');
        
        // Check for overlap (this should be ZERO)
        const userAShareIds = userAShares.map(s => s._id.toString());
        const userBShareIds = userBShares.map(s => s._id.toString());
        const overlap = userAShareIds.filter(id => userBShareIds.includes(id));
        
        console.log('🔒 Security Check:');
        console.log(`   Overlapping shares: ${overlap.length}`);
        
        if (overlap.length === 0) {
            console.log('   ✅ SECURE: No shared access between users');
        } else {
            console.log('   ❌ SECURITY ISSUE: Users can see each other\'s shares!');
            console.log('   Overlapping share IDs:', overlap);
        }
        
        console.log('');
        
        // Check all shares without user assignment (potential security issue)
        console.log('🔍 Checking unassigned shares:');
        const unassignedShares = await ComponentPrivateShares.find({
            shared_with_user: null,
            shared_with_email: null,
            is_active: true,
            status: { $in: ['pending', 'accepted'] }
        }).lean();
        
        console.log(`   Found ${unassignedShares.length} unassigned shares`);
        
        if (unassignedShares.length > 0) {
            console.log('   ⚠️  These shares are not assigned to any user:');
            unassignedShares.slice(0, 5).forEach((share, index) => {
                console.log(`   ${index + 1}. Type: ${share.access_type}, Status: ${share.status}, Token: ${share.access_token?.substring(0, 8)}...`);
            });
            console.log('   📝 Note: These should only be pending link shares waiting for validation');
        }
        
        console.log('');
        
        // Check status distribution
        console.log('📊 Share Status Distribution:');
        const statusCounts = await ComponentPrivateShares.aggregate([
            { $match: { is_active: true } },
            { $group: { _id: '$status', count: { $sum: 1 } } },
            { $sort: { count: -1 } }
        ]);
        
        statusCounts.forEach(status => {
            console.log(`   ${status._id}: ${status.count} shares`);
        });
        
        console.log('\n🎉 Debug completed!');
        
    } catch (error) {
        console.error('❌ Error during debug:', error);
    } finally {
        mongoose.connection.close();
        console.log('\n✅ Database connection closed');
    }
}

debugSharedWithMe();
