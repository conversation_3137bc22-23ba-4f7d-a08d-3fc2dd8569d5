<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Access Token</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="form-header">
                <h1>🔑 Test Access Token Fix</h1>
                <p>Testing if access_token is properly returned in shared components</p>
            </div>

            <div class="card">
                <h3>Access Token Test</h3>
                <div class="button-group">
                    <button onclick="testAccessTokens()" class="success">Test Access Tokens</button>
                    <button onclick="clearLogs()" class="secondary">Clear Logs</button>
                </div>
                
                <div id="results" style="margin-top: 20px;"></div>
            </div>

            <div class="logs">
                <h3>📋 Test Results</h3>
                <div id="logContainer"></div>
            </div>
        </div>
    </div>

    <script src="config.js"></script>
    <script>
        const API_BASE_URL = 'http://localhost:7898/api/front';
        
        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type}] ${message}`);
        }
        
        function showResult(title, success, data) {
            const resultsDiv = document.getElementById('results');
            const resultClass = success ? 'success' : 'error';
            const icon = success ? '✅' : '❌';
            
            const resultHtml = `
                <div class="test-result ${resultClass}" style="margin: 10px 0; padding: 15px; border-radius: 5px; background: ${success ? '#f0fff4' : '#fed7d7'};">
                    <h4>${icon} ${title}</h4>
                    <pre style="background: #f7fafc; padding: 10px; border-radius: 5px; overflow-x: auto; white-space: pre-wrap;">${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
            
            resultsDiv.innerHTML += resultHtml;
        }
        
        async function testAccessTokens() {
            logMessage('Testing access tokens in shared components...', 'info');
            
            const authToken = localStorage.getItem('authToken');
            if (!authToken) {
                showResult('Authentication Check', false, { error: 'No auth token found. Please login first.' });
                return;
            }
            
            try {
                const requestBody = {
                    skip: 0,
                    limit: 5
                };

                const response = await fetch(`${API_BASE_URL}/v1/component/shared-with-me`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`,
                        'token': authToken
                    },
                    credentials: 'include',
                    body: JSON.stringify(requestBody)
                });
                
                logMessage(`Response status: ${response.status}`, 'info');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                logMessage(`API Response received`, 'info');
                
                if (data.status === 200) {
                    const components = data.data.list || [];
                    logMessage(`Found ${components.length} shared components`, 'info');
                    
                    if (components.length === 0) {
                        showResult('Access Token Test', false, { 
                            message: 'No shared components found to test',
                            suggestion: 'Create some shared components first'
                        });
                        return;
                    }
                    
                    // Test each component for access_token
                    const tokenResults = components.map((share, index) => {
                        const hasToken = !!share.access_token;
                        const tokenLength = share.access_token ? share.access_token.length : 0;
                        
                        logMessage(`Component ${index + 1}: ${share.component?.title || 'Unknown'}`, 'info');
                        logMessage(`- Access token: ${hasToken ? 'Present' : 'MISSING'}`, hasToken ? 'success' : 'error');
                        logMessage(`- Token length: ${tokenLength}`, 'info');
                        logMessage(`- Access type: ${share.access_type}`, 'info');
                        
                        return {
                            componentTitle: share.component?.title || 'Unknown',
                            componentSlug: share.component?.slug || 'unknown',
                            hasAccessToken: hasToken,
                            accessTokenLength: tokenLength,
                            accessToken: share.access_token ? `${share.access_token.substring(0, 8)}...` : null,
                            accessType: share.access_type,
                            status: share.status
                        };
                    });
                    
                    const allHaveTokens = tokenResults.every(result => result.hasAccessToken);
                    const missingTokens = tokenResults.filter(result => !result.hasAccessToken);
                    
                    showResult('Access Token Test', allHaveTokens, {
                        totalComponents: components.length,
                        componentsWithTokens: tokenResults.filter(r => r.hasAccessToken).length,
                        componentsWithoutTokens: missingTokens.length,
                        allHaveTokens: allHaveTokens,
                        tokenResults: tokenResults,
                        missingTokenComponents: missingTokens.map(r => r.componentTitle)
                    });
                    
                    if (allHaveTokens) {
                        logMessage('✅ All components have access tokens!', 'success');
                    } else {
                        logMessage(`❌ ${missingTokens.length} components missing access tokens`, 'error');
                    }
                    
                } else {
                    showResult('Access Token Test', false, data);
                }
            } catch (error) {
                logMessage(`Test error: ${error.message}`, 'error');
                showResult('Access Token Test', false, { error: error.message, stack: error.stack });
            }
        }
        
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            document.getElementById('results').innerHTML = '';
        }
        
        // Show auth status on load
        document.addEventListener('DOMContentLoaded', function() {
            const authToken = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');
            
            logMessage(`Auth token: ${authToken ? 'Present' : 'Missing'}`, authToken ? 'success' : 'warning');
            
            if (authToken && userInfo) {
                const user = JSON.parse(userInfo);
                logMessage(`Logged in as: ${user.first_name} ${user.last_name}`, 'info');
                logMessage('Ready to test access tokens', 'success');
            } else {
                logMessage('Please login first to test access tokens', 'warning');
            }
        });
    </script>
</body>
</html>
