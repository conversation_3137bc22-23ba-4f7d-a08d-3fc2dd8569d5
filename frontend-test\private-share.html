<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Private Share Access - Private Share Test</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="form-header">
                <h1>🔗 Private Share Access</h1>
                <p>Accessing shared component...</p>
            </div>

            <div class="card">
                <div id="loadingStep">
                    <div style="text-align: center; padding: 40px;">
                        <div class="loading" style="width: 40px; height: 40px; margin: 0 auto 20px;"></div>
                        <p>Validating access token...</p>
                    </div>
                </div>

                <div id="authRequiredStep" style="display: none;">
                    <div class="access-type requires-payment">
                        ⚠️ Authentication Required
                    </div>
                    <p>This shared component requires you to be logged in to access it.</p>
                    <div class="button-group">
                        <button onclick="redirectToLogin()" class="success">🔑 Login</button>
                        <button onclick="redirectToSignup()" class="secondary">📝 Sign Up</button>
                    </div>
                </div>

                <div id="paymentRequiredStep" style="display: none;">
                    <div class="access-type requires-payment">
                        💳 Payment Required
                    </div>
                    <div id="componentInfo"></div>
                    <div id="paymentInfo"></div>
                    <div class="button-group">
                        <button onclick="proceedToPayment()" class="success">💳 Purchase Component</button>
                        <button onclick="goToSharedWithMe()" class="secondary">📥 View in Shared with Me</button>
                    </div>
                </div>

                <div id="accessGrantedStep" style="display: none;">
                    <div id="accessTypeIndicator"></div>
                    <div id="componentDetails"></div>
                    <div id="shareDetails"></div>
                    <div class="button-group">
                        <button onclick="viewComponent()" class="success">👁️ View Component</button>
                        <button onclick="goToSharedWithMe()" class="secondary">📥 Go to Shared with Me</button>
                    </div>
                </div>

                <div id="errorStep" style="display: none;">
                    <div class="access-type requires-payment">
                        ❌ Access Denied
                    </div>
                    <div id="errorMessage"></div>
                    <div class="button-group">
                        <button onclick="window.location.href='index.html'" class="secondary">🏠 Go Home</button>
                        <button onclick="window.location.href='login.html'" class="success">🔑 Try Login</button>
                    </div>
                </div>

                <div class="navigation-links" style="margin-top: 20px; text-align: center;">
                    <p><a href="index.html" style="color: #667eea; text-decoration: none;">← Back to Home</a></p>
                </div>
            </div>

            <div class="logs">
                <h3>📋 Access Logs</h3>
                <div id="logContainer"></div>
                <button onclick="clearLogs()">🗑️ Clear Logs</button>
            </div>
        </div>
    </div>

    <script src="config.js"></script>
    <script>
        let accessToken = null;
        let componentData = null;
        let shareData = null;

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, checking if config.js loaded...');

            // Check if config.js loaded properly
            if (typeof window.utils === 'undefined') {
                console.error('Config.js not loaded properly');
                logMessage = function(msg, type) { console.log(`[${type}] ${msg}`); };
                showError = function(msg) {
                    document.getElementById('errorMessage').innerHTML = `<p>${msg}</p>`;
                    hideAllSteps();
                    document.getElementById('errorStep').style.display = 'block';
                };
                hideAllSteps = function() {
                    document.getElementById('loadingStep').style.display = 'none';
                    document.getElementById('authRequiredStep').style.display = 'none';
                    document.getElementById('paymentRequiredStep').style.display = 'none';
                    document.getElementById('accessGrantedStep').style.display = 'none';
                    document.getElementById('errorStep').style.display = 'none';
                };
                getUrlParams = function() {
                    const params = new URLSearchParams(window.location.search);
                    const result = {};
                    for (const [key, value] of params) {
                        result[key] = value;
                    }
                    return result;
                };
                formatDate = function(dateString) {
                    return new Date(dateString).toLocaleDateString();
                };
                handleError = function(error, context) {
                    console.error(`Error in ${context}:`, error);
                    logMessage(`Error in ${context}: ${error.message}`, 'error');
                };
                apiCall = async function(endpoint, options = {}) {
                    const url = `${window.API_BASE_URL}${endpoint}`;
                    const response = await fetch(url, {
                        ...options,
                        headers: {
                            'Content-Type': 'application/json',
                            ...options.headers
                        }
                    });
                    const data = await response.json();
                    return { response, data };
                };
            } else {
                console.log('Config.js loaded successfully');
                window.utils.initializePage();
            }
            
            // Extract token from URL path
            const pathParts = window.location.pathname.split('/');
            const tokenIndex = pathParts.indexOf('private-share') + 1;
            
            if (tokenIndex > 0 && pathParts[tokenIndex]) {
                accessToken = pathParts[tokenIndex];
                logMessage(`Extracted token from URL: ${accessToken}`, 'info');
                validateAccess();
            } else {
                // Try to get token from URL params as fallback
                const params = getUrlParams();
                if (params.token) {
                    accessToken = params.token;
                    logMessage(`Using token from URL params: ${accessToken}`, 'info');
                    validateAccess();
                } else {
                    showError('No access token provided in URL');
                }
            }
        });

        async function validateAccess() {
            if (!accessToken) {
                showError('No access token provided');
                return;
            }

            try {
                logMessage('Validating access token...', 'info');
                
                const { response, data } = await apiCall(`/component/private-share/${accessToken}/validate`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (data.status === 200) {
                    shareData = data.data;
                    logMessage('Token validation successful', 'success');
                    
                    if (shareData.requiresSignup) {
                        showAuthRequired();
                    } else {
                        // Token is valid, now try to access the component
                        await accessComponent();
                    }
                } else if (data.status === 401) {
                    showAuthRequired();
                } else {
                    showError(data.message || 'Invalid access token');
                }
            } catch (error) {
                handleError(error, 'validating access token');
                showError('Failed to validate access token');
            }
        }

        async function accessComponent() {
            if (!shareData || !shareData.component) {
                showError('Component information not available');
                return;
            }

            const componentSlug = shareData.component.slug;
            
            try {
                logMessage(`Accessing component: ${componentSlug}`, 'info');
                
                const { response, data } = await apiCall(`/component/private/${componentSlug}?token=${accessToken}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (data.status === 200) {
                    componentData = data.data;
                    logMessage('Component access successful', 'success');
                    
                    if (componentData.requires_payment) {
                        showPaymentRequired();
                    } else {
                        showAccessGranted();
                    }
                } else {
                    showError(data.message || 'Failed to access component');
                }
            } catch (error) {
                handleError(error, 'accessing component');
                showError('Failed to access component');
            }
        }

        function showAuthRequired() {
            hideAllSteps();
            document.getElementById('authRequiredStep').style.display = 'block';
        }

        function showPaymentRequired() {
            hideAllSteps();
            
            // Show component info
            const componentInfo = document.getElementById('componentInfo');
            componentInfo.innerHTML = `
                <h3>${componentData.title}</h3>
                <p>${componentData.description || 'No description available'}</p>
            `;
            
            // Show payment info
            const paymentInfo = document.getElementById('paymentInfo');
            if (componentData.payment_info) {
                paymentInfo.innerHTML = `
                    <div class="payment-details">
                        <h4>💰 Payment Details</h4>
                        <p><strong>Price:</strong> $${componentData.payment_info.item_price}</p>
                        <p><strong>Buyer Fee:</strong> $${componentData.payment_info.buyer_fee}</p>
                        <p><strong>Total:</strong> $${componentData.payment_info.purchase_price}</p>
                    </div>
                `;
            }
            
            document.getElementById('paymentRequiredStep').style.display = 'block';
        }

        function showAccessGranted() {
            hideAllSteps();
            
            // Show access type
            const accessTypeIndicator = document.getElementById('accessTypeIndicator');
            let accessTypeClass = 'public-link';
            let accessTypeText = '🔗 Public Link Access';
            
            if (componentData.share_type === 'private_invite') {
                accessTypeClass = 'private-invite';
                accessTypeText = '📧 Private Invitation';
            }
            
            accessTypeIndicator.innerHTML = `<div class="access-type ${accessTypeClass}">${accessTypeText}</div>`;
            
            // Show component details
            const componentDetails = document.getElementById('componentDetails');
            componentDetails.innerHTML = `
                <h3>${componentData.title}</h3>
                <p>${componentData.description || 'No description available'}</p>
                <div class="component-meta">
                    <span>Type: ${componentData.component_type || 'Component'}</span>
                    <span>Created: ${formatDate(componentData.created_at)}</span>
                </div>
            `;
            
            // Show share details
            const shareDetails = document.getElementById('shareDetails');
            if (componentData.shared_by) {
                shareDetails.innerHTML = `
                    <div class="share-info">
                        <h4>📤 Shared by: ${componentData.shared_by.first_name} ${componentData.shared_by.last_name}</h4>
                        ${componentData.personal_message ? `<p><em>"${componentData.personal_message}"</em></p>` : ''}
                        <p><strong>Access Controls:</strong> ${componentData.access_controls?.join(', ') || 'None specified'}</p>
                    </div>
                `;
            }
            
            document.getElementById('accessGrantedStep').style.display = 'block';
        }

        function showError(message) {
            hideAllSteps();
            document.getElementById('errorMessage').innerHTML = `<p>${message}</p>`;
            document.getElementById('errorStep').style.display = 'block';
        }

        function hideAllSteps() {
            document.getElementById('loadingStep').style.display = 'none';
            document.getElementById('authRequiredStep').style.display = 'none';
            document.getElementById('paymentRequiredStep').style.display = 'none';
            document.getElementById('accessGrantedStep').style.display = 'none';
            document.getElementById('errorStep').style.display = 'none';
        }

        function redirectToLogin() {
            const returnUrl = `private-share/${accessToken}`;
            window.location.href = `login.html?returnUrl=${encodeURIComponent(returnUrl)}`;
        }

        function redirectToSignup() {
            const returnUrl = `private-share/${accessToken}`;
            window.location.href = `signup.html?returnUrl=${encodeURIComponent(returnUrl)}`;
        }

        function viewComponent() {
            if (componentData && componentData.slug) {
                window.location.href = `component-view.html?slug=${componentData.slug}&token=${accessToken}`;
            } else {
                logMessage('Component data not available', 'error');
            }
        }

        function goToSharedWithMe() {
            window.location.href = 'shared-with-me.html';
        }

        function proceedToPayment() {
            if (componentData && componentData.slug) {
                window.location.href = `payment.html?component=${componentData.slug}&token=${accessToken}`;
            } else {
                logMessage('Component data not available for payment', 'error');
            }
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }
    </script>
</body>
</html>
