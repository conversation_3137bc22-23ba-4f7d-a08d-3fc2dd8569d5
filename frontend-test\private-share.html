<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Private Share Access - Private Share Test</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="form-header">
                <h1>🔗 Private Share Access</h1>
                <p>Accessing shared component...</p>
            </div>

            <div class="card">
                <div id="loadingStep">
                    <div style="text-align: center; padding: 40px;">
                        <div class="loading" style="width: 40px; height: 40px; margin: 0 auto 20px;"></div>
                        <p>Validating access token...</p>
                    </div>
                </div>

                <div id="authRequiredStep" style="display: none;">
                    <div class="access-type requires-payment">
                        ⚠️ Authentication Required
                    </div>
                    <div class="auth-message">
                        <p>This shared component requires you to be logged in to access it.</p>
                    </div>
                    <div class="button-group">
                        <button onclick="redirectToLogin()" class="success">🔑 Login</button>
                        <button onclick="redirectToSignup()" class="secondary">📝 Sign Up</button>
                    </div>
                </div>

                <div id="paymentRequiredStep" style="display: none;">
                    <div class="access-type requires-payment">
                        💳 Payment Required
                    </div>
                    <div id="componentInfo"></div>
                    <div id="paymentInfo"></div>
                    <div class="button-group">
                        <button onclick="proceedToPayment()" class="success">💳 Purchase Component</button>
                        <button onclick="goToSharedWithMe()" class="secondary">📥 View in Shared with Me</button>
                    </div>
                </div>

                <div id="accessGrantedStep" style="display: none;">
                    <div id="accessTypeIndicator"></div>
                    <div id="componentDetails"></div>
                    <div id="shareDetails"></div>
                    <div class="button-group">
                        <button onclick="viewComponent()" class="success">👁️ View Component</button>
                        <button onclick="goToSharedWithMe()" class="secondary">📥 Go to Shared with Me</button>
                    </div>
                </div>

                <div id="errorStep" style="display: none;">
                    <div class="access-type requires-payment">
                        ❌ Access Denied
                    </div>
                    <div id="errorMessage"></div>
                    <div class="button-group">
                        <button onclick="window.location.href='index.html'" class="secondary">🏠 Go Home</button>
                        <button onclick="window.location.href='login.html'" class="success">🔑 Try Login</button>
                    </div>
                </div>

                <div class="navigation-links" style="margin-top: 20px; text-align: center;">
                    <p><a href="index.html" style="color: #667eea; text-decoration: none;">← Back to Home</a></p>
                </div>
            </div>

            <div class="logs">
                <h3>📋 Access Logs</h3>
                <div id="logContainer"></div>
                <button onclick="clearLogs()">🗑️ Clear Logs</button>
            </div>
        </div>
    </div>

    <script src="config.js"></script>
    <script>
        let accessToken = null;
        let componentData = null;
        let shareData = null;
        const {apiCall, logMessage, handleError, getUrlParams, getCurrentUser, checkAuthenticationStatus } = window.utils;

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing private share page...');

            // Initialize page utilities
            if (typeof window.utils !== 'undefined') {
                window.utils.initializePage();
            }

            // Check authentication status
            checkAuthenticationStatus();

            // Extract token from URL path
            const pathParts = window.location.pathname.split('/');
            const tokenIndex = pathParts.indexOf('private-share') + 1;

            if (tokenIndex > 0 && pathParts[tokenIndex]) {
                accessToken = pathParts[tokenIndex];
                logMessage(`Extracted token from URL: ${accessToken}`, 'info');
                validateAccess();
            } else {
                // Try to get token from URL params as fallback
                const params = getUrlParams();
                if (params.token) {
                    accessToken = params.token;
                    logMessage(`Using token from URL params: ${accessToken}`, 'info');
                    validateAccess();
                } else {
                    showError('No access token provided in URL');
                }
            }
        });

        function checkAuthenticationStatus() {
            const token = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');

            if (token && userInfo) {
                const user = JSON.parse(userInfo);
                logMessage(`User authenticated: ${user.first_name} ${user.last_name} (${user.email})`, 'success');
                logMessage(`User ID: ${user._id}`, 'info');

                // Ensure session data is properly set
                if (user._id) localStorage.setItem('userId', user._id);
                if (user.email) localStorage.setItem('userEmail', user.email);

                return true;
            } else {
                logMessage('User not authenticated', 'warning');
                return false;
            }
        }

        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            const result = {};
            for (const [key, value] of params) {
                result[key] = value;
            }
            return result;
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            if (logContainer) {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry ${type}`;
                logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
            }
            console.log(`[${type}] ${message}`);
        }

        function handleError(error, context = '') {
            console.error(`Error in ${context}:`, error);
            logMessage(`Error in ${context}: ${error.message}`, 'error');
        }

        async function validateAccess() {
            if (!accessToken) {
                showError('No access token provided');
                return;
            }

            try {
                logMessage('Validating access token...', 'info');

                // Get current authentication status
                const isAuthenticated = checkAuthenticationStatus();
                const userInfo = getCurrentUser();

                logMessage(`Authentication status: ${isAuthenticated}`, 'info');
                if (userInfo) {
                    logMessage(`Current user: ${userInfo.email} (ID: ${userInfo._id})`, 'info');
                }

                const { response, data } = await apiCall(`/component/private-share/${accessToken}/validate`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                        'Content-Type': 'application/json'
                    }
                });

                logMessage(`Validation response status: ${data.status}`, 'info');
                logMessage(`Response data: ${JSON.stringify(data.data, null, 2)}`, 'info');

                if (data.status === 200) {
                    shareData = data.data;
                    logMessage('Token validation successful', 'success');

                    // Handle authentication requirements
                    if (shareData.requiresSignup) {
                        logMessage('Signup required - redirecting...', 'warning');
                        handleAuthRequired('signup');
                    } else if (shareData.requiresLogin) {
                        logMessage('Login required - redirecting...', 'warning');
                        handleAuthRequired('login');
                    } else if (shareData.authRequired) {
                        logMessage('Authentication required - redirecting...', 'warning');
                        handleAuthRequired(shareData.requiresSignup ? 'signup' : 'login');
                    } else {
                        // Token is valid and user is authenticated, try to access the component
                        logMessage('Access granted, proceeding to component...', 'success');
                        await accessComponent();
                    }
                } else if (data.status === 401) {
                    logMessage('Unauthorized - authentication required', 'warning');
                    handleAuthRequired('signup');
                } else {
                    showError(data.message || 'Invalid access token');
                }
            } catch (error) {
                handleError(error, 'validating access token');
                showError('Failed to validate access token');
            }
        }

        function getCurrentUser() {
            const userInfo = localStorage.getItem('userInfo');
            return userInfo ? JSON.parse(userInfo) : null;
        }

        function handleAuthRequired(type) {
            const returnUrl = `private-share/${accessToken}`;
            let redirectUrl;

            if (type === 'signup') {
                redirectUrl = `signup.html?returnUrl=${encodeURIComponent(returnUrl)}`;
                showAuthRequired('signup', redirectUrl);
            } else {
                redirectUrl = `login.html?returnUrl=${encodeURIComponent(returnUrl)}`;
                showAuthRequired('login', redirectUrl);
            }

            // Auto-redirect after 3 seconds
            setTimeout(() => {
                logMessage(`Auto-redirecting to ${type} page...`, 'info');
                window.location.href = redirectUrl;
            }, 3000);
        }

        async function accessComponent() {
            if (!shareData || !shareData.component) {
                showError('Component information not available');
                return;
            }

            const componentSlug = shareData.component.slug;
            
            try {
                logMessage(`Accessing component: ${componentSlug}`, 'info');
                
                const { response, data } = await apiCall(`/component/private/${componentSlug}?token=${accessToken}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (data.status === 200) {
                    componentData = data.data;
                    logMessage('Component access successful', 'success');
                    
                    if (componentData.requires_payment) {
                        showPaymentRequired();
                    } else {
                        showAccessGranted();
                    }
                } else {
                    showError(data.message || 'Failed to access component');
                }
            } catch (error) {
                handleError(error, 'accessing component');
                showError('Failed to access component');
            }
        }

        function showAuthRequired(type = 'signup', redirectUrl = null) {
            hideAllSteps();

            const authStep = document.getElementById('authRequiredStep');
            const authMessage = authStep.querySelector('.auth-message');
            const authButtons = authStep.querySelector('.button-group');

            if (type === 'login') {
                authMessage.innerHTML = `
                    <h3>🔐 Login Required</h3>
                    <p>You need to login to access this shared component.</p>
                    <p>You will be redirected to the login page in <span id="countdown">3</span> seconds...</p>
                `;
                authButtons.innerHTML = `
                    <button onclick="window.location.href='${redirectUrl || 'login.html'}'" class="success">🔑 Login Now</button>
                    <button onclick="window.location.href='index.html'" class="secondary">🏠 Go Home</button>
                `;
            } else {
                authMessage.innerHTML = `
                    <h3>📝 Signup Required</h3>
                    <p>You need to create an account to access this shared component.</p>
                    <p>You will be redirected to the signup page in <span id="countdown">3</span> seconds...</p>
                `;
                authButtons.innerHTML = `
                    <button onclick="window.location.href='${redirectUrl || 'signup.html'}'" class="success">📝 Sign Up Now</button>
                    <button onclick="window.location.href='login.html'" class="secondary">🔑 Already have an account?</button>
                `;
            }

            authStep.style.display = 'block';

            // Start countdown
            let countdown = 3;
            const countdownElement = document.getElementById('countdown');
            const countdownInterval = setInterval(() => {
                countdown--;
                if (countdownElement) {
                    countdownElement.textContent = countdown;
                }
                if (countdown <= 0) {
                    clearInterval(countdownInterval);
                }
            }, 1000);
        }

        function showPaymentRequired() {
            hideAllSteps();
            
            // Show component info
            const componentInfo = document.getElementById('componentInfo');
            componentInfo.innerHTML = `
                <h3>${componentData.title}</h3>
                <p>${componentData.description || 'No description available'}</p>
            `;
            
            // Show payment info
            const paymentInfo = document.getElementById('paymentInfo');
            if (componentData.payment_info) {
                paymentInfo.innerHTML = `
                    <div class="payment-details">
                        <h4>💰 Payment Details</h4>
                        <p><strong>Price:</strong> $${componentData.payment_info.item_price}</p>
                        <p><strong>Buyer Fee:</strong> $${componentData.payment_info.buyer_fee}</p>
                        <p><strong>Total:</strong> $${componentData.payment_info.purchase_price}</p>
                    </div>
                `;
            }
            
            document.getElementById('paymentRequiredStep').style.display = 'block';
        }

        function showAccessGranted() {
            hideAllSteps();
            
            // Show access type
            const accessTypeIndicator = document.getElementById('accessTypeIndicator');
            let accessTypeClass = 'public-link';
            let accessTypeText = '🔗 Public Link Access';
            
            if (componentData.share_type === 'private_invite') {
                accessTypeClass = 'private-invite';
                accessTypeText = '📧 Private Invitation';
            }
            
            accessTypeIndicator.innerHTML = `<div class="access-type ${accessTypeClass}">${accessTypeText}</div>`;
            
            // Show component details
            const componentDetails = document.getElementById('componentDetails');
            componentDetails.innerHTML = `
                <h3>${componentData.title}</h3>
                <p>${componentData.description || 'No description available'}</p>
                <div class="component-meta">
                    <span>Type: ${componentData.component_type || 'Component'}</span>
                    <span>Created: ${formatDate(componentData.created_at)}</span>
                </div>
            `;
            
            // Show share details
            const shareDetails = document.getElementById('shareDetails');
            if (componentData.shared_by) {
                shareDetails.innerHTML = `
                    <div class="share-info">
                        <h4>📤 Shared by: ${componentData.shared_by.first_name} ${componentData.shared_by.last_name}</h4>
                        ${componentData.personal_message ? `<p><em>"${componentData.personal_message}"</em></p>` : ''}
                        <p><strong>Access Controls:</strong> ${componentData.access_controls?.join(', ') || 'None specified'}</p>
                    </div>
                `;
            }
            
            document.getElementById('accessGrantedStep').style.display = 'block';
        }

        function showError(message) {
            hideAllSteps();
            document.getElementById('errorMessage').innerHTML = `<p>${message}</p>`;
            document.getElementById('errorStep').style.display = 'block';
        }

        function hideAllSteps() {
            document.getElementById('loadingStep').style.display = 'none';
            document.getElementById('authRequiredStep').style.display = 'none';
            document.getElementById('paymentRequiredStep').style.display = 'none';
            document.getElementById('accessGrantedStep').style.display = 'none';
            document.getElementById('errorStep').style.display = 'none';
        }

        function redirectToLogin() {
            const returnUrl = `private-share/${accessToken}`;
            window.location.href = `login.html?returnUrl=${encodeURIComponent(returnUrl)}`;
        }

        function redirectToSignup() {
            const returnUrl = `private-share/${accessToken}`;
            window.location.href = `signup.html?returnUrl=${encodeURIComponent(returnUrl)}`;
        }

        function viewComponent() {
            if (componentData && componentData.slug) {
                window.location.href = `component-view.html?slug=${componentData.slug}&token=${accessToken}`;
            } else {
                logMessage('Component data not available', 'error');
            }
        }

        function goToSharedWithMe() {
            window.location.href = 'shared-with-me.html';
        }

        function proceedToPayment() {
            if (componentData && componentData.slug) {
                window.location.href = `payment.html?component=${componentData.slug}&token=${accessToken}`;
            } else {
                logMessage('Component data not available for payment', 'error');
            }
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }
    </script>
</body>
</html>
