const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const componentUnlockHistorySchema = new Schema({
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    },
    unlock_by: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    component_name: {
        type: String,
        required: true
    },
    component_slug: {
        type: String,
        required: true
    },
    price: {
        fiat: {
            type: Number,
            default: 0
        },
        mpn_points: {
            type: Number,
            default: 0
        }
    },
    expense: {
        fiat: {
            type: Number,
            default: 0
        },
        mpn_points: {
            type: Number,
            default: 0
        }
    },
    mpn_parity: {
        type: Number,
        default: 0
    },
    expired_on: {
        type: Date
    },
    is_active: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const ComponentUnlockHistory = mongoose.model('component_unlock_history', componentUnlockHistorySchema);

module.exports = {
    ComponentUnlockHistory
};