# 🔗 Frontend-Backend Integration Guide

## 🎯 **Overview**

I've created a complete frontend test environment that simulates the entire private share functionality. Here's what you need to know about integrating it with your backend and the changes required.

## 📁 **Frontend Structure Created**

```
frontend-test/
├── index.html              # Main dashboard
├── login.html              # Login with OTP
├── signup.html             # Signup with OTP  
├── private-share.html      # Token access handler
├── shared-with-me.html     # List shared components
├── component-view.html     # View component content
├── share-component.html    # Create shares/links
├── styles.css              # Complete styling
├── config.js               # API utilities
├── server.js               # Simple HTTP server
├── package.json            # Node.js setup
└── README.md               # Complete usage guide
```

## 🔧 **Backend Changes Required**

### **1. CORS Configuration**
Add CORS headers to allow frontend requests:

```javascript
// In your main app.js or server setup
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', 'http://localhost:8080');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.header('Access-Control-Allow-Credentials', 'true');
    
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});
```

### **2. Session/Cookie Configuration**
Update session configuration for cross-origin requests:

```javascript
// In your session middleware
app.use(session({
    // ... existing config
    cookie: {
        secure: false, // Set to true in production with HTTPS
        sameSite: 'lax', // Allow cross-origin requests
        domain: 'localhost' // Adjust for your domain
    }
}));
```

### **3. Email Template URLs**
Update your email service to use the correct frontend URLs:

```javascript
// In services/send_email.service.js
// Update the sendPrivateShareInvitationEmail function

// CHANGE FROM:
const accessUrl = `${process.env.SITE_URL}private-share/${accessToken}`;
const signupUrl = `${process.env.SITE_URL}auth/signup?returnUrl=${encodeURIComponent(`private-share/${accessToken}`)}`;

// TO:
const accessUrl = `http://localhost:8080/private-share/${accessToken}`;
const signupUrl = `http://localhost:8080/signup.html?returnUrl=${encodeURIComponent(`private-share/${accessToken}`)}`;
```

### **4. Environment Variables**
Add/update these environment variables:

```env
# Frontend URL for email links
FRONTEND_URL=http://localhost:8080

# CORS origin
CORS_ORIGIN=http://localhost:8080

# Site URL for email templates
SITE_URL=http://localhost:8080/
```

## 🔗 **URL Redirection Flow**

### **Current Email Links**
Your backend sends emails with these URLs:
```
https://multiplatform.blockverse.tech/private-share/{accessToken}
```

### **Frontend Handling**
The frontend handles these patterns:

1. **Private Share Access**: `/private-share/{token}`
   - Extracts token from URL path
   - Calls `/component/private-share/{token}/validate`
   - Shows appropriate UI based on response

2. **Authentication Flow**: 
   - Login: `/login.html?returnUrl=private-share/{token}`
   - Signup: `/signup.html?returnUrl=private-share/{token}`
   - After auth: Redirects back to `private-share/{token}`

3. **Component View**: `/component-view.html?slug={slug}&token={token}`
   - Shows component after successful access
   - Displays success message

## 📧 **Email Service Integration**

### **Testing Email Delivery**
1. Configure your email service (SMTP/SendGrid/etc.)
2. Use real email addresses in the test frontend
3. Check email delivery and click links
4. Verify redirection works correctly

### **Email Template Variables**
Make sure your email templates use these URLs:
```javascript
// For existing users
{{access_url}} = http://localhost:8080/private-share/{token}

// For new users  
{{signup_url}} = http://localhost:8080/signup.html?returnUrl=private-share/{token}
```

## 🧪 **Testing the Complete Flow**

### **1. Start Both Servers**
```bash
# Terminal 1: Start your backend
npm start  # or your backend start command

# Terminal 2: Start frontend test server
cd frontend-test
node server.js
```

### **2. Test Private Email Invitations**
1. Open `http://localhost:8080`
2. Login/signup with your email
3. Go to "Share Component"
4. Enter a valid component ID
5. Select "Private Email Invitation"
6. Enter recipient email addresses
7. Send invitation
8. Check recipient email and click link
9. Verify complete flow works

### **3. Test Public Shareable Links**
1. Go to "Share Component"
2. Select "Public Shareable Link"
3. Create link and copy URL
4. Open URL in incognito/different browser
5. Verify anyone can access

### **4. Test Token Access**
1. Use the access token from step 2 or 3
2. Go to main page and enter token
3. Test token validation
4. Access component directly

## 🔍 **Debugging & Troubleshooting**

### **Common Issues & Solutions**

1. **CORS Errors**
   ```
   Error: Access to fetch at 'http://localhost:3000/...' from origin 'http://localhost:8080' has been blocked by CORS policy
   ```
   **Solution**: Add CORS headers to your backend (see section 1 above)

2. **Session/Cookie Issues**
   ```
   Error: User not authenticated
   ```
   **Solution**: Update session configuration (see section 2 above)

3. **Email Link Redirection**
   ```
   Error: Page not found when clicking email links
   ```
   **Solution**: Update email service URLs (see section 3 above)

4. **Token Extraction Issues**
   ```
   Error: No access token provided in URL
   ```
   **Solution**: Verify URL format matches `/private-share/{token}`

### **Debugging Tools**

1. **Browser Console**: All API calls are logged
2. **Network Tab**: Check request/response details
3. **Frontend Logs**: Each page has a logs section
4. **Backend Logs**: Check your backend console

## 🎯 **Key Benefits of This Frontend**

### **Complete Visualization**
- ✅ See the entire user journey
- ✅ Test all edge cases
- ✅ Verify email integration
- ✅ Check token handling

### **Real-World Testing**
- ✅ Actual email delivery
- ✅ Cross-browser compatibility
- ✅ Mobile responsiveness
- ✅ Authentication flows

### **Development Efficiency**
- ✅ No need to build full frontend
- ✅ Quick iteration and testing
- ✅ Easy debugging and logging
- ✅ Isolated testing environment

## 🚀 **Next Steps**

1. **Setup**: Follow the README.md in the frontend-test folder
2. **Configure**: Update backend CORS and session settings
3. **Test**: Run through all test scenarios
4. **Iterate**: Use logs to debug and improve
5. **Deploy**: Apply learnings to your production frontend

## 📞 **Support**

If you encounter issues:
1. Check browser console for errors
2. Verify API endpoints with curl/Postman
3. Test email service separately
4. Check database connections
5. Review the detailed logs in the frontend

This frontend test environment will help you visualize and perfect the private share functionality before implementing it in your production frontend! 🎉
