<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Shares - Private Share Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            background: white;
            color: #667eea;
            border-bottom: 3px solid #667eea;
        }

        .nav-tab:hover {
            background: #e9ecef;
        }

        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f3f4;
        }

        .section-title {
            font-size: 1.5rem;
            color: #333;
            font-weight: 600;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(81, 207, 102, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffd43b 0%, #fab005 100%);
            color: #333;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .share-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .share-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .share-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .share-info {
            flex: 1;
        }

        .share-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .share-meta {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 10px;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-accepted {
            background: #d1edff;
            color: #0c5460;
        }

        .status-expired {
            background: #f8d7da;
            color: #721c24;
        }

        .status-revoked {
            background: #d3d3d4;
            color: #383d41;
        }

        .share-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .share-link {
            background: #e9ecef;
            padding: 10px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9rem;
            word-break: break-all;
            margin: 10px 0;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 500px;
            width: 90%;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .checkbox-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .tab-content {
                padding: 20px;
            }

            .share-header {
                flex-direction: column;
                gap: 10px;
            }

            .share-actions {
                flex-direction: column;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 Manage Shares</h1>
            <p>Manage your private shares and shareable links</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="switchTab('overview')">📊 Overview</button>
            <button class="nav-tab" onclick="switchTab('private-shares')">👥 Private Shares</button>
            <button class="nav-tab" onclick="switchTab('shareable-links')">🔗 Shareable Links</button>
            <button class="nav-tab" onclick="switchTab('shared-with-me')">📥 Shared With Me</button>
        </div>

        <!-- Overview Tab -->
        <div id="overview" class="tab-content active">
            <div class="section-header">
                <h2 class="section-title">📊 Overview</h2>
            </div>
            
            <div class="stats-grid" id="statsGrid">
                <div class="stat-card">
                    <div class="stat-number" id="totalShares">-</div>
                    <div class="stat-label">Total Shares</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="activeLinks">-</div>
                    <div class="stat-label">Active Links</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="sharedWithMe">-</div>
                    <div class="stat-label">Shared With Me</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalAccess">-</div>
                    <div class="stat-label">Total Access Count</div>
                </div>
            </div>
        </div>

        <!-- Private Shares Tab -->
        <div id="private-shares" class="tab-content">
            <div class="section-header">
                <h2 class="section-title">👥 Private Shares</h2>
                <button class="btn btn-primary" onclick="showCreateShareModal()">
                    ➕ Create New Share
                </button>
            </div>
            
            <div id="privateSharesContainer">
                <div class="loading">Loading private shares...</div>
            </div>
        </div>

        <!-- Shareable Links Tab -->
        <div id="shareable-links" class="tab-content">
            <div class="section-header">
                <h2 class="section-title">🔗 Shareable Links</h2>
                <button class="btn btn-primary" onclick="showCreateLinkModal()">
                    ➕ Generate New Link
                </button>
            </div>
            
            <div id="shareableLinksContainer">
                <div class="loading">Loading shareable links...</div>
            </div>
        </div>

        <!-- Shared With Me Tab -->
        <div id="shared-with-me" class="tab-content">
            <div class="section-header">
                <h2 class="section-title">📥 Shared With Me</h2>
            </div>
            
            <div id="sharedWithMeContainer">
                <div class="loading">Loading shared components...</div>
            </div>
        </div>
    </div>

    <!-- Create Link Modal -->
    <div id="createLinkModal" class="modal">
        <div class="modal-content">
            <h3 style="margin-bottom: 20px;">🔗 Generate New Shareable Link</h3>
            
            <div class="form-group">
                <label class="form-label">Component</label>
                <select id="componentSelect" class="form-control">
                    <option value="">Select a component...</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">Link Name</label>
                <input type="text" id="linkName" class="form-control" placeholder="Enter link name">
            </div>
            
            <div class="form-group">
                <label class="form-label">Access Duration</label>
                <select id="accessDuration" class="form-control" onchange="toggleDurationDays()">
                    <option value="undefined">No expiration</option>
                    <option value="days">Custom days</option>
                </select>
            </div>
            
            <div class="form-group" id="durationDaysGroup" style="display: none;">
                <label class="form-label">Duration (Days)</label>
                <input type="number" id="durationDays" class="form-control" min="1" max="365" placeholder="Enter number of days">
            </div>
            
            <div class="form-group">
                <label class="form-label">Access Controls</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="accessCopy" value="copy" checked>
                        <label for="accessCopy">Copy</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="accessDownload" value="download">
                        <label for="accessDownload">Download</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="accessFork" value="fork">
                        <label for="accessFork">Fork</label>
                    </div>
                </div>
            </div>
            
            <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 30px;">
                <button class="btn btn-secondary" onclick="closeCreateLinkModal()">Cancel</button>
                <button class="btn btn-primary" onclick="createShareableLink()">Generate Link</button>
            </div>
        </div>
    </div>

    <script src="shared-api-utils.js"></script>
    <script>
        const API_BASE_URL = 'http://localhost:7898/api/front';
        let currentTab = 'overview';
        let userComponents = [];

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            loadOverviewData();
            loadUserComponents();
        });

        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all nav tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to selected nav tab
            event.target.classList.add('active');
            
            currentTab = tabName;
            
            // Load data for the selected tab
            switch(tabName) {
                case 'overview':
                    loadOverviewData();
                    break;
                case 'private-shares':
                    loadPrivateShares();
                    break;
                case 'shareable-links':
                    loadShareableLinks();
                    break;
                case 'shared-with-me':
                    loadSharedWithMe();
                    break;
            }
        }

        async function loadOverviewData() {
            try {
                // Load statistics for overview
                const stats = await Promise.all([
                    apiCall('/v1/component/my-private-shares', { method: 'POST', body: JSON.stringify({ limit: 1000, skip: 0 }) }),
                    apiCall('/v1/component/my-shareable-links', { method: 'POST', body: JSON.stringify({ limit: 1000, skip: 0 }) }),
                    apiCall('/v1/component/shared-with-me', { method: 'POST', body: JSON.stringify({ limit: 1000, skip: 0 }) })
                ]);

                const privateShares = stats[0].data?.data?.recordsTotal || 0;
                const shareableLinks = stats[1].data?.data?.recordsTotal || 0;
                const sharedWithMe = stats[2].data?.data?.recordsTotal || 0;
                
                // Calculate total access count
                const linksData = stats[1].data?.data?.list || [];
                const totalAccess = linksData.reduce((sum, link) => sum + (link.access_count || 0), 0);

                document.getElementById('totalShares').textContent = privateShares + shareableLinks;
                document.getElementById('activeLinks').textContent = shareableLinks;
                document.getElementById('sharedWithMe').textContent = sharedWithMe;
                document.getElementById('totalAccess').textContent = totalAccess;

            } catch (error) {
                console.error('Error loading overview data:', error);
            }
        }

        async function loadUserComponents() {
            try {
                const { response, data } = await apiCall('/v1/component/list/', {
                    method: 'POST',
                    body: JSON.stringify({
                        limit: 100,
                        skip: 0,
                        component_state: 'private'
                    })
                });

                if (data.status === 200) {
                    userComponents = data.data.list || [];
                    populateComponentSelect();
                }
            } catch (error) {
                console.error('Error loading user components:', error);
            }
        }

        function populateComponentSelect() {
            const select = document.getElementById('componentSelect');
            select.innerHTML = '<option value="">Select a component...</option>';
            
            userComponents.forEach(component => {
                const option = document.createElement('option');
                option.value = component._id;
                option.textContent = component.title;
                select.appendChild(option);
            });
        }

        function showCreateLinkModal() {
            document.getElementById('createLinkModal').style.display = 'block';
        }

        function closeCreateLinkModal() {
            document.getElementById('createLinkModal').style.display = 'none';
            // Reset form
            document.getElementById('componentSelect').value = '';
            document.getElementById('linkName').value = '';
            document.getElementById('accessDuration').value = 'undefined';
            document.getElementById('durationDays').value = '';
            document.getElementById('durationDaysGroup').style.display = 'none';
            document.querySelectorAll('input[type="checkbox"]').forEach(cb => {
                cb.checked = cb.id === 'accessCopy';
            });
        }

        function toggleDurationDays() {
            const duration = document.getElementById('accessDuration').value;
            const daysGroup = document.getElementById('durationDaysGroup');
            daysGroup.style.display = duration === 'days' ? 'block' : 'none';
        }

        async function createShareableLink() {
            const componentId = document.getElementById('componentSelect').value;
            const linkName = document.getElementById('linkName').value;
            const accessDuration = document.getElementById('accessDuration').value;
            const durationDays = document.getElementById('durationDays').value;
            
            if (!componentId || !linkName) {
                alert('Please select a component and enter a link name');
                return;
            }

            const accessControls = [];
            document.querySelectorAll('input[type="checkbox"]:checked').forEach(cb => {
                accessControls.push(cb.value);
            });

            try {
                const requestBody = {
                    link_name: linkName,
                    access_duration: accessDuration,
                    access_controls: accessControls
                };

                if (accessDuration === 'days' && durationDays) {
                    requestBody.duration_days = parseInt(durationDays);
                }

                const { response, data } = await apiCall(`/v1/component/${componentId}/generate-link`, {
                    method: 'POST',
                    body: JSON.stringify(requestBody)
                });

                if (data.status === 200) {
                    alert('Shareable link generated successfully!');
                    closeCreateLinkModal();
                    if (currentTab === 'shareable-links') {
                        loadShareableLinks();
                    }
                    loadOverviewData();
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                console.error('Error creating shareable link:', error);
                alert('Error creating shareable link');
            }
        }

        async function loadPrivateShares() {
            const container = document.getElementById('privateSharesContainer');
            container.innerHTML = '<div class="loading">Loading private shares...</div>';

            try {
                const { response, data } = await apiCall('/v1/component/my-private-shares', {
                    method: 'POST',
                    body: JSON.stringify({ limit: 100, skip: 0 })
                });

                if (data.status === 200) {
                    const shares = data.data.list || [];
                    renderPrivateShares(shares);
                } else {
                    container.innerHTML = '<div class="empty-state"><h3>Error loading private shares</h3><p>' + data.message + '</p></div>';
                }
            } catch (error) {
                console.error('Error loading private shares:', error);
                container.innerHTML = '<div class="empty-state"><h3>Error</h3><p>Failed to load private shares</p></div>';
            }
        }

        function renderPrivateShares(shares) {
            const container = document.getElementById('privateSharesContainer');

            if (shares.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <h3>No Private Shares</h3>
                        <p>You haven't created any private shares yet.</p>
                        <button class="btn btn-primary" onclick="showCreateShareModal()">Create Your First Share</button>
                    </div>
                `;
                return;
            }

            container.innerHTML = shares.map(share => `
                <div class="share-card">
                    <div class="share-header">
                        <div class="share-info">
                            <div class="share-title">${share.personal_message || 'Private Share'}</div>
                            <div class="share-meta">
                                Shared with: ${share.shared_with_email || 'Unknown'} •
                                Created: ${new Date(share.created_at).toLocaleDateString()} •
                                <span class="status-badge status-${share.status}">${share.status}</span>
                            </div>
                            <div class="share-meta">
                                Component: ${share.component?.title || 'Unknown'} •
                                Access: ${(share.access_controls || []).join(', ') || 'View'} •
                                Accessed: ${share.access_count || 0} times
                            </div>
                            ${share.expires_at ? `<div class="share-meta">Expires: ${new Date(share.expires_at).toLocaleDateString()}</div>` : ''}
                        </div>
                    </div>
                    <div class="share-actions">
                        <button class="btn btn-danger" onclick="revokeShare('${share._id}', 'private')">
                            🗑️ Revoke Share
                        </button>
                        <button class="btn btn-secondary" onclick="viewShareDetails('${share._id}')">
                            👁️ View Details
                        </button>
                    </div>
                </div>
            `).join('');
        }

        async function loadShareableLinks() {
            const container = document.getElementById('shareableLinksContainer');
            container.innerHTML = '<div class="loading">Loading shareable links...</div>';

            try {
                const { response, data } = await apiCall('/v1/component/my-shareable-links', {
                    method: 'POST',
                    body: JSON.stringify({ limit: 100, skip: 0 })
                });

                if (data.status === 200) {
                    const links = data.data.list || [];
                    renderShareableLinks(links);
                } else {
                    container.innerHTML = '<div class="empty-state"><h3>Error loading shareable links</h3><p>' + data.message + '</p></div>';
                }
            } catch (error) {
                console.error('Error loading shareable links:', error);
                container.innerHTML = '<div class="empty-state"><h3>Error</h3><p>Failed to load shareable links</p></div>';
            }
        }

        function renderShareableLinks(links) {
            const container = document.getElementById('shareableLinksContainer');

            if (links.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <h3>No Shareable Links</h3>
                        <p>You haven't generated any shareable links yet.</p>
                        <button class="btn btn-primary" onclick="showCreateLinkModal()">Generate Your First Link</button>
                    </div>
                `;
                return;
            }

            container.innerHTML = links.map(link => {
                const status = getShareStatus(link);
                const shareUrl = `${window.location.origin}/private-share/${link.access_token}`;

                return `
                    <div class="share-card">
                        <div class="share-header">
                            <div class="share-info">
                                <div class="share-title">${link.link_name || 'Shareable Link'}</div>
                                <div class="share-meta">
                                    Component: ${link.component?.title || 'Unknown'} •
                                    Created: ${new Date(link.created_at).toLocaleDateString()} •
                                    <span class="status-badge status-${status.toLowerCase()}">${status}</span>
                                </div>
                                <div class="share-meta">
                                    Access: ${(link.access_controls || []).join(', ') || 'View'} •
                                    Accessed: ${link.access_count || 0} times
                                    ${link.shared_with_user ? ' • Claimed by user' : ' • Unclaimed'}
                                </div>
                                ${link.expires_at ? `<div class="share-meta">Expires: ${new Date(link.expires_at).toLocaleDateString()}</div>` : '<div class="share-meta">No expiration</div>'}
                                <div class="share-link">${shareUrl}</div>
                            </div>
                        </div>
                        <div class="share-actions">
                            <button class="btn btn-success" onclick="copyToClipboard('${shareUrl}')">
                                📋 Copy Link
                            </button>
                            <button class="btn btn-warning" onclick="refreshShareLink('${link._id}')">
                                🔄 Refresh Link
                            </button>
                            <button class="btn btn-danger" onclick="deleteShareLink('${link._id}')">
                                🗑️ Delete Link
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getShareStatus(share) {
            if (!share.is_active) return 'REVOKED';
            if (share.expires_at && new Date(share.expires_at) < new Date()) return 'EXPIRED';
            if (share.status === 'accepted') return 'ACCEPTED';
            return 'PENDING';
        }

        async function loadSharedWithMe() {
            const container = document.getElementById('sharedWithMeContainer');
            container.innerHTML = '<div class="loading">Loading shared components...</div>';

            try {
                const { response, data } = await apiCall('/v1/component/shared-with-me', {
                    method: 'POST',
                    body: JSON.stringify({ limit: 100, skip: 0 })
                });

                if (data.status === 200) {
                    const shares = data.data.list || [];
                    renderSharedWithMe(shares);
                } else {
                    container.innerHTML = '<div class="empty-state"><h3>Error loading shared components</h3><p>' + data.message + '</p></div>';
                }
            } catch (error) {
                console.error('Error loading shared components:', error);
                container.innerHTML = '<div class="empty-state"><h3>Error</h3><p>Failed to load shared components</p></div>';
            }
        }

        function renderSharedWithMe(shares) {
            const container = document.getElementById('sharedWithMeContainer');

            if (shares.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <h3>No Shared Components</h3>
                        <p>No components have been shared with you yet.</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = shares.map(share => `
                <div class="share-card">
                    <div class="share-header">
                        <div class="share-info">
                            <div class="share-title">${share.component?.title || 'Shared Component'}</div>
                            <div class="share-meta">
                                Shared by: ${share.shared_by?.first_name || 'Unknown'} ${share.shared_by?.last_name || ''} •
                                Type: ${share.access_type === 'by_invite' ? 'Private Invite' : 'Shareable Link'} •
                                <span class="status-badge status-${share.status}">${share.status}</span>
                            </div>
                            <div class="share-meta">
                                Access: ${(share.access_controls || []).join(', ') || 'View'} •
                                Received: ${new Date(share.created_at).toLocaleDateString()}
                            </div>
                            ${share.personal_message ? `<div class="share-meta">Message: "${share.personal_message}"</div>` : ''}
                            ${share.expires_at ? `<div class="share-meta">Expires: ${new Date(share.expires_at).toLocaleDateString()}</div>` : ''}
                        </div>
                    </div>
                    <div class="share-actions">
                        <button class="btn btn-primary" onclick="openComponent('${share.component?._id}', '${share.shared_by?.username}')">
                            🚀 Open Component
                        </button>
                        <button class="btn btn-secondary" onclick="viewShareDetails('${share._id}')">
                            👁️ View Details
                        </button>
                    </div>
                </div>
            `).join('');
        }

        async function revokeShare(shareId, type) {
            if (!confirm('Are you sure you want to revoke this share? This action cannot be undone.')) {
                return;
            }

            try {
                const { response, data } = await apiCall(`/v1/component/revoke-share/${shareId}`, {
                    method: 'POST'
                });

                if (data.status === 200) {
                    alert('Share revoked successfully');
                    if (currentTab === 'private-shares') {
                        loadPrivateShares();
                    } else if (currentTab === 'shareable-links') {
                        loadShareableLinks();
                    }
                    loadOverviewData();
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                console.error('Error revoking share:', error);
                alert('Error revoking share');
            }
        }

        async function deleteShareLink(shareId) {
            if (!confirm('Are you sure you want to delete this shareable link? This action cannot be undone.')) {
                return;
            }

            try {
                const { response, data } = await apiCall(`/v1/component/delete-share/${shareId}`, {
                    method: 'DELETE'
                });

                if (data.status === 200) {
                    alert('Shareable link deleted successfully');
                    loadShareableLinks();
                    loadOverviewData();
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                console.error('Error deleting share link:', error);
                alert('Error deleting share link');
            }
        }

        async function refreshShareLink(shareId) {
            if (!confirm('Are you sure you want to refresh this link? The old link will become invalid.')) {
                return;
            }

            try {
                const { response, data } = await apiCall(`/v1/component/refresh-share-link/${shareId}`, {
                    method: 'POST'
                });

                if (data.status === 200) {
                    alert('Share link refreshed successfully');
                    loadShareableLinks();
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                console.error('Error refreshing share link:', error);
                alert('Error refreshing share link');
            }
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('Link copied to clipboard!');
            }).catch(err => {
                console.error('Error copying to clipboard:', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('Link copied to clipboard!');
            });
        }

        function openComponent(componentId, username) {
            if (componentId && username) {
                const url = `${window.location.origin}/${username}/${componentId}`;
                window.open(url, '_blank');
            } else {
                alert('Component information not available');
            }
        }

        function viewShareDetails(shareId) {
            alert('Share details view - to be implemented');
        }

        function showCreateShareModal() {
            alert('Create private share modal - to be implemented');
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('createLinkModal');
            if (event.target === modal) {
                closeCreateLinkModal();
            }
        }
    </script>
</body>
</html>
    </script>
</body>
</html>
