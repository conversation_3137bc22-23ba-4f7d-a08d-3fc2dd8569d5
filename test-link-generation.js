// Test script to debug link generation issues
require('dotenv').config();
const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

const { ComponentPrivateShares } = require('./models/component_private_shares.model');
const { Users } = require('./models/users.model');
const { Components } = require('./models/component.model');
const { generatePublicShareableLink } = require('./services/private_share.service');

async function testLinkGeneration() {
    try {
        console.log('🔍 Testing Link Generation...\n');
        
        // Find a user and their component
        const user = await Users.findOne({ is_verified: true }).lean();
        if (!user) {
            console.log('❌ No verified users found');
            return;
        }
        
        const component = await Components.findOne({ 
            created_by_user: user._id,
            component_state: 'private'
        }).lean();
        
        if (!component) {
            console.log('❌ No private components found for this user');
            return;
        }
        
        console.log(`👤 User: ${user.first_name} ${user.last_name} (${user.email})`);
        console.log(`   ID: ${user._id}`);
        console.log(`📦 Component: ${component.title}`);
        console.log(`   ID: ${component._id}`);
        console.log('');
        
        // Test link generation
        console.log('🔗 Generating shareable link...');
        
        const linkResult = await generatePublicShareableLink(
            component._id,
            user._id,
            'Test Link Generation',
            'days',
            30,
            ['copy', 'download']
        );
        
        console.log('✅ Link generation result:');
        console.log(`   Success: ${linkResult.success}`);
        console.log(`   Share ID: ${linkResult.share._id}`);
        console.log(`   URL: ${linkResult.shareableUrl}`);
        console.log('');
        
        // Fetch the created share from database to verify
        console.log('🔍 Fetching created share from database...');
        const createdShare = await ComponentPrivateShares.findById(linkResult.share._id).lean();
        
        console.log('📋 Created share details:');
        console.log(`   ID: ${createdShare._id}`);
        console.log(`   Type: ${createdShare.access_type}`);
        console.log(`   Status: ${createdShare.status}`);
        console.log(`   Shared with user: ${createdShare.shared_with_user || 'NULL'}`);
        console.log(`   Shared with email: ${createdShare.shared_with_email || 'NULL'}`);
        console.log(`   Shared by: ${createdShare.shared_by}`);
        console.log(`   Token: ${createdShare.access_token.substring(0, 16)}...`);
        console.log(`   Link name: ${createdShare.link_name}`);
        console.log(`   Active: ${createdShare.is_active}`);
        console.log(`   Created: ${createdShare.created_at}`);
        console.log('');
        
        // Check if this matches expected values
        console.log('🔍 Validation:');
        
        const issues = [];
        
        if (createdShare.shared_with_user !== null) {
            issues.push(`❌ shared_with_user should be null, but is: ${createdShare.shared_with_user}`);
        } else {
            console.log('✅ shared_with_user is correctly null');
        }
        
        if (createdShare.shared_with_email !== null) {
            issues.push(`❌ shared_with_email should be null, but is: ${createdShare.shared_with_email}`);
        } else {
            console.log('✅ shared_with_email is correctly null');
        }
        
        if (createdShare.status !== 'pending') {
            issues.push(`❌ status should be 'pending', but is: ${createdShare.status}`);
        } else {
            console.log('✅ status is correctly pending');
        }
        
        if (createdShare.access_type !== 'by_link') {
            issues.push(`❌ access_type should be 'by_link', but is: ${createdShare.access_type}`);
        } else {
            console.log('✅ access_type is correctly by_link');
        }
        
        if (issues.length > 0) {
            console.log('\n🚨 ISSUES FOUND:');
            issues.forEach(issue => console.log(issue));
        } else {
            console.log('\n🎉 All validations passed! Link generation is working correctly.');
        }
        
        // Clean up - delete the test share
        console.log('\n🧹 Cleaning up test share...');
        await ComponentPrivateShares.findByIdAndDelete(createdShare._id);
        console.log('✅ Test share deleted');
        
    } catch (error) {
        console.error('❌ Error during test:', error);
    } finally {
        mongoose.connection.close();
        console.log('\n✅ Database connection closed');
    }
}

testLinkGeneration();
