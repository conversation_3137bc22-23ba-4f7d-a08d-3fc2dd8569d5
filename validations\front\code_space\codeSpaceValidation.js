const Joi = require('joi');

const { codeSpaceFilterTypes, repositoryState } = require('../../../config/gitlab.constant');

class CodeSpaceValidation {
    createRepository(params) {

        const forbiddenNames = [
            'badges', 'blame', 'blob', 'builds', 'commits', 'create', 'create_dir', 'edit',
            'environments/folders', 'files', 'find_file', 'gitlab-lfs/objects', 'info/lfs/objects',
            'new', 'preview', 'raw', 'refs', 'tree', 'update', 'wikis'
        ];

        const regex = /^(?!.*[-_.]{2})(?!.*\.(git|atom)$)(?![-_.])[a-zA-Z0-9][a-zA-Z0-9._-]*[a-zA-Z0-9]$/;

        const schema = Joi.object({
            project_name: Joi.string().invalid(...forbiddenNames).pattern(regex).required().messages({
                'any.required': 'Codespace name is required.',
                'string.empty': 'Codespace name cannot be empty.',
                'string.pattern.base': 'Codespace name format is invalid. Please ensure it follows the required naming convention.',
                'any.invalid': 'The chosen codespace name is not allowed. Please select a different name.'
            }),
            platform_id: Joi.array().items(Joi.string().trim()).optional(),
            component_id: Joi.string().optional(),
            description: Joi.string().optional().allow(null, '').messages({
                'string.base': 'Description must be a string.'
            }),
            initialize_with_readme: Joi.boolean().required().messages({
                'any.required': 'Please specify whether to initialize with a README.',
                'boolean.base': 'Initialize with README must be true or false.'
            }),
            state: Joi.string().valid(...Object.values(repositoryState)).optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    fetchAllCodeSpace(params) {
        const schema = Joi.object({
            searchText: Joi.string().optional().allow(null, ''),
            linkType: Joi.string().valid(...Object.values(codeSpaceFilterTypes)).optional(),
            platform_id: Joi.array().items(Joi.string().trim()).optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    createRepositoryFork(params) {

        const forbiddenNames = [
            'badges', 'blame', 'blob', 'builds', 'commits', 'create', 'create_dir', 'edit',
            'environments/folders', 'files', 'find_file', 'gitlab-lfs/objects', 'info/lfs/objects',
            'new', 'preview', 'raw', 'refs', 'tree', 'update', 'wikis'
        ];

        const regex = /^(?!.*[-_.]{2})(?!.*\.(git|atom)$)(?![-_.])[a-zA-Z0-9][a-zA-Z0-9._-]*[a-zA-Z0-9]$/;

        const schema = Joi.object({
            project_name: Joi.string().invalid(...forbiddenNames).pattern(regex).required().messages({
                'any.required': 'Codespace name is required.',
                'string.empty': 'Codespace name cannot be empty.',
                'string.pattern.base': 'Codespace name format is invalid. Please ensure it follows the required naming convention.',
                'any.invalid': 'The chosen codespace name is not allowed. Please select a different name.'
            }),
            description: Joi.string().optional().allow(null, '').messages({
                'string.base': 'Description must be a string.'
            }),
            platform_id: Joi.array().items(Joi.string().trim()).optional(),
            state: Joi.string().valid(...Object.values(repositoryState)).optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    updateCodeSpace(params) {
        const schema = Joi.object({
            identification_tag: Joi.array().items(Joi.string().trim()).optional(),
            video_url: Joi.string().optional().allow(null, '')
        });
        return schema.validate(params);
    }
}

module.exports = new CodeSpaceValidation();