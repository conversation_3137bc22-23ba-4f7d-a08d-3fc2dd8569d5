const Joi = require('joi');

const { codeSpaceFilterTypes, repositoryState } = require('../../../config/gitlab.constant');

class CodeSpaceValidation {
    createRepository(params) {

        const forbiddenNames = [
            'badges', 'blame', 'blob', 'builds', 'commits', 'create', 'create_dir', 'edit',
            'environments/folders', 'files', 'find_file', 'gitlab-lfs/objects', 'info/lfs/objects',
            'new', 'preview', 'raw', 'refs', 'tree', 'update', 'wikis'
        ];

        const regex = /^(?!.*[-_.]{2})(?!.*\.(git|atom)$)(?![-_.])[a-zA-Z0-9][a-zA-Z0-9._-]*[a-zA-Z0-9]$/;

        const schema = Joi.object({
            project_name: Joi.string().invalid(...forbiddenNames).pattern(regex).required(),
            platform_id: Joi.array().items(Joi.string().trim()).optional(),
            component_id: Joi.string().optional(),
            description: Joi.string().optional().allow(null, ''),
            initialize_with_readme: Joi.boolean().required(),
            state: Joi.string().valid(...Object.values(repositoryState)).optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    fetchAllCodeSpace(params) {
        const schema = Joi.object({
            searchText: Joi.string().optional().allow(null, ''),
            linkType: Joi.string().valid(...Object.values(codeSpaceFilterTypes)).optional(),
            platform_id: Joi.array().items(Joi.string().trim()).optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    createRepositoryFork(params) {

        const forbiddenNames = [
            'badges', 'blame', 'blob', 'builds', 'commits', 'create', 'create_dir', 'edit',
            'environments/folders', 'files', 'find_file', 'gitlab-lfs/objects', 'info/lfs/objects',
            'new', 'preview', 'raw', 'refs', 'tree', 'update', 'wikis'
        ];

        const regex = /^(?!.*[-_.]{2})(?!.*\.(git|atom)$)(?![-_.])[a-zA-Z0-9][a-zA-Z0-9._-]*[a-zA-Z0-9]$/;

        const schema = Joi.object({
            project_name: Joi.string().invalid(...forbiddenNames).pattern(regex).required(),
            description: Joi.string().optional().allow(null, ''),
            platform_id: Joi.array().items(Joi.string().trim()).optional(),
            state: Joi.string().valid(...Object.values(repositoryState)).optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    updateCodeSpace(params) {
        const schema = Joi.object({
            identification_tag: Joi.array().items(Joi.string().trim()).optional(),
            video_url: Joi.string().optional().allow(null, '')
        });
        return schema.validate(params);
    }
}

module.exports = new CodeSpaceValidation();