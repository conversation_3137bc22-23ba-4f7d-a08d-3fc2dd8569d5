// Test the exact query conditions being used
require('dotenv').config();
const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

const { ComponentPrivateShares } = require('./models/component_private_shares.model');
const { Users } = require('./models/users.model');

async function testQueryConditions() {
    try {
        console.log('🔍 Testing Query Conditions...\n');
        
        // Get a test user
        const testUser = await Users.findOne({ is_verified: true }).lean();
        if (!testUser) {
            console.log('❌ No verified users found');
            return;
        }
        
        console.log(`👤 Test User: ${testUser.first_name} ${testUser.last_name} (${testUser.email})`);
        console.log(`   User ID: ${testUser._id}`);
        console.log('');
        
        // Test the exact conditions from the service
        const userId = testUser._id.toString();
        const userEmail = testUser.email;
        
        console.log('📋 Building conditions exactly like the service...');
        
        const conditions = {
            // Must be specifically shared with this user (either by ID or email)
            $and: [
                {
                    $or: [
                        // Shares assigned to this user's ID (accepted shares)
                        { shared_with_user: new mongoose.Types.ObjectId(userId) },
                        // Shares assigned to this user's email (pending invites)
                        { 
                            shared_with_email: userEmail,
                            shared_with_user: null // Ensure it's not assigned to another user
                        }
                    ]
                },
                // Additional security filters
                {
                    is_active: true,
                    status: { $in: ['pending', 'accepted'] },
                    $or: [
                        { expires_at: { $gt: new Date() } },
                        { expires_at: null }
                    ]
                }
            ]
        };
        
        console.log('🔍 Query conditions:');
        console.log(JSON.stringify(conditions, null, 2));
        console.log('');
        
        // Execute the query
        console.log('⚡ Executing query...');
        const shares = await ComponentPrivateShares.find(conditions).lean();
        
        console.log(`📊 Found ${shares.length} shares for this user`);
        
        if (shares.length > 0) {
            console.log('\n📋 Share details:');
            shares.forEach((share, index) => {
                console.log(`${index + 1}. Share ID: ${share._id}`);
                console.log(`   Type: ${share.access_type}`);
                console.log(`   Status: ${share.status}`);
                console.log(`   Shared with user: ${share.shared_with_user || 'NULL'}`);
                console.log(`   Shared with email: ${share.shared_with_email || 'NULL'}`);
                console.log(`   Created: ${share.created_at}`);
                console.log(`   Active: ${share.is_active}`);
                console.log('');
            });
        }
        
        // Now test with a different user to ensure isolation
        console.log('🔒 Testing user isolation...');
        const otherUsers = await Users.find({ 
            is_verified: true, 
            _id: { $ne: testUser._id } 
        }).limit(1).lean();
        
        if (otherUsers.length > 0) {
            const otherUser = otherUsers[0];
            console.log(`👤 Other User: ${otherUser.first_name} ${otherUser.last_name} (${otherUser.email})`);
            
            const otherConditions = {
                $and: [
                    {
                        $or: [
                            { shared_with_user: new mongoose.Types.ObjectId(otherUser._id) },
                            { 
                                shared_with_email: otherUser.email,
                                shared_with_user: null
                            }
                        ]
                    },
                    {
                        is_active: true,
                        status: { $in: ['pending', 'accepted'] },
                        $or: [
                            { expires_at: { $gt: new Date() } },
                            { expires_at: null }
                        ]
                    }
                ]
            };
            
            const otherShares = await ComponentPrivateShares.find(otherConditions).lean();
            console.log(`📊 Other user has ${otherShares.length} shares`);
            
            // Check for overlap
            const shareIds1 = shares.map(s => s._id.toString());
            const shareIds2 = otherShares.map(s => s._id.toString());
            const overlap = shareIds1.filter(id => shareIds2.includes(id));
            
            if (overlap.length === 0) {
                console.log('✅ SECURE: No overlap between users');
            } else {
                console.log(`❌ SECURITY ISSUE: ${overlap.length} overlapping shares!`);
                console.log('Overlapping IDs:', overlap);
            }
        }
        
        // Check for problematic shares (no user and no email)
        console.log('\n🔍 Checking for problematic shares...');
        const problematicShares = await ComponentPrivateShares.find({
            shared_with_user: null,
            shared_with_email: null,
            is_active: true,
            status: 'accepted' // These should not exist
        }).lean();
        
        console.log(`⚠️  Found ${problematicShares.length} accepted shares with no user/email assignment`);
        if (problematicShares.length > 0) {
            console.log('These shares could be causing security issues!');
            problematicShares.slice(0, 3).forEach((share, index) => {
                console.log(`${index + 1}. ID: ${share._id}, Type: ${share.access_type}, Token: ${share.access_token?.substring(0, 8)}...`);
            });
        }
        
        console.log('\n🎉 Query test completed!');
        
    } catch (error) {
        console.error('❌ Error during test:', error);
    } finally {
        mongoose.connection.close();
        console.log('\n✅ Database connection closed');
    }
}

testQueryConditions();
