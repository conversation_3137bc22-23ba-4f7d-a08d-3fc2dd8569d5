<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component View - Private Share Test</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>👁️ Component View</h1>
            <p>Viewing shared component</p>
        </header>

        <div class="auth-status" id="authStatus">
            <span id="userInfo">Not logged in</span>
            <button id="logoutBtn" style="display: none;" onclick="logout()">Logout</button>
        </div>

        <div class="card">
            <div id="loadingStep">
                <div style="text-align: center; padding: 40px;">
                    <div class="loading" style="width: 40px; height: 40px; margin: 0 auto 20px;"></div>
                    <p>Loading component...</p>
                </div>
            </div>

            <div id="componentContent" style="display: none;">
                <div id="accessTypeIndicator"></div>
                
                <div class="component-header">
                    <h2 id="componentTitle">Component Title</h2>
                    <div class="component-meta" id="componentMeta"></div>
                </div>

                <div class="component-details">
                    <div id="componentDescription"></div>
                    <div id="shareInfo"></div>
                </div>

                <div class="component-actions">
                    <h3>🛠️ Available Actions</h3>
                    <div class="button-group" id="actionButtons">
                        <!-- Actions will be populated based on access_controls -->
                    </div>
                </div>

                <div class="component-preview">
                    <h3>🎨 Component Preview</h3>
                    <div id="previewContainer">
                        <div style="background: #f7fafc; border: 2px dashed #cbd5e0; padding: 40px; text-align: center; border-radius: 8px;">
                            <p>📄 Component preview would be displayed here</p>
                            <p style="color: #718096; font-size: 0.9rem;">In a real implementation, this would show the actual component code, preview, or interactive demo</p>
                        </div>
                    </div>
                </div>

                <div class="success-message" style="background: #f0fff4; border: 1px solid #9ae6b4; padding: 20px; border-radius: 8px; margin-top: 20px;">
                    <h3 style="color: #22543d; margin-bottom: 10px;">🎉 Success!</h3>
                    <p style="color: #22543d;">You have successfully accessed this shared component!</p>
                    <p style="color: #22543d; margin-top: 10px;">
                        <strong>Next steps:</strong> Check out more shared components in your 
                        <a href="shared-with-me.html" style="color: #38a169; text-decoration: underline;">Shared with Me</a> section.
                    </p>
                </div>
            </div>

            <div id="errorStep" style="display: none;">
                <div class="access-type requires-payment">
                    ❌ Access Error
                </div>
                <div id="errorMessage"></div>
                <div class="button-group">
                    <button onclick="window.location.href='shared-with-me.html'" class="secondary">📥 Shared with Me</button>
                    <button onclick="window.location.href='index.html'" class="success">🏠 Go Home</button>
                </div>
            </div>
        </div>

        <div class="navigation">
            <div class="button-group">
                <button onclick="window.location.href='shared-with-me.html'">📥 Shared with Me</button>
                <button onclick="window.location.href='index.html'">🏠 Home</button>
            </div>
        </div>

        <div class="logs">
            <h3>📋 Component Logs</h3>
            <div id="logContainer"></div>
            <button onclick="clearLogs()">🗑️ Clear Logs</button>
        </div>
    </div>

    <script src="config.js"></script>
    <script>
        let componentSlug = null;
        let accessToken = null;
        let componentData = null;

        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            checkAuthStatus();
            
            const params = getUrlParams();
            componentSlug = params.slug;
            accessToken = params.token;
            
            if (!componentSlug || !accessToken) {
                showError('Missing component slug or access token');
                return;
            }
            
            loadComponent();
        });

        function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');
            
            if (token && userInfo) {
                const user = JSON.parse(userInfo);
                document.getElementById('userInfo').textContent = `Logged in as: ${user.first_name} ${user.last_name}`;
                document.getElementById('logoutBtn').style.display = 'inline-block';
            } else {
                document.getElementById('userInfo').textContent = 'Not logged in';
                document.getElementById('logoutBtn').style.display = 'none';
            }
        }

        function logout() {
            clearUserSession();
            checkAuthStatus();
            window.location.href = 'index.html';
        }

        async function loadComponent() {
            try {
                logMessage(`Loading component: ${componentSlug} with token: ${accessToken}`, 'info');
                
                const { response, data } = await apiCall(`/component/private/${componentSlug}?token=${accessToken}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`
                    }
                });

                document.getElementById('loadingStep').style.display = 'none';

                if (data.status === 200) {
                    componentData = data.data;
                    logMessage('Component loaded successfully', 'success');
                    displayComponent();
                } else {
                    showError(data.message || 'Failed to load component');
                }
            } catch (error) {
                document.getElementById('loadingStep').style.display = 'none';
                handleError(error, 'loading component');
                showError('Failed to load component');
            }
        }

        function displayComponent() {
            // Show access type
            const accessTypeIndicator = document.getElementById('accessTypeIndicator');
            let accessTypeClass = 'public-link';
            let accessTypeText = '🔗 Public Link Access';
            
            if (componentData.share_type === 'private_invite') {
                accessTypeClass = 'private-invite';
                accessTypeText = '📧 Private Invitation';
            }
            
            if (componentData.requires_payment) {
                accessTypeClass = 'requires-payment';
                accessTypeText += ' (Payment Required)';
            }
            
            accessTypeIndicator.innerHTML = `<div class="access-type ${accessTypeClass}">${accessTypeText}</div>`;
            
            // Component header
            document.getElementById('componentTitle').textContent = componentData.title;
            
            const metaInfo = [];
            if (componentData.component_type) metaInfo.push(`Type: ${componentData.component_type}`);
            if (componentData.created_at) metaInfo.push(`Created: ${formatDate(componentData.created_at)}`);
            if (componentData.is_paid) metaInfo.push('💳 Paid Component');
            else metaInfo.push('🆓 Free Component');
            
            document.getElementById('componentMeta').innerHTML = metaInfo.join(' • ');
            
            // Component description
            document.getElementById('componentDescription').innerHTML = `
                <h3>📝 Description</h3>
                <p>${componentData.description || 'No description available'}</p>
            `;
            
            // Share info
            if (componentData.shared_by) {
                document.getElementById('shareInfo').innerHTML = `
                    <h3>📤 Share Information</h3>
                    <p><strong>Shared by:</strong> ${componentData.shared_by.first_name} ${componentData.shared_by.last_name}</p>
                    ${componentData.personal_message ? `<p><strong>Message:</strong> <em>"${componentData.personal_message}"</em></p>` : ''}
                    <p><strong>Access Controls:</strong> ${componentData.access_controls?.join(', ') || 'None specified'}</p>
                `;
            }
            
            // Action buttons based on access controls
            const actionButtons = document.getElementById('actionButtons');
            const controls = componentData.access_controls || [];
            
            let buttonsHtml = '';
            
            if (controls.includes('download')) {
                buttonsHtml += '<button onclick="downloadComponent()" class="success">📥 Download</button>';
            }
            
            if (controls.includes('fork')) {
                buttonsHtml += '<button onclick="forkComponent()" class="secondary">🍴 Fork</button>';
            }
            
            if (controls.includes('view')) {
                buttonsHtml += '<button onclick="viewCode()" class="secondary">👁️ View Code</button>';
            }
            
            if (!buttonsHtml) {
                buttonsHtml = '<p style="color: #718096;">No specific actions available - view only access</p>';
            }
            
            actionButtons.innerHTML = buttonsHtml;
            
            document.getElementById('componentContent').style.display = 'block';
        }

        function showError(message) {
            document.getElementById('loadingStep').style.display = 'none';
            document.getElementById('componentContent').style.display = 'none';
            document.getElementById('errorMessage').innerHTML = `<p>${message}</p>`;
            document.getElementById('errorStep').style.display = 'block';
        }

        function downloadComponent() {
            logMessage('Download action triggered', 'info');
            alert('Download functionality would be implemented here.\n\nThis would typically:\n1. Call the download API\n2. Generate a ZIP file\n3. Trigger browser download');
        }

        function forkComponent() {
            logMessage('Fork action triggered', 'info');
            alert('Fork functionality would be implemented here.\n\nThis would typically:\n1. Create a copy in user\'s account\n2. Set up Git repository\n3. Redirect to the forked component');
        }

        function viewCode() {
            logMessage('View code action triggered', 'info');
            alert('View code functionality would be implemented here.\n\nThis would typically:\n1. Open code viewer\n2. Display syntax-highlighted code\n3. Allow code browsing');
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }
    </script>
</body>
</html>
