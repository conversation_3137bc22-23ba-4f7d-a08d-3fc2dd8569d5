<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Share Component - Private Share Test</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🔗 Share Component</h1>
            <p>Share your components privately or create public links</p>
        </header>

        <div class="auth-status" id="authStatus">
            <span id="userInfo">Not logged in</span>
            <button id="logoutBtn" style="display: none;" onclick="logout()">Logout</button>
        </div>

        <div class="card">
            <div id="loginRequired" style="display: none;">
                <h3>🔑 Login Required</h3>
                <p>Please login to share components.</p>
                <div class="button-group">
                    <button onclick="window.location.href='login.html'" class="success">🔑 Login</button>
                    <button onclick="window.location.href='signup.html'" class="secondary">📝 Sign Up</button>
                </div>
            </div>

            <div id="shareForm">
                <div class="input-group">
                    <label for="componentId">Component ID:</label>
                    <input type="text" id="componentId" placeholder="Enter component ID (e.g., 67c817b678249aea18c4ca79)" required>
                    <small style="color: #718096;">For testing, use any valid MongoDB ObjectId format</small>
                </div>

                <div class="share-type-selector">
                    <h3>📤 Share Type</h3>
                    <div class="input-group">
                        <label>
                            <input type="radio" name="shareType" value="private" checked onchange="toggleShareType()">
                            📧 Private Email Invitation
                        </label>
                        <label>
                            <input type="radio" name="shareType" value="public" onchange="toggleShareType()">
                            🔗 Public Shareable Link
                        </label>
                    </div>
                </div>

                <!-- Private Share Section -->
                <div id="privateShareSection">
                    <h3>📧 Private Email Invitation</h3>
                    <div class="input-group">
                        <label for="emails">Email Addresses:</label>
                        <textarea id="emails" placeholder="Enter email addresses (one per line or comma-separated)" rows="4"></textarea>
                        <small style="color: #718096;">Example: <EMAIL>, <EMAIL></small>
                    </div>
                    <div class="input-group">
                        <label for="personalMessage">Personal Message (Optional):</label>
                        <textarea id="personalMessage" placeholder="Add a personal message..." rows="3"></textarea>
                    </div>
                </div>

                <!-- Public Link Section -->
                <div id="publicLinkSection" style="display: none;">
                    <h3>🔗 Public Shareable Link</h3>
                    <div class="input-group">
                        <label for="linkName">Link Name:</label>
                        <input type="text" id="linkName" placeholder="Enter a name for this link" required>
                        <small style="color: #718096;">Example: "Premium Component Share"</small>
                    </div>
                </div>

                <!-- Common Settings -->
                <div class="common-settings">
                    <h3>⚙️ Access Settings</h3>
                    
                    <div class="input-group">
                        <label for="accessDuration">Access Duration:</label>
                        <select id="accessDuration" onchange="toggleDurationDays()">
                            <option value="undefined">No Expiration</option>
                            <option value="days">Custom Days</option>
                        </select>
                    </div>

                    <div class="input-group" id="durationDaysGroup" style="display: none;">
                        <label for="durationDays">Duration (Days):</label>
                        <input type="number" id="durationDays" min="1" max="365" placeholder="30">
                    </div>

                    <div class="input-group">
                        <label>Access Controls:</label>
                        <div class="checkbox-group">
                            <label><input type="checkbox" value="view" checked disabled> 👁️ View (Always included)</label>
                            <label><input type="checkbox" value="download" id="downloadAccess"> 📥 Download</label>
                            <label><input type="checkbox" value="fork" id="forkAccess"> 🍴 Fork</label>
                        </div>
                    </div>
                </div>

                <div class="button-group">
                    <button onclick="shareComponent()" id="shareBtn" class="success">🚀 Share Component</button>
                    <button onclick="clearForm()" class="secondary">🗑️ Clear Form</button>
                </div>
            </div>

            <div id="shareResult" style="display: none;">
                <h3>✅ Share Successful!</h3>
                <div id="resultContent"></div>
                <div class="button-group">
                    <button onclick="resetForm()" class="success">🔄 Share Another</button>
                    <button onclick="window.location.href='manage-shares.html'" class="secondary">📤 Manage Shares</button>
                </div>
            </div>
        </div>

        <div class="navigation">
            <div class="button-group">
                <button onclick="window.location.href='index.html'">🏠 Home</button>
                <button onclick="window.location.href='manage-shares.html'">📤 Manage My Shares</button>
                <button onclick="window.location.href='shared-with-me.html'">📥 Shared with Me</button>
            </div>
        </div>

        <div class="logs">
            <h3>📋 Share Logs</h3>
            <div id="logContainer"></div>
            <button onclick="clearLogs()">🗑️ Clear Logs</button>
        </div>
    </div>

    <script src="config.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            checkAuthStatus();
        });

        function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');
            
            if (token && userInfo) {
                const user = JSON.parse(userInfo);
                document.getElementById('userInfo').textContent = `Logged in as: ${user.first_name} ${user.last_name}`;
                document.getElementById('logoutBtn').style.display = 'inline-block';
                document.getElementById('loginRequired').style.display = 'none';
                document.getElementById('shareForm').style.display = 'block';
            } else {
                document.getElementById('userInfo').textContent = 'Not logged in';
                document.getElementById('logoutBtn').style.display = 'none';
                document.getElementById('loginRequired').style.display = 'block';
                document.getElementById('shareForm').style.display = 'none';
            }
        }

        function logout() {
            clearUserSession();
            checkAuthStatus();
        }

        function toggleShareType() {
            const shareType = document.querySelector('input[name="shareType"]:checked').value;
            const privateSection = document.getElementById('privateShareSection');
            const publicSection = document.getElementById('publicLinkSection');
            
            if (shareType === 'private') {
                privateSection.style.display = 'block';
                publicSection.style.display = 'none';
            } else {
                privateSection.style.display = 'none';
                publicSection.style.display = 'block';
            }
        }

        function toggleDurationDays() {
            const accessDuration = document.getElementById('accessDuration').value;
            const durationDaysGroup = document.getElementById('durationDaysGroup');
            
            if (accessDuration === 'days') {
                durationDaysGroup.style.display = 'block';
            } else {
                durationDaysGroup.style.display = 'none';
            }
        }

        async function shareComponent() {
            const componentId = document.getElementById('componentId').value.trim();
            const shareType = document.querySelector('input[name="shareType"]:checked').value;
            
            if (!componentId) {
                logMessage('Please enter a component ID', 'error');
                return;
            }

            if (!isAuthenticated()) {
                logMessage('Please login to share components', 'error');
                return;
            }

            const hideLoading = showLoading(document.getElementById('shareBtn'));

            try {
                let result;
                
                if (shareType === 'private') {
                    result = await sharePrivately(componentId);
                } else {
                    result = await createPublicLink(componentId);
                }

                hideLoading();
                
                if (result.success) {
                    showShareResult(result);
                }
            } catch (error) {
                hideLoading();
                handleError(error, 'sharing component');
            }
        }

        async function sharePrivately(componentId) {
            const emails = document.getElementById('emails').value.trim();
            const personalMessage = document.getElementById('personalMessage').value.trim();
            const accessDuration = document.getElementById('accessDuration').value;
            const durationDays = document.getElementById('durationDays').value;
            
            if (!emails) {
                logMessage('Please enter at least one email address', 'error');
                return { success: false };
            }

            // Parse emails
            const emailList = emails.split(/[,\n]/).map(email => email.trim()).filter(email => email);
            
            // Validate emails
            for (const email of emailList) {
                if (!isValidEmail(email)) {
                    logMessage(`Invalid email address: ${email}`, 'error');
                    return { success: false };
                }
            }

            // Get access controls
            const accessControls = ['view']; // Always include view
            if (document.getElementById('downloadAccess').checked) accessControls.push('download');
            if (document.getElementById('forkAccess').checked) accessControls.push('fork');

            const requestBody = {
                emails: emailList,
                personal_message: personalMessage,
                access_duration: accessDuration,
                access_controls: accessControls
            };

            if (accessDuration === 'days' && durationDays) {
                requestBody.duration_days = parseInt(durationDays);
            }

            logMessage(`Sharing component privately with ${emailList.length} recipients`, 'info');

            const { response, data } = await apiCall(`/v1/component/${componentId}/share-privately`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                    'token': localStorage.getItem('authToken')
                },
                body: JSON.stringify(requestBody)
            });

            if (data.status === 200) {
                logMessage('Component shared successfully!', 'success');
                return {
                    success: true,
                    type: 'private',
                    data: data.data,
                    emails: emailList
                };
            } else {
                logMessage(data.message || 'Failed to share component', 'error');
                return { success: false };
            }
        }

        async function createPublicLink(componentId) {
            const linkName = document.getElementById('linkName').value.trim();
            const accessDuration = document.getElementById('accessDuration').value;
            const durationDays = document.getElementById('durationDays').value;
            
            if (!linkName) {
                logMessage('Please enter a link name', 'error');
                return { success: false };
            }

            // Get access controls
            const accessControls = ['view']; // Always include view
            if (document.getElementById('downloadAccess').checked) accessControls.push('download');
            if (document.getElementById('forkAccess').checked) accessControls.push('fork');

            const requestBody = {
                link_name: linkName,
                access_duration: accessDuration,
                access_controls: accessControls
            };

            if (accessDuration === 'days' && durationDays) {
                requestBody.duration_days = parseInt(durationDays);
            }

            logMessage(`Creating public shareable link: ${linkName}`, 'info');

            const { response, data } = await apiCall(`/v1/component/${componentId}/generate-link`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                    'token': localStorage.getItem('authToken')
                },
                body: JSON.stringify(requestBody)
            });

            if (data.status === 200) {
                logMessage('Public link created successfully!', 'success');
                return {
                    success: true,
                    type: 'public',
                    data: data.data
                };
            } else {
                logMessage(data.message || 'Failed to create public link', 'error');
                return { success: false };
            }
        }

        function showShareResult(result) {
            document.getElementById('shareForm').style.display = 'none';
            
            let resultHtml = '';
            
            if (result.type === 'private') {
                resultHtml = `
                    <div class="success-info">
                        <p><strong>📧 Private invitations sent to:</strong></p>
                        <ul>
                            ${result.emails.map(email => `<li>${email}</li>`).join('')}
                        </ul>
                        <p><strong>Summary:</strong></p>
                        <ul>
                            <li>Total: ${result.data.summary.total}</li>
                            <li>Successful: ${result.data.summary.successful}</li>
                            <li>Invalid: ${result.data.summary.invalid.length}</li>
                            <li>Duplicates: ${result.data.summary.duplicates.length}</li>
                        </ul>
                    </div>
                `;
            } else {
                resultHtml = `
                    <div class="success-info">
                        <p><strong>🔗 Public shareable link created:</strong></p>
                        <div class="link-result" style="background: #f7fafc; padding: 15px; border-radius: 8px; margin: 10px 0;">
                            <p><strong>Link Name:</strong> ${result.data.share.link_name}</p>
                            <p><strong>Shareable URL:</strong></p>
                            <input type="text" value="${result.data.shareableUrl}" readonly style="width: 100%; margin: 5px 0;">
                            <button onclick="copyToClipboard('${result.data.shareableUrl}')" class="secondary">📋 Copy Link</button>
                        </div>
                        <p><strong>Access Token:</strong> ${result.data.share.access_token}</p>
                        <p><strong>Expires:</strong> ${result.data.share.expires_at ? formatDate(result.data.share.expires_at) : 'Never'}</p>
                    </div>
                `;
            }
            
            document.getElementById('resultContent').innerHTML = resultHtml;
            document.getElementById('shareResult').style.display = 'block';
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                logMessage('Link copied to clipboard!', 'success');
            }).catch(() => {
                logMessage('Failed to copy link', 'error');
            });
        }

        function resetForm() {
            document.getElementById('shareResult').style.display = 'none';
            document.getElementById('shareForm').style.display = 'block';
            clearForm();
        }

        function clearForm() {
            document.getElementById('componentId').value = '';
            document.getElementById('emails').value = '';
            document.getElementById('personalMessage').value = '';
            document.getElementById('linkName').value = '';
            document.getElementById('accessDuration').value = 'undefined';
            document.getElementById('durationDays').value = '';
            document.getElementById('downloadAccess').checked = false;
            document.getElementById('forkAccess').checked = false;
            document.querySelector('input[name="shareType"][value="private"]').checked = true;
            toggleShareType();
            toggleDurationDays();
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }
    </script>
</body>
</html>
