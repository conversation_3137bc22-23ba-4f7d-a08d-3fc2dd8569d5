// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');
const mongoose = require('mongoose');
const { notificationTypes, filterTypes, componentState } = require('../../config/component.constant');

// Service declaration
const { ReS, escapeRegex } = require('../../services/general.helper');
const { saveAndSendNotification } = require('../../services/notification_helper.service');

// Models declaration
const { Users } = require('../../models/users.model');
const { CreatorFollow } = require('../../models/creator_follow.model');
const { Components } = require('../../models/component.model');

/**
 * Follow a creator
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Response with status and message
 */
async function followCreator(req, res) {
    try {
        const username = req.params.username;
        const follower_id = req.session._id;

        // Validate username
        if (!username || typeof username !== 'string') {
            return ReS(res, constants.bad_request_code, 'Invalid username provided.');
        }

        // Find the creator by username
        const creator = await Users.findOne({
            username: username,
            is_active: true,
            is_deleted: { $ne: true }
        }).lean();

        if (!creator) {
            return ReS(res, constants.bad_request_code, 'The user does not exist or is currently inactive.');
        }

        // Prevent self-following
        if (creator._id.toString() === follower_id.toString()) {
            return ReS(res, constants.bad_request_code, 'You cannot follow yourself.');
        }

        // Check if already following
        const existingFollow = await CreatorFollow.findOne({
            creator_id: creator._id,
            follower_id: follower_id
        }).lean();

        if (existingFollow) {
            return ReS(res, constants.bad_request_code, 'You are already following this user.');
        }

        // Create new follow relationship
        const newFollow = new CreatorFollow({
            creator_id: creator._id,
            follower_id: follower_id
        });

        await newFollow.save();

        // Get follower details for notification
        const follower = await Users.findOne({ _id: follower_id }, 'username first_name last_name').lean();
        const followerName = follower ?
            (follower.first_name && follower.last_name ?
                `${follower.first_name} ${follower.last_name}` :
                follower.username) :
            'Someone';

        // Send notification to creator
        await saveAndSendNotification({
            title: 'New Follower',
            message: `${followerName} started following you.`,
            type: notificationTypes.NEW_FOLLOWER,
            entity: {
                follower_id: follower_id,
                follower_username: follower ? follower.username : null
            },
            user_id: creator._id,
            created_by: follower_id
        }, [creator._id]);

        return ReS(res, constants.success_code, 'Successfully followed creator.');
    } catch (err) {
        logger.error('Error following creator:', err);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

/**
 * Unfollow a creator
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Response with status and message
 */
async function unfollowCreator(req, res) {
    try {
        const username = req.params.username;
        const follower_id = req.session._id;

        // Validate username
        if (!username || typeof username !== 'string') {
            return ReS(res, constants.bad_request_code, 'Invalid username provided.');
        }

        // Find the creator by username
        const creator = await Users.findOne({
            username: username,
            is_creator: true
        }).lean();

        if (!creator) {
            return ReS(res, constants.resource_not_found, 'Creator not found.');
        }

        // Find and delete the follow relationship
        const deletedFollow = await CreatorFollow.findOneAndDelete({
            creator_id: creator._id,
            follower_id: follower_id
        });

        if (!deletedFollow) {
            return ReS(res, constants.bad_request_code, 'You are not following this creator.');
        }

        return ReS(res, constants.success_code, 'Successfully unfollowed creator.');
    } catch (err) {
        logger.error('Error unfollow creator:', err);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

/**
 * Get creator's followers with pagination
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Response with followers list and pagination info
 */
async function getFollowers(req, res) {
    try {
        const creatorId = req.session._id;
        // Validate and parse pagination parameters
        const limit = Math.max(1, parseInt(req.body.limit || 10, 10));
        const skip = Math.max(0, parseInt(req.body.skip || 0, 10));

        // Get search text
        const searchText = req.body.searchText || '';

        // Default sort by most recent followers
        let sort = { created_at: -1 };

        // Check if sort_by property exists in the request body and set sort criteria accordingly
        if (req.body.sort_by) {
            if (req.body.sort_by === filterTypes.MOST_LIKED) {
                // Sort by total_likes in descending order
                sort = {
                    total_likes: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.MOST_POPULAR) {
                // Sort by followers_count in descending order
                sort = {
                    followers_count: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_ADDITIONS) {
                // Sort by created_at in descending order (default)
                sort = {
                    created_at: -1
                };
            }
        }

        // Count total followers (without search filter)
        const totalFollowers = await CreatorFollow.countDocuments({
            creator_id: creatorId
        });

        // Get all follower IDs for this creator
        const followerIds = (await CreatorFollow.distinct('follower_id', { creator_id: creatorId })).map((id) => new mongoose.Types.ObjectId(id));

        // Build conditions for Users query
        const conditions = {
            _id: {
                $in: followerIds
            }
        };

        // Add search conditions if search text is provided
        if (searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(searchText);
            conditions['$or'] = [{
                'username': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }, {
                'first_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }, {
                'last_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        // Count filtered followers
        const filteredFollowers = await Users.countDocuments(conditions);

        // Get followers with pagination
        const pipelines = [
            {
                $match: conditions
            },
            // Get published files count
            {
                $lookup: {
                    from: 'components',
                    let: { follower_id: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$created_by_user', '$$follower_id'] },
                                        { $eq: ['$component_state', componentState.PUBLISHED] }
                                    ]
                                }
                            }
                        },
                        {
                            $count: 'total'
                        }
                    ],
                    as: 'published_files'
                }
            },
            // Check if the logged-in user follows each follower
            {
                $lookup: {
                    from: 'creator_follows',
                    let: { follower_id: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$creator_id', '$$follower_id'] },
                                        { $eq: ['$follower_id', new mongoose.Types.ObjectId(creatorId)] }
                                    ]
                                }
                            }
                        }
                    ],
                    as: 'followingStatus'
                }
            },
            // Get follow relationship data (created_at)
            {
                $lookup: {
                    from: 'creator_follows',
                    let: { follower_id: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$follower_id', '$$follower_id'] },
                                        { $eq: ['$creator_id', new mongoose.Types.ObjectId(creatorId)] }
                                    ]
                                }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                created_at: 1
                            }
                        }
                    ],
                    as: 'followData'
                }
            },
            {
                $project: {
                    _id: 1,
                    username: 1,
                    first_name: 1,
                    last_name: 1,
                    avatar: 1,
                    is_active: 1,
                    is_creator: 1,
                    followers_count: 1,
                    total_likes: 1,
                    created_at: { $arrayElemAt: ['$followData.created_at', 0] },
                    is_following_back: {
                        $cond: [
                            { $gt: [{ $size: '$followingStatus' }, 0] },
                            true,
                            false
                        ]
                    },
                    published_files: {
                        $cond: [
                            { $gt: [{ $size: '$published_files' }, 0] },
                            { $arrayElemAt: ['$published_files.total', 0] },
                            0
                        ]
                    }
                }
            },
            {
                $sort: sort
            },
            {
                $skip: skip
            },
            {
                $limit: limit
            }
        ];

        const followers = await Users.aggregate(pipelines);

        const responseObj = {
            recordsTotal: totalFollowers,
            recordsFiltered: filteredFollowers,
            list: followers
        };

        return ReS(res, constants.success_code, 'Followers fetched successfully.', responseObj);
    } catch (err) {
        logger.error('Error getting followers:', err);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

/**
 * Get user's following list with pagination
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Response with following list and pagination info
 */
async function getFollowing(req, res) {
    try {
        const follower_id = req.session._id;

        // Validate and parse pagination parameters
        const limit = Math.max(1, parseInt(req.body.limit || 10, 10));
        const skip = Math.max(0, parseInt(req.body.skip || 0, 10));

        // Default sort by most recent following
        let sort = { created_at: -1 };

        // Check if sort_by property exists in the request body and set sort criteria accordingly
        if (req.body.sort_by) {
            if (req.body.sort_by === filterTypes.MOST_LIKED) {
                // Sort by total_likes in descending order
                sort = {
                    total_likes: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.MOST_POPULAR) {
                // Sort by followers_count in descending order
                sort = {
                    followers_count: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_ADDITIONS) {
                // Sort by created_at in descending order (default)
                sort = {
                    created_at: -1
                };
            }
        }

        // Count total following
        const totalFollowing = await CreatorFollow.countDocuments({
            follower_id: follower_id
        });


        const followingIds = (await CreatorFollow.distinct('creator_id', { follower_id: follower_id })).map((id) => new mongoose.Types.ObjectId(id));

        const conditions = {
            _id: {
                $in: followingIds
            }
        };

        // Get search text
        const searchText = req.body.searchText || '';

        if (searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(searchText);
            conditions['$or'] = [{
                'username': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }, {
                'first_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }, {
                'last_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        const filteredFollowing = await Users.countDocuments(conditions);

        // Get following with pagination
        const pipelines = [
            {
                $match: conditions
            },
            // Get published files count
            {
                $lookup: {
                    from: 'components',
                    let: { creator_id: '$_id' }, // using _id since we're querying Users collection
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$created_by_user', '$$creator_id'] },
                                        { $eq: ['$component_state', componentState.PUBLISHED] }
                                    ]
                                }
                            }
                        },
                        {
                            $count: 'total'
                        }
                    ],
                    as: 'published_files'
                }
            },
            // Get follow relationship data (created_at)
            {
                $lookup: {
                    from: 'creator_follows',
                    let: { creator_id: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$creator_id', '$$creator_id'] },
                                        { $eq: ['$follower_id', new mongoose.Types.ObjectId(follower_id)] }
                                    ]
                                }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                created_at: 1
                            }
                        }
                    ],
                    as: 'followData'
                }
            },
            {
                $project: {
                    _id: 1,
                    username: 1,
                    first_name: 1,
                    last_name: 1,
                    avatar: 1,
                    is_creator: 1,
                    biography: 1,
                    is_active: 1,
                    followers_count: 1,
                    total_likes: 1,
                    created_at: { $arrayElemAt: ['$followData.created_at', 0] },
                    published_files: {
                        $cond: [
                            { $gt: [{ $size: '$published_files' }, 0] },
                            { $arrayElemAt: ['$published_files.total', 0] },
                            0
                        ]
                    }
                }
            },
            {
                $sort: sort
            },
            {
                $skip: skip
            },
            {
                $limit: limit
            }
        ];

        const following = await Users.aggregate(pipelines);

        const responseObj = {
            recordsTotal: totalFollowing,
            recordsFiltered: filteredFollowing,
            list: following
        };

        return ReS(res, constants.success_code, 'Following list fetched successfully.', responseObj);
    } catch (err) {
        console.error('Error getting following list:', err);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

/**
 * Check if user is following a creator
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Response with follow status
 */
async function checkFollowStatus(req, res) {
    try {
        const username = req.params.username;
        const follower_id = req.session._id;

        // Validate username
        if (!username || typeof username !== 'string') {
            return ReS(res, constants.bad_request_code, 'Invalid username provided.');
        }

        // Find the creator by username
        const creator = await Users.findOne({
            username: username,
            is_creator: true,
            is_active: true,
            is_deleted: { $ne: true }
        }).lean();

        if (!creator) {
            return ReS(res, constants.bad_request_code, 'The creator does not exist or is currently inactive.');
        }

        // Check if following
        const isFollowing = await CreatorFollow.exists({
            creator_id: creator._id,
            follower_id: follower_id
        });

        return ReS(res, constants.success_code, 'Follow status fetched.', { is_following: !!isFollowing });
    } catch (err) {
        logger.error('Error checking follow status:', err);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

/**
 * Get creator's followers count
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Response with followers count
 */
async function getFollowersCount(req, res) {
    try {
        // Find the creator by username
        const creator = await Users.findOne({
            _id: req.session._id,
            is_creator: true,
            is_active: true,
            is_deleted: { $ne: true }
        }).lean();

        if (!creator) {
            return ReS(res, constants.bad_request_code, 'The creator does not exist or is currently inactive.');
        }

        // Get followers count directly from the user model
        return ReS(res, constants.success_code, 'Followers count fetched successfully.', { count: creator.followers_count || 0 });
    } catch (err) {
        logger.error('Error getting followers count:', err);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

/**
 * Get user's following count
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Response with following count
 */
async function getFollowingCount(req, res) {
    try {
        // Find the user by username
        const user = await Users.findOne({
            _id: req.session._id,
            is_active: true,
            is_deleted: { $ne: true }
        }).lean();

        if (!user) {
            return ReS(res, constants.resource_not_found, 'User not found or inactive.');
        }

        // Get following count directly from the user model
        return ReS(res, constants.success_code, 'Following count fetched successfully.', { count: user.following_count || 0 });
    } catch (err) {
        logger.error('Error getting following count:', err);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

module.exports = {
    followCreator,
    unfollowCreator,
    getFollowers,
    getFollowing,
    checkFollowStatus,
    getFollowersCount,
    getFollowingCount
};
