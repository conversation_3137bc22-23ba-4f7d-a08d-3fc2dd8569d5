
// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

// Service declaration
const { ReS, sendError } = require('../../services/general.helper');

const { webhookEvents, gitlabProductionBranch } = require('./../../config/gitlab.constant');
const { transactionStatus, unlockTypes } = require('./../../config/component.constant');
const { fetchAndUpdateRepositoryLanguages } = require('../../services/language.service');
const { fetchAndUpdateProjectStorageSize, fetchAndSyncUserUsedStorage } = require('../../services/gitlab_webhook.service');
const { processComponentFiatPurchase } = require('../../services/mpc_balance.service');

const { GitlabRepository } = require('../../models/gitlab_repository.model');
const { PaymentTransactions } = require('../../models/payment_transactions.model');
const { ComponentUnlockHistory } = require('../../models/component_unlock_history.model');
const { ComponentSales } = require('../../models/component_sales.model');
const { Components } = require('../../models/component.model');

async function gitlabMergeRequestEventWebhook(req, res) {
    try {
        const { event_type, object_attributes, project } = req.body;

        console.log(`Web hook call received for Event => ${event_type} for project ${project.id}`);

        if (event_type === webhookEvents.MERGE_REQUEST) {
            const action = object_attributes.action;
            const targetBranch = object_attributes.target_branch;
            const state = object_attributes.state;

            if (action === 'merge' && targetBranch === gitlabProductionBranch && state === 'merged') {
                await fetchAndUpdateRepositoryLanguages(project.id, 'webhook');
            }
        }
        return ReS(res, constants.success_code, 'Gitlab Webhook called');
    } catch (err) {
        logger.error(`Error at Front Controller gitlabMergeRequestEventWebhook: ${err}`);
        return sendError(res, err);
    }
}

async function gitlabRepositoryUpdateEventWebhook(req, res) {
    try {
        const { event_name, project_id } = req.body;

        console.log(`Web hook call received for Event => ${event_name} for project_id => ${project_id}`);

        if (event_name === webhookEvents.REPOSITORY_UPDATE) {
            await GitlabRepository.updateOne({
                project_id: project_id
            }, {
                $set: {
                    last_activity_at: new Date()
                }
            });
            // Fetch and update gitlab project storage size
            await fetchAndUpdateProjectStorageSize(project_id);
            await fetchAndSyncUserUsedStorage(project_id);
            console.log(`Updated repository last_activity_at date for project => ${project_id}`);
        }
        return ReS(res, constants.success_code, 'Gitlab Webhook called');
    } catch (err) {
        logger.error(`Error at Front Controller gitlabRepositoryUpdateEventWebhook: ${err}`);
        return sendError(res, err);
    }
}

async function razorpayCapturedWebHook(req, res) {
    try {
        const { order_id } = req.body;

        const pendingOrder = await PaymentTransactions.findOne({ order_id: order_id, status: transactionStatus.PENDING }).lean();

        if (!pendingOrder) {
            return ReS(res, constants.bad_request_code, `Invalid webhook received for ${order_id}`);
        }

        // Find the component data
        const component = await Components.findOne({ _id: pendingOrder.component_id }).lean();

        // Process Purchase
        const { deductFromUser, creditToAdmin, creditToCreator, amountToPay, fiatLocked, pointsLocked } = await processComponentFiatPurchase(order_id);
        // Prepare unlock history
        const unlockHistory = {
            unlock_by: pendingOrder.user_id,
            component_id: component._id,
            component_name: component.title,
            component_slug: component.slug,
            price: component.purchase_price,
            is_active: true,
            expense: deductFromUser,
            mpn_parity: component.mpn_parity
        };

        // If the unlock type is limited, set the expiry date
        if (component.unlock_type === unlockTypes.LIMITED) {
            const currentDate = moment();
            const expiredOn = currentDate.add(component.unlock_duration, 'days');
            unlockHistory['expired_on'] = expiredOn;
        }

        // Create unlock history
        await ComponentUnlockHistory.create(unlockHistory);

        const componentSalesObj = {
            component_id: component?._id,
            creator_id: component?.created_by_user,
            purchaser_id: pendingOrder.user_id,
            mpn_parity: component?.mpn_parity || 0,
            purchase_price: component?.purchase_price,
            item_price: component?.item_price,
            buyer_fee: component?.buyer_fee,
            distribution: {
                to_creator: creditToCreator,
                to_admin: creditToAdmin,
                total_credited: {
                    fiat: creditToCreator?.fiat + creditToAdmin?.fiat,
                    mpn_points: creditToCreator?.mpn_points + creditToAdmin?.mpn_points
                }
            },
            payment_breakdown: {
                used_mpn_points: pointsLocked,
                used_fiat_from_wallet: fiatLocked,
                paid_externally: amountToPay
            }
        };
        // Create Component sales history
        await ComponentSales.create(componentSalesObj);
        return ReS(res, constants.success_code, 'Razorpay Webhook called');
    } catch (err) {
        logger.error(`Error at Front Controller razorpayCapturedWebHook: ${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    gitlabMergeRequestEventWebhook,
    gitlabRepositoryUpdateEventWebhook,
    razorpayCapturedWebHook
};  