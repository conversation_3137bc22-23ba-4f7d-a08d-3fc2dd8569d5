# 🔗 Manage Shares Frontend Features

## **📋 Overview**

The new Manage Shares page provides a comprehensive interface for users to manage all their private shares and shareable links. It includes four main sections:

1. **📊 Overview** - Statistics and summary
2. **👥 Private Shares** - Email-based invites sent to specific users
3. **🔗 Shareable Links** - Public links that can be shared with anyone
4. **📥 Shared With Me** - Components shared by others

## **🎯 Key Features Implemented**

### **1. Overview Dashboard**
- **Total Shares**: Combined count of private shares and shareable links
- **Active Links**: Count of currently active shareable links
- **Shared With Me**: Count of components shared by others
- **Total Access Count**: Sum of all access counts across all links

### **2. Private Shares Management**
- **View All Private Shares**: List all email-based invites sent
- **Share Details**: Shows recipient email, component, access controls, status
- **Revoke Shares**: Ability to revoke access for specific shares
- **Create New Shares**: Modal to create new private shares (placeholder)

### **3. Shareable Links Management**
- **View All Links**: List all generated shareable links
- **Link Status**: Shows PENDING, ACCEPTED, EXPIRED, or REVOKED status
- **Copy Link**: One-click copy to clipboard
- **Refresh Link**: Generate new token (invalidates old link)
- **Delete Link**: Permanently remove the link
- **Generate New Link**: Create new shareable links with custom settings

### **4. Shared With Me**
- **View Received Shares**: Components shared by other users
- **Access Components**: Direct links to open shared components
- **Share Details**: Shows who shared, when, and access permissions

## **🔧 Backend APIs Added**

### **New Routes:**
```javascript
// Revoke any type of share
POST /api/front/component/revoke-share/:shareId

// Delete shareable link permanently
DELETE /api/front/component/delete-share/:shareId

// Refresh shareable link (generate new token)
POST /api/front/component/refresh-share-link/:shareId
```

### **New Service Functions:**
```javascript
// Delete a shareable link permanently
async function deleteShareLink(shareId, userId)

// Refresh a shareable link (generate new token)
async function refreshShareableLinkNew(shareId, userId)
```

## **🔄 Auto-Expiration & Link Management**

### **✅ Auto-Expiration Implemented:**
When you generate a **new shareable link** for the same component:
- **Previous active links are automatically expired** 🔄
- **Only one active link exists per component per user** 🔒
- **Previous links are marked as 'revoked' with reason 'Auto-expired due to new link generation'** 📝
- **This ensures security and prevents link proliferation** 🛡️

### **Refresh Link Behavior:**
When you **refresh an existing link**:
- **Old token becomes invalid** immediately
- **New token is generated** for the same link
- **Status resets to 'pending'**
- **User assignment is cleared** (if it was claimed)
- **Access count resets to 0**

### **Auto-Expiration Logic:**
```javascript
// Before creating new link, expire previous links for the same component
await ComponentPrivateShares.updateMany(
    {
        component_id: componentId,
        shared_by: sharedBy,
        access_type: 'by_link',
        is_active: true,
        status: { $in: ['pending', 'accepted'] }
    },
    {
        $set: {
            is_active: false,
            status: 'revoked',
            updated_at: new Date(),
            revoked_reason: 'Auto-expired due to new link generation'
        }
    }
);
```

## **📊 Share Status Logic**

### **Status Types:**
1. **PENDING** - Link created but not yet accessed by any user
2. **ACCEPTED** - Link has been accessed and claimed by a user
3. **EXPIRED** - Link has passed its expiration date
4. **REVOKED** - Link has been manually deactivated
5. **AUTO-EXPIRED** - Link was automatically deactivated when a new link was generated

### **Status Determination:**
```javascript
function getShareStatus(share) {
    if (!share.is_active) {
        // Check if it was auto-expired
        if (share.revoked_reason && share.revoked_reason.includes('Auto-expired')) {
            return 'AUTO-EXPIRED';
        }
        return 'REVOKED';
    }
    if (share.expires_at && new Date(share.expires_at) < new Date()) return 'EXPIRED';
    if (share.status === 'accepted') return 'ACCEPTED';
    return 'PENDING';
}
```

## **🎨 Frontend Features**

### **Responsive Design:**
- Mobile-friendly layout
- Collapsible sections on small screens
- Touch-friendly buttons

### **Interactive Elements:**
- **Tab Navigation**: Switch between different sections
- **Modal Forms**: Create new links with custom settings
- **Copy to Clipboard**: One-click link copying
- **Status Badges**: Visual status indicators
- **Action Buttons**: Contextual actions for each share

### **Real-time Updates:**
- Automatic refresh after actions
- Loading states during API calls
- Success/error notifications

## **🔒 Security Features**

### **Access Control:**
- Users can only manage their own shares
- Ownership verification on all operations
- Secure token generation for new links

### **Data Validation:**
- Input validation on all forms
- Parameter sanitization
- Error handling for edge cases

## **📱 Usage Instructions**

### **To Generate a New Link:**
1. Go to "🔗 Shareable Links" tab
2. Click "➕ Generate New Link"
3. Select component, set name and permissions
4. Choose expiration settings
5. Click "Generate Link"

### **To Refresh an Existing Link:**
1. Find the link in "🔗 Shareable Links" tab
2. Click "🔄 Refresh Link" button
3. Confirm the action
4. Old link becomes invalid, new link is generated

### **To Delete a Link:**
1. Find the link in "🔗 Shareable Links" tab
2. Click "🗑️ Delete Link" button
3. Confirm the action
4. Link is permanently removed

### **To Revoke a Private Share:**
1. Go to "👥 Private Shares" tab
2. Find the share to revoke
3. Click "🗑️ Revoke Share" button
4. Confirm the action

## **🚀 Future Enhancements**

### **Potential Improvements:**
1. **Bulk Operations**: Select multiple shares for bulk actions
2. **Advanced Filtering**: Filter by status, date, component
3. **Export Data**: Export share lists to CSV/Excel
4. **Analytics**: Detailed access analytics and charts
5. **Notifications**: Email notifications for share activities
6. **Link Expiration**: Auto-expire previous links when generating new ones

### **Auto-Expiration Feature:**
To implement automatic expiration of previous links when generating new ones, we can modify the `generatePublicShareableLink` function to:

```javascript
// Before creating new link, expire previous links for the same component
await ComponentPrivateShares.updateMany(
    {
        component_id: componentId,
        shared_by: sharedBy,
        access_type: 'by_link',
        is_active: true
    },
    {
        is_active: false,
        status: 'revoked',
        updated_at: new Date()
    }
);
```

This would ensure only one active link exists per component per user.

## **🎉 Summary**

The Manage Shares page provides a complete solution for:
- ✅ Viewing all shares and links in one place
- ✅ Managing share permissions and status
- ✅ Generating new links with custom settings
- ✅ Refreshing existing links with new tokens
- ✅ Deleting unwanted links permanently
- ✅ Revoking access for private shares
- ✅ Monitoring share usage and statistics

The system is secure, user-friendly, and provides all the functionality needed for comprehensive share management.
