// Npm declaration
const mongoose = require('mongoose');

// Model declaration
const { Components } = require('../models/component.model');
const { CreatorFollow } = require('../models/creator_follow.model');

const { saveAndSendNotification } = require('../services/notification_helper.service');

const { notificationTypes } = require('../config/component.constant');


const sendNotificationOnPublishElement = async (elementId, creatorId) => {
    try {
        const element = await Components.findOne({ _id: elementId }, 'title slug published_count').populate({
            'path': 'created_by_user',
            'select': 'first_name last_name username email avatar'
        }).lean();

        const notificationData = {
            title: element.title,
            message: `${element.title} has new updates please have a look`,
            type: notificationTypes.ELEMENT_PUBLISH,
            created_by: creatorId,
            entity: {
                slug: element.slug,
                created_by_user: element.created_by_user.username
            }
        };

        const receivers = await fetchFollowersList(creatorId);

        await saveAndSendNotification(notificationData, receivers);
    } catch (error) {
        // Log error if any
        console.error('Error at function sendNotificationOnPublishElement', error);
        throw error;
    }
};

const fetchFollowersList = async (creatorId) => {
    try {
        // Get all follower IDs for this creator
        const followerIds = (await CreatorFollow.distinct('follower_id', { creator_id: creatorId })).map((id) => new mongoose.Types.ObjectId(id));
        return followerIds;
    } catch (error) {
        // Log error if any
        console.error('Error at function fetchFollowersList', error);
        throw error;
    }
};

module.exports = {
    sendNotificationOnPublishElement,
    fetchFollowersList
};