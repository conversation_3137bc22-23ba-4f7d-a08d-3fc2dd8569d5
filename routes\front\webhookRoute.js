const express = require('express');
const router = express.Router();

const { gitlabMergeRequestEventWebhook, gitlabRepositoryUpdateEventWebhook, razorpayCapturedWebHook } = require('../../controller/front/webhook.controller');

const { verifyGitLabSignature } = require('../../middlewares/validateGitlabWebHook');

router.post('/gitlab-webhook', verifyGitLabSignature, gitlabMergeRequestEventWebhook);
router.post('/gitlab-webhook/update', verifyGitLabSignature, gitlabRepositoryUpdateEventWebhook);
router.post('/razorpay/captured', razorpayCapturedWebHook);

module.exports = router;