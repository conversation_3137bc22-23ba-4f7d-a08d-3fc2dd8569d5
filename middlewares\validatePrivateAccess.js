const { ReS } = require('../services/general.helper');
const constants = require('../config/constants');
const logger = require('../config/logger');



/**
 * Middleware to check if user owns the component
 */
const validateComponentOwnership = async (req, res, next) => {
    try {
        const componentId = req.params.id || req.params.componentId;
        const userId = req.session?._id;

        if (!userId) {
            return ReS(res, constants.unauthorized_code, 'Authentication required');
        }

        if (!componentId) {
            return ReS(res, constants.bad_request_code, 'Component ID is required');
        }

        const Components = require('../models/component.model').Components;
        const isOwner = await Components.exists({
            _id: componentId,
            created_by_user: userId
        });

        if (!isOwner) {
            return ReS(res, constants.forbidden_code, 'You do not own this component');
        }

        next();

    } catch (error) {
        logger.error(`Error in validateComponentOwnership: ${error.message}`);
        return ReS(res, constants.server_error_code, 'Error validating component ownership');
    }
};

module.exports = {
    validateComponentOwnership
};