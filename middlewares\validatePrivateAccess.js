const { checkPrivateAccess } = require('../services/private_share.service');
const { ReS } = require('../services/general.helper');
const constants = require('../config/constants');
const logger = require('../config/logger');

/**
 * Middleware to validate private component access
 * Adds access information to req.componentAccess
 */
const validatePrivateComponentAccess = async (req, res, next) => {
    try {
        const componentId = req.params.id || req.params.componentId;
        const userId = req.session?._id;
        const userEmail = req.session?.email;

        if (!componentId) {
            return ReS(res, constants.bad_request_code, 'Component ID is required');
        }

        const accessInfo = await checkPrivateAccess(componentId, userId, userEmail);

        if (!accessInfo.hasAccess) {
            return ReS(res, constants.forbidden_code, 'You do not have access to this component');
        }

        // Add access information to request for use in controllers
        req.componentAccess = accessInfo;
        next();

    } catch (error) {
        logger.error(`Error in validatePrivateComponentAccess: ${error.message}`);
        return ReS(res, constants.server_error_code, 'Error validating component access');
    }
};

/**
 * Middleware to validate private component access for public routes (optional auth)
 * Used for routes that can be accessed by both authenticated and unauthenticated users
 */
const validatePrivateComponentAccessOptional = async (req, res, next) => {
    try {
        const componentId = req.params.id || req.params.componentId;
        const userId = req.session?._id;
        const userEmail = req.session?.email;

        if (!componentId) {
            return ReS(res, constants.bad_request_code, 'Component ID is required');
        }

        const accessInfo = await checkPrivateAccess(componentId, userId, userEmail);

        // Add access information to request (even if no access)
        req.componentAccess = accessInfo;
        next();

    } catch (error) {
        logger.error(`Error in validatePrivateComponentAccessOptional: ${error.message}`);
        // Continue with no access info rather than failing
        req.componentAccess = { hasAccess: false, accessType: null };
        next();
    }
};

/**
 * Middleware to check if user owns the component
 */
const validateComponentOwnership = async (req, res, next) => {
    try {
        const componentId = req.params.id || req.params.componentId;
        const userId = req.session?._id;

        if (!userId) {
            return ReS(res, constants.unauthorized_code, 'Authentication required');
        }

        if (!componentId) {
            return ReS(res, constants.bad_request_code, 'Component ID is required');
        }

        const Components = require('../models/component.model').Components;
        const isOwner = await Components.exists({
            _id: componentId,
            created_by_user: userId
        });

        if (!isOwner) {
            return ReS(res, constants.forbidden_code, 'You do not own this component');
        }

        next();

    } catch (error) {
        logger.error(`Error in validateComponentOwnership: ${error.message}`);
        return ReS(res, constants.server_error_code, 'Error validating component ownership');
    }
};

module.exports = {
    validatePrivateComponentAccess,
    validatePrivateComponentAccessOptional,
    validateComponentOwnership
};