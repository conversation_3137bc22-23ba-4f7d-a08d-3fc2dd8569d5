<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Private Share Test</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="form-header">
                <h1>🔑 Login</h1>
                <p>Enter your email to receive an OTP</p>
            </div>

            <div class="card">
                <div id="emailStep">
                    <div class="input-group">
                        <label for="email">Email Address:</label>
                        <input type="email" id="email" placeholder="Enter your email" required>
                    </div>
                    <button onclick="sendLoginOTP()" id="sendOtpBtn">📧 Send OTP</button>
                </div>

                <div id="otpStep" style="display: none;">
                    <div class="input-group">
                        <label for="otp">Enter OTP:</label>
                        <input type="text" id="otp" placeholder="Enter 6-digit OTP" maxlength="6" required>
                    </div>
                    <div class="button-group">
                        <button onclick="verifyLoginOTP()" id="verifyBtn">✅ Verify & Login</button>
                        <button onclick="resendOTP()" id="resendBtn" class="secondary">🔄 Resend OTP</button>
                    </div>
                    <p class="info-text">Check your email for the OTP. It expires in 10 minutes.</p>
                </div>

                <div class="navigation-links" style="margin-top: 20px; text-align: center;">
                    <p style="margin-bottom: 10px;">Don't have an account? <a href="signup.html" style="color: #667eea; text-decoration: none;">Sign up here</a></p>
                    <p><a href="index.html" style="color: #667eea; text-decoration: none;">← Back to Home</a></p>
                </div>
            </div>

            <div class="logs">
                <h3>📋 Login Logs</h3>
                <div id="logContainer"></div>
                <button onclick="clearLogs()">🗑️ Clear Logs</button>
            </div>
        </div>
    </div>

    <script src="config.js"></script>
    <script>
        let currentUserId = null;
        let currentEmail = null;

        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            
            // Check if already logged in
            if (isAuthenticated()) {
                logMessage('Already logged in', 'info');
                const returnUrl = getUrlParams().returnUrl;
                if (returnUrl) {
                    // Add fromAuth parameter to indicate successful authentication
                    const separator = returnUrl.includes('?') ? '&' : '?';
                    const redirectUrl = `${returnUrl}${separator}fromAuth=true`;
                    window.location.href = redirectUrl;
                } else {
                    window.location.href = 'index.html';
                }
            }
        });

        async function sendLoginOTP() {
            const email = document.getElementById('email').value.trim();
            
            if (!email) {
                logMessage('Please enter your email', 'error');
                return;
            }

            if (!isValidEmail(email)) {
                logMessage('Please enter a valid email address', 'error');
                return;
            }

            const hideLoading = showLoading(document.getElementById('sendOtpBtn'));

            try {
                const { response, data } = await apiCall('/auth/login-with-email', {
                    method: 'POST',
                    body: JSON.stringify({ email })
                });

                hideLoading();

                if (data.status === 202) {
                    currentUserId = data.data.user_id;
                    currentEmail = email;
                    
                    logMessage('OTP sent successfully!', 'success');
                    
                    // Show OTP step
                    document.getElementById('emailStep').style.display = 'none';
                    document.getElementById('otpStep').style.display = 'block';
                    
                    // Focus on OTP input
                    document.getElementById('otp').focus();
                } else {
                    logMessage(data.message || 'Failed to send OTP', 'error');
                }
            } catch (error) {
                hideLoading();
                handleError(error, 'sending login OTP');
            }
        }

        async function verifyLoginOTP() {
            const otp = document.getElementById('otp').value.trim();
            
            if (!otp) {
                logMessage('Please enter the OTP', 'error');
                return;
            }

            if (otp.length !== 6) {
                logMessage('OTP must be 6 digits', 'error');
                return;
            }

            if (!currentUserId) {
                logMessage('Session expired. Please start over.', 'error');
                window.location.reload();
                return;
            }

            const hideLoading = showLoading(document.getElementById('verifyBtn'));

            try {
                const { response, data } = await apiCall(`/auth/verify-login-otp/${currentUserId}`, {
                    method: 'POST',
                    body: JSON.stringify({ otp })
                });

                hideLoading();

                if (data.status === 200) {
                    const userInfo = data.data;
                    const token = userInfo.token;
                    
                    // Save user session
                    saveUserSession(token, userInfo);
                    
                    logMessage('Login successful!', 'success');
                    
                    // Check for return URL
                    const params = getUrlParams();
                    const returnUrl = params.returnUrl;
                    
                    if (returnUrl) {
                        logMessage(`Redirecting to: ${returnUrl}`, 'info');
                        // Add fromAuth parameter to indicate successful authentication
                        const separator = returnUrl.includes('?') ? '&' : '?';
                        const redirectUrl = `${returnUrl}${separator}fromAuth=true`;
                        setTimeout(() => {
                            window.location.href = redirectUrl;
                        }, 1000);
                    } else {
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 1000);
                    }
                } else {
                    logMessage(data.message || 'Invalid OTP', 'error');
                }
            } catch (error) {
                hideLoading();
                handleError(error, 'verifying login OTP');
            }
        }

        async function resendOTP() {
            if (!currentUserId) {
                logMessage('Session expired. Please start over.', 'error');
                window.location.reload();
                return;
            }

            const hideLoading = showLoading(document.getElementById('resendBtn'));

            try {
                const { response, data } = await apiCall(`/auth/resend/login-otp/${currentUserId}`, {
                    method: 'POST'
                });

                hideLoading();

                if (data.status === 202) {
                    logMessage('OTP resent successfully!', 'success');
                } else {
                    logMessage(data.message || 'Failed to resend OTP', 'error');
                }
            } catch (error) {
                hideLoading();
                handleError(error, 'resending OTP');
            }
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }

        // Auto-submit OTP when 6 digits are entered
        document.addEventListener('DOMContentLoaded', function() {
            const otpInput = document.getElementById('otp');
            if (otpInput) {
                otpInput.addEventListener('input', function() {
                    if (this.value.length === 6) {
                        setTimeout(() => {
                            verifyLoginOTP();
                        }, 500);
                    }
                });
            }
        });
    </script>
</body>
</html>
