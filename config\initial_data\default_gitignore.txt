# -------------------------
# OS-Specific Files
# -------------------------
.DS_Store
Thumbs.db
ehthumbs.db
Icon?
Desktop.ini
$RECYCLE.BIN/

# -------------------------
# Node.js
# -------------------------
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
dist/
.env
.env.*.local

# -------------------------
# PHP / Composer
# -------------------------
vendor/
composer.lock
.env

# Laravel specific
/storage/*.key
/storage/logs
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*

# -------------------------
# Python
# -------------------------
__pycache__/
*.py[cod]
*.pyo
*.pyd
.Python
env/
venv/
*.env

# Django specific
db.sqlite3
*.log

# -------------------------
# Java / Gradle / Maven
# -------------------------
*.class
*.jar
*.war
*.ear
target/
build/
.gradle/
out/

# -------------------------
# Android
# -------------------------
*.iml
.gradle
/local.properties
/.idea/libraries
.DS_Store
/build
/captures
.externalNativeBuild
.cxx
*.apk
*.ap_
*.dex

# -------------------------
# iOS / Xcode
# -------------------------
*.xcuserstate
*.xcworkspace
*.xcuserdatad
*.xcuserdata
*.xcodeproj/project.xcworkspace/xcuserdata
*.pbxuser
!default.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3
*.xcscmblueprint
DerivedData/
build/
Pods/
*.ipa

# -------------------------
# React Native
# -------------------------
.expo/
.expo-shared/
*.expo
*.db
*.sqlite
*.log

# -------------------------
# Flutter
# -------------------------
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
build/
generated_plugin_registrant.dart
ios/Flutter/App.framework
ios/Flutter/Flutter.framework
ios/Flutter/Generated.xcconfig
pubspec.lock

# -------------------------
# Visual Studio Code
# -------------------------
.vscode/
.history/

# -------------------------
# IntelliJ / WebStorm / PhpStorm
# -------------------------
.idea/
*.iml
*.iws
*.ipr
out/

# -------------------------
# Logs / Misc
# -------------------------
*.log
logs/
*.pid
*.seed
*.pid.lock
*.swp
*.swo

# -------------------------
# Archives
# -------------------------
*.tar.gz
*.rar
*.7z
*.gz
*.tgz

# -------------------------
# Backup files
# -------------------------
*~
*.bak
*.tmp
*.temp
*.orig
*.old

# -------------------------
# Coverage / Reports
# -------------------------
coverage/
.nyc_output/
lcov-report/
*.lcov
test-output/
cobertura/

# -------------------------
# Terraform
# -------------------------
.terraform/
*.tfstate
*.tfstate.backup
crash.log

# -------------------------
# Misc tools
# -------------------------
.env.local
.env.development.local
.env.test.local
.env.production.local
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.sass-cache/
.cache/
.next/
.nuxt/
out/
.cache-loader/

# -------------------------
# CI/CD
# -------------------------
.circleci/
.github/workflows/
.gitlab-ci.yml