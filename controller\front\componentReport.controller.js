// Service declaration
const constants = require('../../config/constants');
const { ReS } = require('../../services/general.helper');
const logger = require('../../config/logger');
const { componentState, reportStatus } = require('../../config/component.constant');

const ComponentReports = require('../../models/component_report.model').ComponentReports;
const Components = require('../../models/component.model').Components;

const mongoose = require('mongoose');

async function reportComponent(req, res) {
    try {
        const { component_id } = req.params;

        const { report_type, additional_info } = req.body;

        const component = await Components.findOne({
            _id: component_id,
            component_state: componentState.PUBLISHED
        }, '_id created_by_user');

        if (!component) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        const componentReport = await ComponentReports.findOne({
            reported_by: req.session._id,
            component_id: component_id,
            status: reportStatus.PENDING
        });

        if (componentReport) {
            return ReS(res, constants.bad_request_code, 'Oops! A pending report already exists for this component. Our team is reviewing it.');
        }

        if (component.created_by_user.toString() == req.session._id.toString()) {
            return ReS(res, constants.bad_request_code, 'Oops! As the author of this component, you\'re not allowed to report it.');
        }

        const newReport = await ComponentReports.create({
            component_id: component_id,
            report_type: report_type,
            additional_info: additional_info,
            reported_by: req.session._id,
            status: reportStatus.PENDING
        });

        return ReS(res, constants.success_code, 'Reported Successfully', { report_id: newReport._id });
    } catch (err) {
        logger.error(`Error at Front Controller reportComponent ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getAllComponentReports(req, res) {
    try {

        // Set Default conditions
        const conditions = {
            reported_by: new mongoose.Types.ObjectId(req.session._id)
        };

        // Set Default sort
        const sort = {
            created_at: -1
        };

        const pipelines = [{
            $match: conditions
        }, {
            $lookup: {
                from: 'components',
                let: {
                    component_id: '$component_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$component_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'slug': 1,
                        'title': 1,
                        'image_url': 1
                    }
                }],
                as: 'component_id'
            }
        }, {
            $project: {
                reported_by: 1,
                report_type: 1,
                status: 1,
                created_at: 1,
                component_id: {
                    $arrayElemAt: ['$component_id', 0]
                }
            }
        }, {
            $sort: sort
        }];

        const componentReports = await ComponentReports.aggregate(pipelines);
        return ReS(res, constants.success_code, 'Reported Successfully', componentReports);
    } catch (err) {
        logger.error(`Error at Front Controller getAllComponentReports ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}


module.exports = {
    reportComponent,
    getAllComponentReports
};