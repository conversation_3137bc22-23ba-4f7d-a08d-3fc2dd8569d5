const logger = require('../config/logger');
const redis = require('../config/redis');

const { createGitLabUser, createUserAccessToken, createNewGitlabProject, acceptMergeRequest, createGitLabMergeRequest, createGitlabBranch, addMemberToProject, restrictBranchCreation, protectBranch, addReadmeFileToRepository, findRepositoryFile } = require('./gitlab.helper');
const { v4: uuidv4 } = require('uuid');
const { generateRandomPassword, generateRandomUsername, encryptDataWithAES, createGitlabTree } = require('./general.helper');

// Model declaration
const GitlabUsers = require('../models/gitlab_users.model').GitlabUsers;
const GitlabUserTokens = require('../models/gitlab_user_tokens.model').GitlabUserTokens;
const Admins = require('../models/admins.model').Admins;
const GitlabRepository = require('../models/gitlab_repository.model').GitlabRepository;
const Users = require('../models/users.model').Users;
const RepositoryStructure = require('../models/repository_structure.model').RepositoryStructure;
const Components = require('../models/component.model').Components;
const DraftComponents = require('../models/draft_components.model').DraftComponents;

const { gitlabUserScope, projectVisibility, gitlabDefaultBranch, gitlabProductionBranch, gitlabAccessLevel, repositoryState } = require('../config/gitlab.constant');
const { componentType } = require('../config/component.constant');
const constants = require('../config/constants');

const path = require('path');
const filePath = path.join(__dirname, './../config/initial_data/default_readme.txt'); // Replace with the path to your file
const fs = require('fs');

const createRepositoryUserFromAdmin = async (admin_id, user_name = null) => {
    try {
        // Fetch admin data from the database using the provided admin_id
        const adminData = await Admins.findOne({ _id: admin_id }).lean();

        // Check if a GitLab user already exists for this admin
        if (adminData && adminData.gitlab_user_exists && adminData.gitlab_user_exists == true) {
            return; // If user exists, return early
        }
        // Extract first_name and last_name information from admin data
        const firstName = adminData.first_name;
        const lastName = adminData.last_name;
        // Extract email, name, and other necessary information from admin data
        const email = adminData.email;
        const name = `${firstName} ${lastName}`;

        // Generate a unique external user ID
        const extern_uid = await uuidv4();

        // Generate a random password of 12 characters
        const password = generateRandomPassword(12);

        let username = user_name;
        // Generate a random username if not provide
        if (!user_name) {
            username = generateRandomUsername();
        }
        // Create a new GitLab user with the extracted and generated data
        const newGitlabUser = await createGitLabUser(email, name, username, password);

        // Store the new GitLab user information in the database+
        const newGitlabUserDB = await GitlabUsers.create({
            admin_id: admin_id,
            email: email,
            extern_uid: extern_uid,
            name: name,
            username: username,
            password: encryptDataWithAES(password),
            gitlab_user_id: newGitlabUser.id
        });

        // Update the admin document to mark that a GitLab user now exists
        await Admins.updateOne({
            _id: admin_id
        }, {
            $set: {
                gitlab_user_exists: true
            }
        });

        const tokenName = `${username}-token-01`;
        // Create a personal access token for the newly created GitLab user
        const gitlabUserToken = await createUserAccessToken(newGitlabUser.id, tokenName, gitlabUserScope);

        // Update the GitLab user document with the generated token and its details
        await GitlabUserTokens.create({
            user_id: newGitlabUserDB._id,
            personal_access_tokens: encryptDataWithAES(gitlabUserToken.token),
            personal_access_token_name: tokenName,
            token_expires_at: gitlabUserToken.expires_at,
            scopes: gitlabUserToken.scopes
        });

        return {
            email: email,
            username: username,
            personal_access_tokens: gitlabUserToken.token,
            token_expires_at: new Date(gitlabUserToken.expires_at)
        };

    } catch (err) {
        // Log any errors that occur during the process
        logger.error(`Error at repository service createRepositoryUserFromAdmin: ${err}`);
        throw err; // Throw the error for handling in higher layers
    }
};

const createNewGitlabRepository = async (component_id, admin_id, project_name, platform_id, initialize_with_readme = true, description) => {
    try {
        const gitlabUser = await GitlabUsers.findOne({
            admin_id: admin_id
        }).lean();

        if (gitlabUser == null) {
            const error = new Error('Gitlab user not found');
            error.statusCode = constants.resource_not_found;
            throw error;
        }

        const newProject = await createNewGitlabProject(project_name, gitlabDefaultBranch, false);

        await addMemberToProject(newProject.id, gitlabUser.gitlab_user_id, gitlabAccessLevel.DEVELOPER);

        // If the user opted to initialize with a README, prepare and add the README file to the repository
        if (initialize_with_readme) {
            const readmeContent = await prepareReadmeFileContent(project_name);
            await addReadmeFileToRepository(newProject.id, gitlabDefaultBranch, readmeContent);
        }

        // Protect the default branch to prevent direct pushes
        await protectBranch(newProject.id, gitlabDefaultBranch);

        // Restrict the creation of additional branches in the repository
        await restrictBranchCreation(newProject.id);

        const newGitlabRepository = await GitlabRepository.create({
            gitlab_user_id: gitlabUser._id,
            admin_id: admin_id,
            component_id: component_id,
            platform_id: platform_id,
            project_name: project_name,
            project_id: newProject.id,
            http_url_to_repo: newProject.http_url_to_repo,
            visibility: projectVisibility.PRIVATE,
            default_branch: gitlabDefaultBranch,
            description: description,
            is_active: true,
            is_deleted: false
        });
        return newGitlabRepository;
    } catch (err) {
        // Log any errors that occur during the process
        logger.error(`Error at repository service createNewGitlabRepository: ${err}`);
        throw err; // Throw the error for handling in higher layers
    }
};

const publishComponentRepository = async (repositoryIds) => {
    try {
        // Retrieve the GitLab repository information based on the component ID
        const gitlabRepositoryList = await GitlabRepository.find({ _id: { $in: repositoryIds } }).lean();

        console.log(`Component found with total ${gitlabRepositoryList.length} codes-pace linked`);

        for (const gitlabRepository of gitlabRepositoryList) {
            // Extract the project ID from the repository information
            const projectId = gitlabRepository.project_id;

            // Check if the repository exists and the production branch does not yet exist
            if (gitlabRepository && !gitlabRepository.production_branch_exists) {


                // Create the production branch from the development branch
                await createGitlabBranch(projectId, gitlabProductionBranch, gitlabDefaultBranch);
                // Update the repository to indicate the production branch now exists
                await GitlabRepository.updateOne({ _id: gitlabRepository._id }, {
                    $set: { production_branch_exists: true }
                });
            } else {
                // Create a merge request to merge development branch into the main branch
                const mergeRequestResponse = await createGitLabMergeRequest(projectId, gitlabDefaultBranch, gitlabProductionBranch);


                if (mergeRequestResponse && mergeRequestResponse.iid) {
                    // Accept the merge request to complete the merge process
                    await acceptMergeRequest(projectId, mergeRequestResponse.iid);
                }
            }

            await cacheRepositoryDataInRedis(gitlabRepository._id, projectId);
        }
    } catch (err) {
        // Log any errors that occur during the process
        logger.error(`Error at repository service publishComponentRepository: ${err}`);
        throw err; // Rethrow the error for handling in higher layers
    }
};

const findComponentAndRepository = async (id) => {
    try {
        const repository = await GitlabRepository.findOne({
            _id: id
        }, 'project_id component_id state published_state');

        if (!repository) {
            return { error: 'Oops! Resource not found.', status: constants.resource_not_found };
        }

        return { repository };
    } catch (error) {
        // Log any errors that occur during the process
        logger.error(`Error at repository service findComponentAndRepository: ${error}`);
        return { error: 'Oops! Something went wrong.', status: constants.server_error };
    }
};

const validateUserGitlabTokenExpiry = async (userId) => {
    try {
        const gitlabToken = await GitlabUserTokens.findOne({ user_id: userId })
            .sort({ created_at: -1 })
            .lean();

        if (!gitlabToken) {
            return { error: 'GitLab token not found.', status: constants.resource_not_found };
        }

        const tokenExpiry = new Date(gitlabToken.token_expires_at);
        const currentTime = new Date();

        if (tokenExpiry <= currentTime) {
            return { error: 'GitLab token has expired.', status: constants.bad_request_code };
        }

        return { message: 'GitLab token is valid.', status: constants.success_code };
    } catch (error) {
        logger.error(`Error at repository service validateUserGitlabTokenExpiry: ${error}`);
        return { error: 'Oops! Something went wrong.', status: constants.server_error };
    }
};

const createRepositoryUserFromUser = async (user_id, user_name = null) => {
    try {
        // Fetch user data from the database using the provided user_id
        const user = await Users.findOne({ _id: user_id }).lean();

        // Check if a GitLab user already exists for this user
        if (user && user.gitlab_user_exists && user.gitlab_user_exists == true) {
            return; // If user exists, return early
        }
        // Extract first_name and last_name information from user data
        const firstName = user.first_name;
        const lastName = user.last_name;
        // Extract email, name, and other necessary information from user data
        const email = user.email;
        const name = `${firstName} ${lastName}`;

        // Generate a unique external user ID
        const extern_uid = await uuidv4();

        // Generate a random password of 12 characters
        const password = generateRandomPassword(12);

        let username = user_name;
        // Generate a random username if not provide
        if (!user_name) {
            username = generateRandomUsername();
        }
        // Create a new GitLab user with the extracted and generated data
        const newGitlabUser = await createGitLabUser(email, name, username, password);

        // Store the new GitLab user information in the database+
        const newGitlabUserDB = await GitlabUsers.create({
            user_id: user_id,
            email: email,
            extern_uid: extern_uid,
            name: name,
            username: username,
            password: encryptDataWithAES(password),
            gitlab_user_id: newGitlabUser.id
        });

        // Update the user document to mark that a GitLab user now exists
        await Users.updateOne({
            _id: user_id
        }, {
            $set: {
                gitlab_user_exists: true
            }
        });

        const tokenName = `${username}-token-01`;
        // Create a personal access token for the newly created GitLab user
        const gitlabUserToken = await createUserAccessToken(newGitlabUser.id, tokenName, gitlabUserScope);

        // Update the GitLab user document with the generated token and its details
        await GitlabUserTokens.create({
            user_id: newGitlabUserDB._id,
            personal_access_tokens: encryptDataWithAES(gitlabUserToken.token),
            personal_access_token_name: tokenName,
            token_expires_at: gitlabUserToken.expires_at,
            scopes: gitlabUserToken.scopes
        });

        return {
            email: email,
            username: username,
            personal_access_tokens: gitlabUserToken.token,
            token_expires_at: new Date(gitlabUserToken.expires_at)
        };

    } catch (err) {
        // Log any errors that occur during the process
        logger.error(`Error at repository service createRepositoryUserFromUser: ${err}`);
        throw err; // Throw the error for handling in higher layers
    }
};

const prepareReadmeFileContent = async (project_name) => {
    return new Promise(async (resolve, reject) => {
        try {
            // Read the file
            fs.readFile(filePath, 'utf8', async (err, data) => {
                if (err) {
                    console.error('Error reading file:', err);
                    return;
                }

                // Define dynamic variables and their replacements
                const replacements = {
                    '{{project_name}}': project_name
                };
                // Perform replacements
                let updatedData = data;
                for (const [key, value] of Object.entries(replacements)) {
                    updatedData = updatedData.replace(new RegExp(key, 'g'), value);
                }
                resolve(updatedData);
            });
        } catch (error) {
            // Log any errors that occur during the process
            logger.error(`Error at repository service prepareReadmeFileContent: ${error}`);
            reject(error);
        }
    });
};

async function getRepositoryContent(id, ref = gitlabProductionBranch) {
    try {
        const repositoryData = await findRepositoryFile(id, ref);
        // Create the tree structure
        const fileTree = await createGitlabTree(repositoryData);
        return fileTree;
    } catch (err) {
        // Log any errors that occur during the process
        logger.error(`Error at repository service getRepositoryContent: ${err}`);
        throw err; // Throw the error for handling in higher layers
    }
}

async function searchRepositoryFiles(id, ref_name = gitlabProductionBranch) {
    // Fetch production branch files information from GitLab
    try {
        const repositoryData = await findRepositoryFile(id, ref_name);
        const blobPaths = repositoryData.filter((item) => item.type === 'blob').map((item) => item.path);
        return blobPaths;
    } catch (err) {
        // Log any errors that occur during the process
        logger.error(`Error at repository service searchRepositoryFiles: ${err}`);
        throw err; // Throw the error for handling in higher layers
    }

}

async function cacheRepositoryDataInRedis(repository_id, projectId) {
    try {
        // Log the start of the caching process
        logger.info(`Caching repository data for repository ID: ${repository_id} with GitLab project ID: ${projectId}`);

        // Generate Redis key for caching the file tree data
        const redisKeyTree = `repository:${repository_id}:tree:${gitlabProductionBranch}`;
        // Fetch the file tree from the GitLab project
        const fileTree = await getRepositoryContent(projectId, gitlabProductionBranch);
        // Cache the file tree data in Redis
        await redis.set(redisKeyTree, JSON.stringify(fileTree), 'EX', 1800);

        // Generate Redis key for caching the file paths
        const redisKeyFiles = `repository:${repository_id}:files:${gitlabProductionBranch}`;
        // Fetch the file paths from the GitLab project
        const filePaths = await searchRepositoryFiles(projectId, gitlabProductionBranch);
        // Cache the file paths data in Redis
        await redis.set(redisKeyFiles, JSON.stringify(filePaths), 'EX', 1800);

        // Store files tree & path in mongodb as well
        await storeRepositoryStructureInMongo(repository_id, fileTree, filePaths);

        // Log the success of caching
        logger.info(`Successfully cached repository data for repository ID: ${repository_id}`);
    } catch (error) {
        // Log any errors that occur during the caching process
        logger.error(`Error in cacheRepositoryDataInRedis: ${error}`);
        // Re-throw the error for handling in higher layers
        throw error;
    }
}

async function storeRepositoryStructureInMongo(repository_id, filesTree, filesPath) {
    try {
        await RepositoryStructure.updateOne({
            repository_id: repository_id
        }, {
            $set: {
                files_path: filesPath,
                files_tree: filesTree
            },
            $setOnInsert: {
                created_at: new Date()
            }
        }, {
            upsert: true
        }).lean();
        // Log the success of caching
        logger.info(`Successfully stored files structure data for repository ID: ${repository_id}`);
    } catch (error) {
        // Log any errors that occur during the caching process
        logger.error(`Error in storeRepositoryStructureInMongo: ${error}`);
        // Re-throw the error for handling in higher layers
        throw error;
    }
}

async function updateMarkDownAssetPaths(markdown, repository_id) {
    try {
        const baseUrl = `${process.env.BACK_END_BASE_URL}/front/pages/fetch-assets/${repository_id}`;
        // Regular expression to match Markdown image or video syntax
        // Regex to match Markdown syntax, ignoring paths starting with http or https
        const assetRegex = /(!?\[(.*?)\]\()((?!https?:\/\/)[^\s)]+)(\s*(".*?")?\))/g;

        // Replace each matched path with the updated path
        return markdown.replace(assetRegex, (match, start, altText, path, end) => {
            // Remove leading "./", leading "/", and trailing "/"
            const cleanedPath = path
                .replace(/^\.\//, '') // Remove leading "./"
                .replace(/^\/|\/$/g, '') // Remove leading and trailing "/"
                .split('?')[0]; // Remove query parameters
            return `${start}${baseUrl}/${encodeURIComponent(cleanedPath)}${end}`;
        });
    } catch (error) {
        // Log any errors that occur during the caching process
        logger.error(`Error while modify markdown: ${error}`);
        // Re-throw the error for handling in higher layers
        return markdown;
    }
}

async function updateMarkDownAssetPathsDevelopment(markdown, repository_id) {
    try {
        const baseUrl = `${process.env.BACK_END_BASE_URL}/front/pages/fetch-assets/development/${repository_id}`;
        // Regular expression to match Markdown image or video syntax
        // Regex to match Markdown syntax, ignoring paths starting with http or https
        const assetRegex = /(!?\[(.*?)\]\()((?!https?:\/\/)[^\s)]+)(\s*(".*?")?\))/g;

        // Replace each matched path with the updated path
        return markdown.replace(assetRegex, (match, start, altText, path, end) => {
            // Remove leading "./", leading "/", and trailing "/"
            const cleanedPath = path
                .replace(/^\.\//, '') // Remove leading "./"
                .replace(/^\/|\/$/g, '') // Remove leading and trailing "/"
                .split('?')[0]; // Remove query parameters
            return `${start}${baseUrl}/${encodeURIComponent(cleanedPath)}${end}`;
        });
    } catch (error) {
        // Log any errors that occur during the caching process
        logger.error(`Error while modify markdown: ${error}`);
        // Re-throw the error for handling in higher layers
        return markdown;
    }
}

async function checkForkedStatusOfRepository(repositoryId, userId) {
    try {
        // Check if the repository is forked by the user in GitLab
        const isForked = await GitlabRepository.exists({
            fork_id: repositoryId,
            user_id: userId
        });

        return (isForked) ? true : false;
    } catch (error) {
        console.error(`Error while checking forked status: ${error}`);
        throw error;
    }
}

async function isCodespaceDeletable(repository) {
    try {
        if (repository?.state !== repositoryState.PUBLIC) {
            let component = null;
            let projectType = 'project';

            if (repository.component_id) {
                component = await Components.findOne(
                    { _id: repository.component_id },
                    "component_type"
                ).lean();
            } else if (repository.component_draft_id) {
                component = await DraftComponents.findOne(
                    { _id: repository.component_draft_id },
                    "component_type"
                ).lean();
            }

            if (component) {
                projectType = (component.component_type === componentType.MOBILE)
                    ? "mobile component"
                    : "project";

                return { error: `Deletion not allowed: This codespace is currently linked to a ${projectType}`, status: constants.bad_request_code };
            }
        }
        return null; // Deletion allowed
    } catch (error) {
        console.error(`Error while checking codespace deletable or not: ${error}`);
        throw error;
    }
}

module.exports = {
    createRepositoryUserFromAdmin,
    createNewGitlabRepository,
    publishComponentRepository,
    findComponentAndRepository,
    createRepositoryUserFromUser,
    prepareReadmeFileContent,
    cacheRepositoryDataInRedis,
    updateMarkDownAssetPaths,
    updateMarkDownAssetPathsDevelopment,
    validateUserGitlabTokenExpiry,
    checkForkedStatusOfRepository,
    isCodespaceDeletable
};