const constants = require('../../config/constants');

const { ReS } = require('../../services/general.helper');

const AdminMpnPointLogs = require('../../models/admin_mpn_point_logs.model').AdminMpnPointLogs;
const AdminMpnPointWallet = require('../../models/admin_mpn_point_wallet.model').AdminMpnPointWallet;

async function fetchAdminWalletBalance(req, res) {
    try {

        const adminWallet = await AdminMpnPointWallet.findOne({}).lean();

        const mpnPoints = adminWallet?.points ?? 0;

        return ReS(res, constants.success_code, 'Wallet Balance Fetched', { mpn_points: mpnPoints });
    } catch (error) {
        // Log the error and send a generic server error response
        console.error(`Error in fetchAdminPointHistory: ${error}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function fetchAdminPointHistory(req, res) {
    try {
        const { skip = 0, limit = 10 } = req.body;

        // Set default conditions
        const conditions = {};

        // Count filtered documents after filter apply
        const filterDocuments = await AdminMpnPointLogs.countDocuments(conditions);

        // Set default sort
        const sort = {
            created_at: -1
        };
        const pipelines = [{
            $match: conditions
        }, {
            $sort: sort
        }, {
            $skip: skip
        }, {
            $limit: limit
        }, {
            $lookup: {
                from: 'components', // Join with components collection
                let: { component_id: '$component_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$component_id'] }
                        }
                    },
                    {
                        $project: {
                            title: 1,
                            slug: 1,
                            component_state: 1,
                            component_type: 1
                        }
                    }
                ],
                as: 'component_id'
            }
        }, {
            $project: {
                user_id: 1,
                points: 1,
                activity: 1,
                created_at: 1,
                description: 1,
                component_id: {
                    $arrayElemAt: ['$component_id', 0]
                }
            }
        }];

        const pointHistory = await AdminMpnPointLogs.aggregate(pipelines);

        const responseObj = {
            recordsFiltered: filterDocuments,
            list: pointHistory
        };
        return ReS(res, constants.success_code, 'Wallet Point History Fetched', responseObj);
    } catch (error) {
        // Log the error and send a generic server error response
        console.error(`Error in fetchAdminPointHistory: ${error}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

module.exports = {
    fetchAdminWalletBalance,
    fetchAdminPointHistory
};