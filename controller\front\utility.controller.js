const constants = require('../../config/constants');
const logger = require('../../config/logger');
const filePath = require('path');

// Service declaration
const { ReS, escapeRegex, sendError } = require('../../services/general.helper');
const { getComponentOrientation } = require('../../services/component.service');
const { s3UploadStaticContent, uploadBase64ToS3, getFileExtensionFromBase64, uploadBufferToS3 } = require('../../services/general.helper');
const { html2gifWithInteractions, html2gif, html2Screenshot, detectWebGLContent } = require('../../services/elements.service');

// Models declaration
const SupportedPlatforms = require('../../models/supported_platforms.model').SupportedPlatforms;
const Category = require('../../models/category.model').Category;
const Tags = require('../../models/tag.model').Tags;
const Settings = require('../../models/settings.model').Settings;
const Sections = require('../../models/section.model').Sections;
const PlatformLicense = require('../../models/platform_licenses.model').PlatformLicense;
const Users = require('../../models/users.model').Users;

async function getAllPlatformSortList(req, res) {
    try {
        // Set default conditions
        const conditions = {
            is_active: true
        };
        // Set default sort
        const sort = {
            'title': 1
        };
        if (req.query.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.query.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        const query = [{
            $match: conditions
        }, {
            $project: {
                title: 1,
                slug: 1,
                description: 1,
                image_url: 1,
                created_at: 1,
                is_active: 1
            }
        }];
        query.push({
            '$sort': sort
        });
        const platformList = await SupportedPlatforms.aggregate(query);
        return ReS(res, constants.success_code, 'Data Fetched', platformList);
    } catch (err) {
        logger.error(`Error at Front Controller getAllPlatformSortList${err}`);
        return sendError(res, err);
    }
}

async function getAllCategorySortList(req, res) {
    try {
        const { is_active, category_name, group_by, category_type } = req.query;
        // Set default sort
        const sort = {
            'category_name': 1
        };
        // Set default conditions
        const conditions = {
            is_deleted: false,
            is_active: true
        };

        if (group_by == 'parent') {
            conditions['parent_id'] = null;
        }

        if (is_active != undefined) {
            conditions['is_active'] = is_active;
        }

        if (category_name) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(category_name);
            conditions['$or'] = [{
                'category_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        // Default parent condition
        const parentConditions = [{
            $eq: ['$parent_id', '$$category_id']
        }, {
            $eq: ['$is_active', true]
        }, {
            $eq: ['$is_deleted', false]
        }];

        if (category_type) {
            conditions['category_type'] = category_type;
            parentConditions.push({
                $in: [category_type, '$category_type']
            })
        }

        const pipelines = [{
            $match: conditions
        }];

        if (group_by == 'parent') {
            pipelines.push({
                $lookup: {
                    from: 'categories',
                    let: {
                        category_id: '$_id'
                    },
                    pipeline: [{
                        $match: {
                            $expr: {
                                $and: parentConditions
                            }
                        }
                    }, {
                        $project: {
                            category_name: 1,
                            category_slug: 1,
                            short_description: 1,
                            image_url: 1,
                            created_at: 1,
                            is_active: 1,
                            category_type: 1
                        }
                    }],
                    as: 'children'
                }
            });
        }

        pipelines.push({
            $project: {
                category_name: 1,
                category_slug: 1,
                short_description: 1,
                image_url: 1,
                created_at: 1,
                is_active: 1,
                children: 1,
                category_type: 1
            }
        }, {
            $sort: sort
        });

        const categoryList = await Category.aggregate(pipelines);
        return ReS(res, constants.success_code, 'Data Fetched', categoryList);
    } catch (err) {
        logger.error(`Error at Front Controller getAllCategorySortList${err}`);
        return sendError(res, err);
    }
}

async function getAllTagsSortList(req, res) {
    try {
        // Define the filter to select active tags only
        const filter = { is_active: true };

        // Default sort order: by the number of total_popularity in descending order
        const sort = {
            total_popularity: -1,
            updated_at: -1
        };

        // Aggregation pipeline
        const pipelines = [
            {
                $match: filter // Filter tags by is_active status
            },
            {
                $project: {
                    title: 1, // Include title
                    slug: 1, // Include slug
                    is_active: 1, // Include is_active status
                    project_popularity: 1,
                    elements_popularity: 1,
                    mobile_popularity: 1,
                    updated_at: 1,
                    total_popularity: { $add: ['$project_popularity', '$elements_popularity', '$mobile_popularity'] } // Add the two fields
                }
            },
            {
                $sort: sort // Sort by the number of total_popularity
            }
        ];

        // Execute the aggregation query on the Tags collection
        const tagList = await Tags.aggregate(pipelines);

        // Return the successful response with the fetched data
        return ReS(res, constants.success_code, 'Data Fetched', tagList);
    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at Front Controller getAllTagsSortList: ${err}`);
        return sendError(res, err);
    }
}

async function getAllDifficultyLevelSortList(req, res) {
    try {
        // Set default conditions
        const conditions = {
            setting_slug: 'difficulty_levels'
        };
        // Set default sort
        const sort = {
            'sort_order': 1
        };
        const query = [{
            $match: conditions
        }, {
            $unwind: '$values'
        }, {
            $replaceRoot: {
                newRoot: '$values'
            }
        }];
        query.push({
            '$sort': sort
        });
        const difficultyLevelList = await Settings.aggregate(query);
        return ReS(res, constants.success_code, 'Data Fetched', difficultyLevelList);
    } catch (err) {
        logger.error(`Error at Front Controller getAllDifficultyLevelSortList${err}`);
        return sendError(res, err);
    }
}

async function uploadFile(req, res) {
    try {
        let orientation;
        const fileExtension = filePath.extname(req.files.image.name);
        const filename = `${new Date().getTime()}${filePath.extname(req.files.image.name)}`;
        const documentPath = `assets/${filename}`;
        await s3UploadStaticContent(req.files.image, documentPath);

        // Check if layout query parameter is true and process accordingly
        if (req.query && req.query.layout && req.query.layout === 'true') {
            // Determine whether the uploaded file is an image or video
            const type = req.files.image.mimetype.startsWith('image/') ? 'image' : 'video';

            // Process based on the type of file
            if (type === 'image') {
                try {
                    // Get aspect ratio for images
                    orientation = await getComponentOrientation(req.files.image, type);
                } catch (error) {
                    // Handle errors when getting aspect ratio for images
                    logger.error(`Error while getting aspect ratio for images: ${error}`);
                }
            } else if (type === 'video') {
                try {
                    // Get aspect ratio for videos
                    orientation = await getComponentOrientation(documentPath, type);
                } catch (error) {
                    // Handle errors when getting aspect ratio for videos
                    logger.error(`Error while getting aspect ratio for videos: ${error}`);
                }
            }
        }
        return ReS(res, constants.success_code, 'File Uploaded Successfully', {
            path: documentPath,
            orientation: orientation,
            file_extension: fileExtension
        });
    } catch (err) {
        logger.error(`Error at Front Controller uploadFile${err}`);
        return sendError(res, err);
    }
}

async function uploadFileBase64(req, res) {
    try {
        const { image } = req.body;
        const fileExtension = getFileExtensionFromBase64(image);
        const filename = `${new Date().getTime()}.${fileExtension.extension}`;
        const documentPath = `assets/${filename}`;
        await uploadBase64ToS3(image, documentPath, fileExtension.mimeType);
        return ReS(res, constants.success_code, 'File Uploaded Successfully', { path: documentPath });
    } catch (err) {
        logger.error(`Error at Front Controller uploadFile ${err}`);
        return sendError(res, err);
    }
}

async function getAllSectionsSortList(req, res) {
    try {
        // Set default conditions
        const conditions = {
            is_active: true
        };

        // Set default sort
        const sort = {
            order: 1
        };

        if (req.query.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.query.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        const query = [{
            $match: conditions
        }, {
            $project: {
                title: 1,
                slug: 1,
                description: 1,
                image_url: 1,
                created_at: 1,
                is_active: 1,
                editor_type: 1,
                order: 1
            }
        }];

        query.push({
            '$sort': sort
        });
        const sectionList = await Sections.aggregate(query);
        return ReS(res, constants.success_code, 'Data Fetched', sectionList);
    } catch (err) {
        logger.error(`Error at Front Controller getAllSectionsSortList${err}`);
        return sendError(res, err);
    }
}

async function getAllLicenseSortList(req, res) {
    try {
        // Set default conditions
        const conditions = {
            is_active: true
        };

        // Set default sort
        const sort = {
            created_at: -1
        };

        if (req.query.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.query.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        if (req.query.license_type) {
            conditions['license_type'] = req.query.license_type;
        }

        const query = [{
            $match: conditions
        }, {
            $project: {
                title: 1,
                slug: 1,
                category: 1,
                description: 1,
                dynamic_values: 1,
                license_type: 1,
                created_at: 1,
                is_active: 1,
                is_default: 1
            }
        }];

        query.push({
            '$sort': sort
        });

        const licenseList = await PlatformLicense.aggregate(query);

        return ReS(res, constants.success_code, 'Data Fetched', licenseList);
    } catch (err) {
        logger.error(`Error at Front Controller getAllLicenseSortList ${err}`);
        return sendError(res, err);
    }
}

async function getAllRepositoryAccessList(req, res) {
    try {
        const { gitlabAccessLevel } = require('../../config/gitlab.constant');
        return ReS(res, constants.success_code, 'Data Fetched', gitlabAccessLevel);
    } catch (err) {
        logger.error(`Error at Front Controller getAllRepositoryAccessList ${err}`);
        return sendError(res, err);
    }
}

async function getUserSuggestions(req, res) {
    try {
        const { searchText } = req.query;

        // Default conditions
        const conditions = {
            is_active: true,
            is_verified: true
        };
        if (searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(searchText);
            conditions['$or'] = [{
                'username': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }, {
                'email': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        const userList = await Users.find(conditions, 'email username avatar');
        return ReS(res, constants.success_code, 'Data Fetched', userList);
    } catch (err) {
        logger.error(`Error at Front Controller getUserSuggestions ${err}`);
        return sendError(res, err);
    }
}

async function takeAndUploadScreenshot(req, res) {
    try {
        const { htmlContent } = req.body;

        // Validate the input
        if (!htmlContent || typeof htmlContent !== 'string') {
            return ReS(res, constants.bad_request_code, 'Invalid HTML content provided');
        }

        // Convert HTML content to screenshot buffer
        const screenshotBuffer = await html2Screenshot(htmlContent);
        res.set('Content-Type', 'image/png');
        res.send(screenshotBuffer);
    } catch (error) {
        console.log('error while taking screenshot', error);
        return sendError(res, error);
    }
}

async function convertHTMLToGIF(req, res) {
    try {
        const { htmlContent } = req.body;

        // Validate the input
        if (!htmlContent || typeof htmlContent !== 'string') {
            return ReS(res, constants.bad_request_code, 'Invalid HTML content provided');
        }

        // Convert HTML content to GIF buffer
        const gifBuffer = await html2gif(htmlContent);
        res.set('Content-Type', 'image/png');
        res.send(gifBuffer);
    } catch (error) {
        console.error('Error while generating and uploading GIF:', error);
        return sendError(res, error);
    }
}

async function convertHTMLToGIFWithInteraction(req, res) {
    try {
        const { htmlContent } = req.body;

        // Validate the input
        if (!htmlContent || typeof htmlContent !== 'string') {
            return ReS(res, constants.bad_request_code, 'Invalid HTML content provided');
        }
        // Convert HTML content to GIF buffer with interactions
        const gifBuffer = await html2gifWithInteractions(htmlContent);
        res.set('Content-Type', 'image/png');
        res.send(gifBuffer);
    } catch (error) {
        console.log('error while generating GIF with interactions', error);
        return sendError(res, error);
    }
}

async function checkWebGLContent(req, res) {
    try {
        const { htmlContent } = req.body;

        // Validate the input
        if (!htmlContent || typeof htmlContent !== 'string') {
            return ReS(res, constants.bad_request_code, 'Invalid HTML content provided');
        }

        // Detect WebGL content
        const detectionResult = await detectWebGLContent(htmlContent);

        return ReS(res, constants.success_code, 'WebGL detection completed', detectionResult);
    } catch (error) {
        console.error('Error while detecting WebGL content:', error);
        return sendError(res, error);
    }
}

module.exports = {
    getAllPlatformSortList,
    getAllCategorySortList,
    getAllTagsSortList,
    getAllDifficultyLevelSortList,
    uploadFile,
    uploadFileBase64,
    getAllSectionsSortList,
    getAllLicenseSortList,
    getAllRepositoryAccessList,
    getUserSuggestions,
    takeAndUploadScreenshot,
    convertHTMLToGIF,
    convertHTMLToGIFWithInteraction,
    checkWebGLContent
};