const constants = require('../config/constants');
const logger = require('../config/logger');
const mongoose = require('mongoose');

const ComponentUnlockHistory = require('../models/component_unlock_history.model').ComponentUnlockHistory;
const GitlabRepository = require('../models/gitlab_repository.model').GitlabRepository;
const Components = require('../models/component.model').Components;
const GitlabUsers = require('../models/gitlab_users.model').GitlabUsers;
const ComponentPrivateShares = require('../models/component_private_shares.model').ComponentPrivateShares;

const { repositoryState, publishState } = require('../config/gitlab.constant');

/**
 * Helper function to check if user has private share access to a component
 */
async function checkUserHasPrivateShareAccess(componentId, userId, userEmail) {
    try {
        if (!componentId) return false;

        const hasAccess = await ComponentPrivateShares.hasAccess(componentId, userId, userEmail);
        return hasAccess;
    } catch (error) {
        logger.debug(`Error checking private share access: ${error.message}`);
        return false;
    }
}

async function checkIsAccessible(req, res, next) {
    try {
        // Extract the repository ID from request parameters
        const { id, path } = req.params;

        // Proceed if 'path' exists and includes '.md' or '.markdown' (case-insensitive)
        if (path && (path.toLowerCase().includes('.md') || path.toLowerCase().includes('.markdown'))) {
            return next(); // Move to the next middleware or function
        }

        if (!mongoose.isValidObjectId(id)) {
            return res.status(constants.bad_request_code).json({
                status: constants.bad_request_code,
                message: 'Invalid code-space ID provided.'
            });
        }
        // Find the repository in the GitLabRepository collection by its ID
        const repository = await GitlabRepository.findOne(
            { _id: id },
            'project_id component_id gitlab_user_id state published_state'
        ).lean();

        // If the repository is not found, return a 404 error
        if (!repository) {
            return res.status(constants.resource_not_found).json({
                status: constants.resource_not_found,
                message: 'Oops! code-space not found.'
            });
        }

        // Check if the repository public & published for end-user
        if (repository?.state == repositoryState.PUBLIC && repository?.published_state == publishState.PUBLISHED) {
            return next();
        }

        // Fetch the GitLab user associated with the current session user ID
        const gitlabUser = await GitlabUsers.findOne({
            user_id: req.session._id
        }).lean();

        // Check if the repository is not linked to any component
        if (!repository.component_id) {
            // If the current GitLab user does not match the repository's GitLab user, deny access
            if (gitlabUser?._id?.toString() == repository?.gitlab_user_id?.toString()) {
                return next();
            } else {
                return res.status(constants.bad_request_code).json({
                    status: constants.bad_request_code,
                    message: 'You do not have access to this code-space.'
                });
            }
        }

        // Fetch the associated component data using the component_id from the repository
        const componentData = await Components.findOne(
            { _id: repository.component_id },
            'component_type is_paid created_by_user component_state'
        ).lean();

        // If the component is not found, return a 404 error
        if (!componentData) {
            return res.status(constants.resource_not_found).json({
                status: constants.resource_not_found,
                message: 'Oops! Component not found.'
            });
        }

        // Check if user is the component creator (always has access)
        if (componentData?.created_by_user?.toString() === req.session._id) {
            return next();
        }

        // Check for private share access first (works for both paid and non-paid components)
        const hasPrivateShareAccess = await checkUserHasPrivateShareAccess(
            componentData._id,
            req.session._id,
            req.session?.email
        );

        // Check if the component is paid
        if (componentData?.is_paid) {
            // Find an active unlock history for this component by the current user
            const unlockHistory = await ComponentUnlockHistory.findOne({
                component_id: componentData._id,
                unlock_by: req.session._id,
                is_active: true
            });

            // If no unlock history is found, check for private share access
            if (!unlockHistory) {
                // If user doesn't have unlock history AND no private share access, deny access
                if (!hasPrivateShareAccess) {
                    return res.status(constants.bad_request_code).json({
                        status: constants.bad_request_code,
                        message: 'Oops! You do not have access to this code-space.',
                        requires_payment: true,
                        component_id: componentData._id
                    });
                }

                // User has private share access to paid component - allow access
                // The person who shared it should have already unlocked it
                logger.info(`User ${req.session._id} accessing paid component ${componentData._id} via private share`);
            }
        } else {
            // For non-paid components, check if it's private and user has access
            const { componentState } = require('../config/user.constant');
            if (componentData?.component_state === componentState.PRIVATE) {
                // If component is private and user doesn't have private share access, deny access
                if (!hasPrivateShareAccess) {
                    return res.status(constants.bad_request_code).json({
                        status: constants.bad_request_code,
                        message: 'You do not have access to this private component.'
                    });
                }

                logger.info(`User ${req.session._id} accessing private component ${componentData._id} via private share`);
            }
        }

        // If all checks pass, move on to the next middleware or route handler
        return next();

    } catch (error) {
        // Catch and log any errors that occur
        logger.error(`Error in middleware function checkIsAccessible: ${error}`);
        return res.status(500).json({
            status: 500,
            message: 'Oops! Internal server error.'
        });
    }
}



module.exports = {
    checkIsAccessible
};