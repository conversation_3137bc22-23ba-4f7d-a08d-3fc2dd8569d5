<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Debug Test</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Server Debug Test</h1>
        
        <div class="debug-section">
            <h2>CSS Loading Test</h2>
            <div id="cssTest" class="test-result info">Testing CSS loading...</div>
        </div>
        
        <div class="debug-section">
            <h2>URL Routing Test</h2>
            <div id="routingTest">
                <button onclick="testRouting()">Test URL Routing</button>
                <div id="routingResults"></div>
            </div>
        </div>
        
        <div class="debug-section">
            <h2>Private Share Path Test</h2>
            <div id="pathTest">
                <button onclick="testPrivateSharePaths()">Test Private Share Paths</button>
                <div id="pathResults"></div>
            </div>
        </div>
        
        <div class="debug-section">
            <h2>Current URL Info</h2>
            <div id="urlInfo"></div>
        </div>
    </div>

    <script>
        // Test CSS loading
        function testCSSLoading() {
            const testDiv = document.getElementById('cssTest');
            const computedStyle = window.getComputedStyle(document.body);
            
            if (computedStyle.fontFamily || computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)') {
                testDiv.className = 'test-result success';
                testDiv.textContent = '✅ CSS loaded successfully!';
            } else {
                testDiv.className = 'test-result error';
                testDiv.textContent = '❌ CSS failed to load';
            }
        }
        
        async function testRouting() {
            const resultsDiv = document.getElementById('routingResults');
            resultsDiv.innerHTML = '<div class="test-result info">Testing routes...</div>';
            
            const testUrls = [
                '/styles.css',
                '/config.js',
                '/index.html',
                '/nonexistent.html'
            ];
            
            let results = '';
            
            for (const url of testUrls) {
                try {
                    const response = await fetch(url);
                    const status = response.status;
                    const contentType = response.headers.get('content-type');
                    
                    if (status === 200) {
                        results += `<div class="test-result success">✅ ${url} - Status: ${status}, Type: ${contentType}</div>`;
                    } else {
                        results += `<div class="test-result error">❌ ${url} - Status: ${status}</div>`;
                    }
                } catch (error) {
                    results += `<div class="test-result error">❌ ${url} - Error: ${error.message}</div>`;
                }
            }
            
            resultsDiv.innerHTML = results;
        }
        
        async function testPrivateSharePaths() {
            const resultsDiv = document.getElementById('pathResults');
            resultsDiv.innerHTML = '<div class="test-result info">Testing private share paths...</div>';
            
            const testPaths = [
                '/private-share/336569f3fa873ab0d831c8a3d45c937cf4786e27a4d328ffc3d7a8fd7deecaf8',
                '/private-share/styles.css',
                '/private-share/config.js'
            ];
            
            let results = '';
            
            for (const path of testPaths) {
                try {
                    const response = await fetch(path);
                    const status = response.status;
                    const contentType = response.headers.get('content-type');
                    
                    if (status === 200) {
                        results += `<div class="test-result success">✅ ${path} - Status: ${status}, Type: ${contentType}</div>`;
                    } else {
                        results += `<div class="test-result error">❌ ${path} - Status: ${status}</div>`;
                    }
                } catch (error) {
                    results += `<div class="test-result error">❌ ${path} - Error: ${error.message}</div>`;
                }
            }
            
            resultsDiv.innerHTML = results;
        }
        
        function showUrlInfo() {
            const urlInfoDiv = document.getElementById('urlInfo');
            urlInfoDiv.innerHTML = `
                <div class="test-result info">
                    <strong>Current URL:</strong> ${window.location.href}<br>
                    <strong>Pathname:</strong> ${window.location.pathname}<br>
                    <strong>Search:</strong> ${window.location.search}<br>
                    <strong>Hash:</strong> ${window.location.hash}
                </div>
            `;
        }
        
        // Run tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            testCSSLoading();
            showUrlInfo();
        });
    </script>
</body>
</html>
