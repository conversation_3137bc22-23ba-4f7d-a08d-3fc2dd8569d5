// Global configuration and utility functions
window.API_BASE_URL = 'http://localhost:7898/api/front';

// Utility function to get auth headers
function getAuthHeaders() {
    const token = localStorage.getItem('authToken');
    const headers = {
        'Content-Type': 'application/json'
    };
    
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
        // Also set as cookie for session-based auth
        document.cookie = `authToken=${token}; path=/`;
    }
    
    return headers;
}

// Utility function to make API calls
async function apiCall(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const defaultOptions = {
        headers: getAuthHeaders(),
        credentials: 'include' // Include cookies for session auth
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    // Merge headers
    if (options.headers) {
        finalOptions.headers = { ...defaultOptions.headers, ...options.headers };
    }
    
    try {
        const response = await fetch(url, finalOptions);
        const data = await response.json();
        
        // Log API calls for debugging
        logMessage(`API Call: ${options.method || 'GET'} ${endpoint}`, 'info');
        logMessage(`Response: ${JSON.stringify(data, null, 2)}`, 'info');
        
        return { response, data };
    } catch (error) {
        logMessage(`API Error: ${error.message}`, 'error');
        throw error;
    }
}

// Utility function to log messages
function logMessage(message, type = 'info') {
    const logContainer = document.getElementById('logContainer');
    if (!logContainer) return;
    
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
    
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

// Utility function to show loading state
function showLoading(buttonElement) {
    const originalText = buttonElement.textContent;
    buttonElement.innerHTML = '<span class="loading"></span> Loading...';
    buttonElement.disabled = true;
    
    return function hideLoading() {
        buttonElement.textContent = originalText;
        buttonElement.disabled = false;
    };
}

// Utility function to handle errors
function handleError(error, context = '') {
    console.error(`Error in ${context}:`, error);
    logMessage(`Error in ${context}: ${error.message}`, 'error');
    
    // Show user-friendly error message
    if (error.message.includes('fetch')) {
        logMessage('Network error - please check your connection and API URL', 'error');
    }
}

// Utility function to parse URL parameters
function getUrlParams() {
    const params = new URLSearchParams(window.location.search);
    const result = {};
    for (const [key, value] of params) {
        result[key] = value;
    }
    return result;
}

// Utility function to format dates
function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Utility function to validate email
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Utility function to show success message
function showSuccess(message) {
    logMessage(message, 'success');
    
    // You could also show a toast notification here
    const toast = document.createElement('div');
    toast.className = 'toast success';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        document.body.removeChild(toast);
    }, 3000);
}

// Utility function to redirect with return URL
function redirectWithReturn(url, returnUrl = null) {
    if (returnUrl) {
        const separator = url.includes('?') ? '&' : '?';
        window.location.href = `${url}${separator}returnUrl=${encodeURIComponent(returnUrl)}`;
    } else {
        window.location.href = url;
    }
}

// Check if user is authenticated
function isAuthenticated() {
    return !!localStorage.getItem('authToken');
}

// Get current user info
function getCurrentUser() {
    const userInfo = localStorage.getItem('userInfo');
    return userInfo ? JSON.parse(userInfo) : null;
}

// Save user session
function saveUserSession(token, userInfo) {
    localStorage.setItem('authToken', token);
    localStorage.setItem('userInfo', JSON.stringify(userInfo));
    
    // Also set as cookie for backend session
    document.cookie = `authToken=${token}; path=/`;
}

// Clear user session
function clearUserSession() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('userInfo');
    
    // Clear cookie
    document.cookie = 'authToken=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}

// Handle authentication redirect
function handleAuthRedirect() {
    const params = getUrlParams();
    const returnUrl = params.returnUrl;
    
    if (returnUrl && isAuthenticated()) {
        logMessage(`Redirecting to: ${returnUrl}`, 'info');
        window.location.href = returnUrl;
        return true;
    }
    
    return false;
}

// Initialize page
function initializePage() {
    // Load saved API URL
    const savedUrl = localStorage.getItem('apiBaseUrl');
    if (savedUrl) {
        window.API_BASE_URL = savedUrl;
    }
    
    // Handle authentication redirects
    handleAuthRedirect();
    
    logMessage('Page initialized', 'info');
}

// Export for use in other files
window.utils = {
    apiCall,
    logMessage,
    showLoading,
    handleError,
    getUrlParams,
    formatDate,
    isValidEmail,
    showSuccess,
    redirectWithReturn,
    isAuthenticated,
    getCurrentUser,
    saveUserSession,
    clearUserSession,
    handleAuthRedirect,
    initializePage
};
