// Test direct database creation to isolate the issue
require('dotenv').config();
const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

const { ComponentPrivateShares } = require('./models/component_private_shares.model');
const { Users } = require('./models/users.model');
const { Components } = require('./models/component.model');

async function testDirectCreate() {
    try {
        console.log('🔍 Testing Direct Database Creation...\n');
        
        // Find a user and their component
        const user = await Users.findOne({ is_verified: true }).lean();
        if (!user) {
            console.log('❌ No verified users found');
            return;
        }
        
        const component = await Components.findOne({ 
            created_by_user: user._id,
            component_state: 'private'
        }).lean();
        
        if (!component) {
            console.log('❌ No private components found for this user');
            return;
        }
        
        console.log(`👤 User: ${user.first_name} ${user.last_name} (${user.email})`);
        console.log(`   ID: ${user._id}`);
        console.log(`📦 Component: ${component.title}`);
        console.log(`   ID: ${component._id}`);
        console.log('');
        
        // Create share data exactly as it should be
        const shareData = {
            component_id: component._id,
            shared_by: user._id,
            access_type: 'by_link',
            access_token: 'test_token_' + Date.now(),
            expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
            access_duration: 'days',
            duration_days: 30,
            access_controls: ['copy', 'download'],
            link_name: 'Direct Test Link',
            status: 'pending',
            shared_with_user: null,
            shared_with_email: null
        };
        
        console.log('📋 Share data before creation:');
        console.log(JSON.stringify(shareData, null, 2));
        console.log('');
        
        // Create the share directly
        console.log('💾 Creating share in database...');
        const createdShare = await ComponentPrivateShares.create(shareData);
        
        console.log('✅ Share created with ID:', createdShare._id);
        console.log('');
        
        // Fetch it back to see what actually got saved
        console.log('🔍 Fetching created share from database...');
        const fetchedShare = await ComponentPrivateShares.findById(createdShare._id).lean();
        
        console.log('📋 Fetched share details:');
        console.log(`   ID: ${fetchedShare._id}`);
        console.log(`   Type: ${fetchedShare.access_type}`);
        console.log(`   Status: ${fetchedShare.status}`);
        console.log(`   Shared with user: ${fetchedShare.shared_with_user || 'NULL'}`);
        console.log(`   Shared with email: ${fetchedShare.shared_with_email || 'NULL'}`);
        console.log(`   Shared by: ${fetchedShare.shared_by}`);
        console.log(`   Token: ${fetchedShare.access_token}`);
        console.log(`   Link name: ${fetchedShare.link_name}`);
        console.log(`   Active: ${fetchedShare.is_active}`);
        console.log(`   Created: ${fetchedShare.created_at}`);
        console.log('');
        
        // Compare with expected values
        console.log('🔍 Validation:');
        
        const issues = [];
        
        if (fetchedShare.shared_with_user !== null) {
            issues.push(`❌ shared_with_user should be null, but is: ${fetchedShare.shared_with_user}`);
        } else {
            console.log('✅ shared_with_user is correctly null');
        }
        
        if (fetchedShare.shared_with_email !== null) {
            issues.push(`❌ shared_with_email should be null, but is: ${fetchedShare.shared_with_email}`);
        } else {
            console.log('✅ shared_with_email is correctly null');
        }
        
        if (fetchedShare.status !== 'pending') {
            issues.push(`❌ status should be 'pending', but is: ${fetchedShare.status}`);
        } else {
            console.log('✅ status is correctly pending');
        }
        
        if (fetchedShare.access_type !== 'by_link') {
            issues.push(`❌ access_type should be 'by_link', but is: ${fetchedShare.access_type}`);
        } else {
            console.log('✅ access_type is correctly by_link');
        }
        
        if (fetchedShare.shared_by.toString() !== user._id.toString()) {
            issues.push(`❌ shared_by should be ${user._id}, but is: ${fetchedShare.shared_by}`);
        } else {
            console.log('✅ shared_by is correctly set');
        }
        
        if (issues.length > 0) {
            console.log('\n🚨 ISSUES FOUND:');
            issues.forEach(issue => console.log(issue));
            
            console.log('\n🔍 This suggests the issue is in the pre-save middleware or model validation');
        } else {
            console.log('\n🎉 All validations passed! Direct creation works correctly.');
            console.log('🔍 This suggests the issue is in the service function, not the model');
        }
        
        // Clean up - delete the test share
        console.log('\n🧹 Cleaning up test share...');
        await ComponentPrivateShares.findByIdAndDelete(fetchedShare._id);
        console.log('✅ Test share deleted');
        
    } catch (error) {
        console.error('❌ Error during test:', error);
    } finally {
        mongoose.connection.close();
        console.log('\n✅ Database connection closed');
    }
}

testDirectCreate();
