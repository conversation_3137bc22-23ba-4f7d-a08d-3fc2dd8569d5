const crypto = require('crypto');
const mongoose = require('mongoose');
const moment = require('moment');

// Models
const { ComponentPrivateShares } = require('../models/component_private_shares.model');
const { Components } = require('../models/component.model');
const { Users } = require('../models/users.model');
const { ComponentUnlockHistory } = require('../models/component_unlock_history.model');

// Services
const { sendPrivateShareInvitationEmail, sendPrivateShareAcceptedNotification } = require('./send_email.service');
const logger = require('../config/logger');

// Constants
const { componentState, privateShareStatus, accessType } = require('../config/component.constant');

/**
 * Generate a secure access token for private sharing
 */
function generateAccessToken() {
    return crypto.randomBytes(32).toString('hex');
}

/**
 * Generate a public shareable link for component
 */
async function generatePublicShareableLink(componentId, sharedBy, linkName, accessDuration = 'undefined', durationDays = null, accessControls = []) {
    try {
        // Validate component exists, user owns it, and it's private
        const component = await Components.findOne({
            _id: componentId,
            created_by_user: sharedBy,
            component_state: componentState.PRIVATE
        }).lean();

        if (!component) {
            throw new Error('Component not found, you do not have permission to share it, or component must be private to share privately');
        }

        const accessToken = generateAccessToken();
        const expiresAt = accessDuration === 'days' && durationDays ? moment().add(durationDays, 'days').toDate() : null;

        const shareData = {
            component_id: componentId,
            shared_by: sharedBy,
            access_type: 'by_link',
            access_token: accessToken,
            expires_at: expiresAt,
            access_duration: accessDuration,
            duration_days: durationDays,
            access_controls: accessControls,
            link_name: linkName,
            status: 'accepted' // Links are immediately active
        };

        const share = await ComponentPrivateShares.create(shareData);
        
        return {
            success: true,
            share,
            shareableUrl: `${process.env.FRONTEND_URL || process.env.SITE_URL}/private-share/${accessToken}`
        };

    } catch (error) {
        logger.error(`Error in generatePublicShareableLink: ${error.message}`);
        throw error;
    }
}

/**
 * Validate email format
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Share a component privately with multiple emails (by invite)
 */
async function shareComponentPrivately(componentId, sharedBy, emails, personalMessage = '', accessDuration = 'undefined', durationDays = null, accessControls = []) {
    try {
        // Validate component exists, user owns it, and it's private
        const component = await Components.findOne({
            _id: componentId,
            created_by_user: sharedBy,
            component_state: componentState.PRIVATE
        }).lean();

        if (!component) {
            throw new Error('Component not found, you do not have permission to share it, or component must be private to share privately');
        }

        // Validate emails
        const validEmails = [];
        const invalidEmails = [];
        const duplicateEmails = [];

        for (const email of emails) {
            const trimmedEmail = email.trim().toLowerCase();
            
            if (!isValidEmail(trimmedEmail)) {
                invalidEmails.push(email);
                continue;
            }

            // Check if already shared with this email
            const existingShare = await ComponentPrivateShares.findOne({
                component_id: componentId,
                shared_with_email: trimmedEmail,
                status: { $in: [privateShareStatus.PENDING, privateShareStatus.ACCEPTED] },
                expires_at: { $gt: new Date() },
                is_active: true
            });

            if (existingShare) {
                duplicateEmails.push(trimmedEmail);
                continue;
            }

            validEmails.push(trimmedEmail);
        }

        if (validEmails.length === 0) {
            if(duplicateEmails.length > 0){
                throw new Error(`This component is already shared with: ${duplicateEmails.join(', ')}`);
            }
            throw new Error('No valid emails to share with');
        }

        // Get sharer details for email
        const sharer = await Users.findById(sharedBy).select('first_name last_name username email').lean();
        
        const shares = [];
        const expiresAt = accessDuration === 'days' && durationDays ? moment().add(durationDays, 'days').toDate() : null;

        // Create shares for valid emails
        for (const email of validEmails) {
            const accessToken = generateAccessToken();
            
            const shareData = {
                component_id: componentId,
                shared_by: sharedBy,
                access_type: 'by_invite',
                shared_with_email: email,
                access_token: accessToken,
                expires_at: expiresAt,
                access_duration: accessDuration,
                duration_days: durationDays,
                access_controls: accessControls,
                personal_message: personalMessage
            };

            const share = await ComponentPrivateShares.create(shareData);
            shares.push(share);

            // Send invitation email
            try {
                await sendPrivateShareInvitationEmail(
                    email,
                    sharer,
                    component,
                    accessToken,
                    personalMessage,
                    expiresAt
                );
            } catch (emailError) {
                logger.error(`Failed to send invitation email to ${email}: ${emailError.message}`);
                // Continue with other emails even if one fails
            }
        }

        return {
            success: true,
            shares,
            summary: {
                total: emails.length,
                successful: validEmails.length,
                invalid: invalidEmails,
                duplicates: duplicateEmails
            }
        };

    } catch (error) {
        logger.error(`Error in shareComponentPrivately: ${error.message}`);
        throw error;
    }
}

/**
 * Accept a private share invitation using access token
 */
async function acceptPrivateShare(accessToken, userId = null, userEmail = null) {
    try {
        const share = await ComponentPrivateShares.findOne({
            access_token: accessToken,
            status: { $in: [privateShareStatus.PENDING, privateShareStatus.ACCEPTED] },
            expires_at: { $gt: new Date() },
            is_active: true
        }).populate('component_id', 'title slug image_url created_by_user')
          .populate('shared_by', 'first_name last_name username');

        if (!share) {
            throw new Error('Invalid or expired invitation link');
        }

        // Check if component still exists and is accessible
        if (!share.component_id) {
            throw new Error('The shared component is no longer available');
        }

        // Update access information
        const updateData = {
            accessed_at: new Date(),
            $inc: { access_count: 1 }
        };
        
        // If user is logged in, link the share to user account
        if (userId) {
            // Verify email matches if user is logged in
            if (userEmail && userEmail.toLowerCase() !== share.shared_with_email) {
                throw new Error('This invitation was sent to a different email address');
            }
            
            updateData.shared_with_user = userId;
            updateData.status = privateShareStatus.ACCEPTED;
        }

        await ComponentPrivateShares.updateOne(
            { _id: share._id },
            updateData
        );

        return {
            success: true,
            component: share.component_id,
            sharedBy: share.shared_by,
            message: share.personal_message,
            accessControls: share.access_controls || [],
            accessType: share.access_type,
            requiresSignup: !userId && share.access_type === 'by_invite',
            requiresLogin: share.shared_with_user && !userId
        };

    } catch (error) {
        logger.error(`Error in acceptPrivateShare: ${error.message}`);
        throw error;
    }
}

/**
 * Check if user has access to a private component
 */
async function checkPrivateAccess(componentId, userId, userEmail) {
    try {
        if (!userId && !userEmail) {
            return { hasAccess: false, accessType: null };
        }

        // Check if user owns the component
        if (userId) {
            const isOwner = await Components.exists({
                _id: componentId,
                created_by_user: userId
            });

            if (isOwner) {
                return { hasAccess: true, accessType: accessType.OWNER };
            }
        }

        // Check if component is public
        const component = await Components.findById(componentId).select('component_state is_paid').lean();
        if (!component) {
            return { hasAccess: false, accessType: null };
        }

        if (component.component_state === componentState.PUBLISHED) {
            return { hasAccess: true, accessType: accessType.PUBLIC };
        }

        // Check private share access
        const hasPrivateAccess = await ComponentPrivateShares.hasAccess(componentId, userId, userEmail);
        if (hasPrivateAccess) {
            // If component is paid, check if user has unlocked it
            if (component.is_paid && userId) {
                const unlockHistory = await ComponentUnlockHistory.findOne({
                    component_id: componentId,
                    unlock_by: userId,
                    is_active: true
                }).lean();

                if (unlockHistory) {
                    return { hasAccess: true, accessType: accessType.UNLOCKED, requiresPayment: false };
                } else {
                    return { hasAccess: true, accessType: accessType.PRIVATE_SHARE, requiresPayment: true };
                }
            }
            return { hasAccess: true, accessType: accessType.PRIVATE_SHARE, requiresPayment: false };
        }

        return { hasAccess: false, accessType: null };

    } catch (error) {
        logger.error(`Error in checkPrivateAccess: ${error.message}`);
        return { hasAccess: false, accessType: null };
    }
}

/**
 * Get components shared by a user with pagination
 */
async function getMyPrivateShares(userId, options = {}) {
    try {
        const { status = null } = options;
        const limit = options.limit || 12;
        const skip = options.skip || 0;
        
        // Build match conditions
        const conditions = {
            shared_by: new mongoose.Types.ObjectId(userId),
            is_active: true
        };

        if (status) {
            conditions.status = status;
        }

        // Get counts for pagination
        const recordsTotal = await ComponentPrivateShares.countDocuments();
        const recordsFiltered = await ComponentPrivateShares.countDocuments(conditions);

        const query = [
            { $match: conditions },
            { $sort: { created_at: -1 } },
            { $skip: skip },
            { $limit: limit },
            {
                $lookup: {
                    from: 'components',
                    localField: 'component_id',
                    foreignField: '_id',
                    as: 'component'
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'shared_with_user',
                    foreignField: '_id',
                    as: 'shared_with_user'
                }
            },
            {
                $project: {
                    shared_with_email: 1,
                    status: 1,
                    expires_at: 1,
                    accessed_at: 1,
                    access_count: 1,
                    created_at: 1,
                    personal_message: 1,
                    component: { $arrayElemAt: ['$component', 0] },
                    shared_with_user: { $arrayElemAt: ['$shared_with_user', 0] }
                }
            },
            {
                $project: {
                    shared_with_email: 1,
                    status: 1,
                    expires_at: 1,
                    accessed_at: 1,
                    access_count: 1,
                    created_at: 1,
                    personal_message: 1,
                    'component.title': 1,
                    'component.slug': 1,
                    'component.image_url': 1,
                    'component.thumbnail_url': 1,
                    shared_with_user: {
                        first_name: 1,
                        last_name: 1,
                        username: 1,
                        avatar: 1
                    }
                }
            }
        ];

        const list = await ComponentPrivateShares.aggregate(query);

        return {
            recordsTotal,
            recordsFiltered,
            list
        };
    } catch (error) {
        logger.error(`Error in getMyPrivateShares: ${error.message}`, { 
            error: error.stack,
            userId,
            options 
        });
        throw error;
    }
}

/**
 * Get components shared with a user with pagination and Free/Unlocked filtering
 */
async function getComponentsSharedWithMe(userId, userEmail, options = {}) {
    try {
        const { status = null, paymentFilter = null } = options; // paymentFilter: 'free', 'unlocked', or null for all
        const limit = options.limit || 12;
        const skip = options.skip || 0;

        // Build match conditions for both user ID and email
        const conditions = {
            $or: [
                { shared_with_user: new mongoose.Types.ObjectId(userId) },
                { shared_with_email: userEmail }
            ],
            is_active: true
        };

        if (status) {
            conditions.status = status;
        }

        // Get counts for pagination
        const recordsTotal = await ComponentPrivateShares.countDocuments();
        const recordsFiltered = await ComponentPrivateShares.countDocuments(conditions);

        const query = [
            { $match: conditions },
            {
                $lookup: {
                    from: 'components',
                    localField: 'component_id',
                    foreignField: '_id',
                    as: 'component'
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'shared_by',
                    foreignField: '_id',
                    as: 'shared_by_user'
                }
            },
            {
                $lookup: {
                    from: 'component_unlock_histories',
                    let: { componentId: '$component_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$component_id', '$$componentId'] },
                                        { $eq: ['$unlock_by', new mongoose.Types.ObjectId(userId)] },
                                        { $eq: ['$is_active', true] }
                                    ]
                                }
                            }
                        }
                    ],
                    as: 'unlock_history'
                }
            },
            {
                $addFields: {
                    component: { $arrayElemAt: ['$component', 0] },
                    shared_by_user: { $arrayElemAt: ['$shared_by_user', 0] },
                    is_unlocked: { $gt: [{ $size: '$unlock_history' }, 0] }
                }
            },
            {
                $addFields: {
                    payment_status: {
                        $cond: {
                            if: { $eq: ['$component.is_paid', false] },
                            then: 'free',
                            else: {
                                $cond: {
                                    if: '$is_unlocked',
                                    then: 'unlocked',
                                    else: 'locked'
                                }
                            }
                        }
                    }
                }
            },
            // Filter by payment status if specified (exclude 'all' filter)
            ...(paymentFilter && paymentFilter !== 'all' ? [{ $match: { payment_status: paymentFilter } }] : []),
            { $sort: { created_at: -1 } },
            { $skip: skip },
            { $limit: limit },
            {
                $project: {
                    status: 1,
                    expires_at: 1,
                    accessed_at: 1,
                    access_count: 1,
                    created_at: 1,
                    personal_message: 1,
                    is_unlocked: 1,
                    payment_status: 1,
                    'component.title': 1,
                    'component.slug': 1,
                    'component.image_url': 1,
                    'component.thumbnail_url': 1,
                    'component.short_description': 1,
                    'component.views': 1,
                    'component.likes': 1,
                    'component.bookmarks': 1,
                    'component.created_at': 1,
                    'component.is_paid': 1,
                    'component.purchase_price': 1,
                    'component.component_state': 1,
                    shared_by_user: {
                        first_name: 1,
                        last_name: 1,
                        username: 1,
                        avatar: 1
                    }
                }
            }
        ];


        const list = await ComponentPrivateShares.aggregate(query);

        return {
            recordsTotal,
            recordsFiltered,
            list
        };
    } catch (error) {
        logger.error(`Error in getComponentsSharedWithMe: ${error.message}`, {
            error: error.stack,
            userId,
            userEmail,
            options
        });
        throw error;
    }
}

/**
 * Get statistics for components shared with user (Free/Unlocked counts)
 */
async function getSharedWithMeStatistics(userId, userEmail) {
    try {
        const conditions = {
            $or: [
                { shared_with_user: new mongoose.Types.ObjectId(userId) },
                { shared_with_email: userEmail }
            ],
            is_active: true,
            status: { $in: ['pending', 'accepted'] }
        };

        const query = [
            { $match: conditions },
            {
                $lookup: {
                    from: 'components',
                    localField: 'component_id',
                    foreignField: '_id',
                    as: 'component'
                }
            },
            {
                $lookup: {
                    from: 'component_unlock_histories',
                    let: { componentId: '$component_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$component_id', '$$componentId'] },
                                        { $eq: ['$unlock_by', new mongoose.Types.ObjectId(userId)] },
                                        { $eq: ['$is_active', true] }
                                    ]
                                }
                            }
                        }
                    ],
                    as: 'unlock_history'
                }
            },
            {
                $addFields: {
                    component: { $arrayElemAt: ['$component', 0] },
                    is_unlocked: { $gt: [{ $size: '$unlock_history' }, 0] }
                }
            },
            {
                $addFields: {
                    payment_status: {
                        $cond: {
                            if: { $eq: ['$component.is_paid', false] },
                            then: 'free',
                            else: {
                                $cond: {
                                    if: '$is_unlocked',
                                    then: 'unlocked',
                                    else: 'locked'
                                }
                            }
                        }
                    }
                }
            },
            {
                $group: {
                    _id: '$payment_status',
                    count: { $sum: 1 }
                }
            }
        ];

        const results = await ComponentPrivateShares.aggregate(query);

        // Format results
        const statistics = {
            total: 0,
            free: 0,
            unlocked: 0,
            locked: 0
        };

        results.forEach(result => {
            statistics[result._id] = result.count;
            statistics.total += result.count;
        });

        return statistics;

    } catch (error) {
        logger.error(`Error in getSharedWithMeStatistics: ${error.message}`);
        throw error;
    }
}

/**
 * Revoke a private share
 */
async function revokePrivateShare(shareId, userId) {
    try {
        const share = await ComponentPrivateShares.findOne({
            _id: shareId,
            shared_by: userId,
            status: { $in: [privateShareStatus.PENDING, privateShareStatus.ACCEPTED] },
            is_active: true
        });

        if (!share) {
            throw new Error('Share not found or you do not have permission to revoke it');
        }

        await ComponentPrivateShares.updateOne(
            { _id: shareId },
            {
                status: privateShareStatus.REVOKED,
                revoked_at: new Date(),
                revoked_by: userId
            }
        );

        return { success: true, message: 'Share revoked successfully' };

    } catch (error) {
        logger.error(`Error in revokePrivateShare: ${error.message}`);
        throw error;
    }
}

/**
 * Link pending shares when user signs up and notify share creators
 */
async function linkPendingSharesOnSignup(userId, email) {
    const session = await mongoose.startSession();
    session.startTransaction();
    
    try {
        // Get and update pending shares in a single atomic operation
        const pendingShares = await ComponentPrivateShares.find({
            shared_with_email: email,
            shared_with_user: null,
            status: 'pending',
            expires_at: { $gt: new Date() },
            is_active: true
        }).session(session);

        if (pendingShares.length === 0) {
            await session.commitTransaction();
            session.endSession();
            return 0;
        }

        const now = new Date();
        const shareIds = pendingShares.map(share => share._id);
        
        // Update all shares in bulk
        await ComponentPrivateShares.updateMany(
            { _id: { $in: shareIds } },
            {
                $set: {
                    shared_with_user: userId,
                    status: 'accepted',
                    accepted_at: now,
                    updated_at: now
                }
            }
        ).session(session);

        // Get component and user details for notifications
        const componentIds = [...new Set(pendingShares.map(share => share.component_id))];
        const components = await Components.find({ _id: { $in: componentIds } })
            .select('name created_by_user')
            .lean();

        const componentMap = components.reduce((acc, comp) => {
            acc[comp._id.toString()] = comp;
            return acc;
        }, {});

        // Send notifications to share creators
        const notificationPromises = pendingShares.map(async (share) => {
            const component = componentMap[share.component_id.toString()];
            if (!component) return;

            const [sharedBy, sharedWith] = await Promise.all([
                Users.findById(share.shared_by).select('email first_name').lean(),
                Users.findById(userId).select('email first_name username').lean()
            ]);

            if (sharedBy && sharedWith) {
                try {
                    await sendPrivateShareAcceptedNotification(
                        sharedBy.email, // to
                        sharedWith.first_name || sharedWith.username, // recipientName
                        component.name // componentTitle
                    );
                } catch (error) {
                    logger.error(`Failed to send share accepted notification for share ${share._id}: ${error.message}`);
                    // Don't fail the whole operation if notification fails
                }
            }
        });

        await Promise.all(notificationPromises);
        await session.commitTransaction();
        
        logger.info(`Successfully linked ${pendingShares.length} pending shares for user ${userId} (${email})`);
        return pendingShares.length;
        
    } catch (error) {
        await session.abortTransaction();
        logger.error(`Error linking pending shares for user ${userId} (${email}): ${error.message}`, {
            error: error.stack,
            userId,
            email
        });
        throw error; // Re-throw to be handled by the caller
    } finally {
        session.endSession();
    }
}

/**
 * Cleanup expired shares (to be called by cron job)
 */
async function cleanupExpiredShares() {
    try {
        const expiredCount = await ComponentPrivateShares.cleanupExpiredShares();
        logger.info(`Marked ${expiredCount} shares as expired`);
        return expiredCount;
    } catch (error) {
        logger.error(`Error cleaning up expired shares: ${error.message}`);
        return 0;
    }
}

/**
 * Get user's generated shareable links
 */
async function getMyShareableLinks(userId, options = {}) {
    try {
        const limit = options.limit || 12;
        const skip = options.skip || 0;
        
        const conditions = {
            shared_by: new mongoose.Types.ObjectId(userId),
            access_type: 'by_link',
            is_active: true
        };

        const recordsTotal = await ComponentPrivateShares.countDocuments({ access_type: 'by_link' });
        const recordsFiltered = await ComponentPrivateShares.countDocuments(conditions);

        const query = [
            { $match: conditions },
            { $sort: { created_at: -1 } },
            { $skip: skip },
            { $limit: limit },
            {
                $lookup: {
                    from: 'components',
                    localField: 'component_id',
                    foreignField: '_id',
                    as: 'component'
                }
            },
            {
                $project: {
                    link_name: 1,
                    access_token: 1,
                    status: 1,
                    expires_at: 1,
                    access_duration: 1,
                    duration_days: 1,
                    access_controls: 1,
                    access_count: 1,
                    created_at: 1,
                    component: { $arrayElemAt: ['$component', 0] },
                    shareableUrl: { $concat: [`${process.env.FRONTEND_URL || process.env.SITE_URL}/private-share/`, '$access_token'] }
                }
            },
            {
                $project: {
                    link_name: 1,
                    access_token: 1,
                    status: 1,
                    expires_at: 1,
                    access_duration: 1,
                    duration_days: 1,
                    access_controls: 1,
                    access_count: 1,
                    created_at: 1,
                    shareableUrl: 1,
                    'component.title': 1,
                    'component.slug': 1,
                    'component.image_url': 1,
                    'component.thumbnail_url': 1
                }
            }
        ];

        const list = await ComponentPrivateShares.aggregate(query);

        return {
            recordsTotal,
            recordsFiltered,
            list
        };
    } catch (error) {
        logger.error(`Error in getMyShareableLinks: ${error.message}`);
        throw error;
    }
}

/**
 * Get share statistics for a user
 */
async function getShareStatistics(userId) {
    try {
        const stats = await ComponentPrivateShares.aggregate([
            { $match: { shared_by: userId, is_active: true } },
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ]);

        const result = {
            total: 0,
            pending: 0,
            accepted: 0,
            expired: 0,
            revoked: 0
        };

        stats.forEach(stat => {
            result[stat._id] = stat.count;
            result.total += stat.count;
        });

        return result;
    } catch (error) {
        logger.error(`Error getting share statistics: ${error.message}`);
        throw error;
    }
}

/**
 * Refresh/regenerate a shareable link with new token
 */
async function refreshShareableLink(shareId, userId) {
    try {
        // Find the existing share and validate ownership
        const existingShare = await ComponentPrivateShares.findOne({
            _id: shareId,
            shared_by: userId,
            access_type: 'by_link',
            is_active: true
        }).populate('component_id', 'title component_state created_by_user').lean();

        if (!existingShare) {
            throw new Error('Shareable link not found or you do not have permission to refresh it');
        }

        // Validate component is still private
        if (existingShare.component_id.component_state !== componentState.PRIVATE) {
            throw new Error('Component must be private to refresh shareable link');
        }

        // Generate new access token
        const newAccessToken = generateAccessToken();

        // Update the existing share with new token and mark as refreshed
        const updatedShare = await ComponentPrivateShares.findByIdAndUpdate(
            shareId,
            {
                access_token: newAccessToken,
                updated_at: new Date(),
                access_count: 0 // Reset access count for new link
            },
            { new: true }
        ).lean();

        return {
            success: true,
            share: updatedShare,
            shareableUrl: `${process.env.FRONTEND_URL || process.env.SITE_URL}/private-share/${newAccessToken}`,
            message: 'Shareable link refreshed successfully. Previous link is no longer valid.'
        };

    } catch (error) {
        logger.error(`Error refreshing shareable link: ${error.message}`);
        throw error;
    }
}

/**
 * Bulk revoke multiple shares
 */
async function bulkRevokeShares(shareIds, userId) {
    try {
        if (!Array.isArray(shareIds) || shareIds.length === 0) {
            throw new Error('Share IDs array is required');
        }

        if (shareIds.length > 50) {
            throw new Error('Maximum 50 shares can be revoked at once');
        }

        const result = await ComponentPrivateShares.updateMany(
            {
                _id: { $in: shareIds },
                shared_by: userId,
                status: { $in: ['pending', 'accepted'] },
                is_active: true
            },
            {
                status: 'revoked',
                is_active: false,
                revoked_at: new Date(),
                revoked_by: userId
            }
        );

        return {
            success: true,
            revokedCount: result.modifiedCount,
            message: `${result.modifiedCount} share(s) revoked successfully`
        };

    } catch (error) {
        logger.error(`Error in bulk revoke shares: ${error.message}`);
        throw error;
    }
}

/**
 * Get detailed analytics for a specific share
 */
async function getShareAnalytics(shareId, userId) {
    try {
        const share = await ComponentPrivateShares.findOne({
            _id: shareId,
            shared_by: userId
        }).populate([
            {
                path: 'component_id',
                select: 'title slug component_state'
            },
            {
                path: 'shared_with_user',
                select: 'username first_name last_name email'
            }
        ]).lean();

        if (!share) {
            throw new Error('Share not found or you do not have permission to view it');
        }

        // Calculate additional metrics
        const daysSinceCreated = moment().diff(moment(share.created_at), 'days');
        const isExpired = share.expires_at && moment().isAfter(moment(share.expires_at));
        const daysUntilExpiry = share.expires_at ? moment(share.expires_at).diff(moment(), 'days') : null;

        return {
            ...share,
            analytics: {
                daysSinceCreated,
                isExpired,
                daysUntilExpiry,
                accessRate: share.access_count > 0 ? (share.access_count / Math.max(daysSinceCreated, 1)).toFixed(2) : 0,
                lastAccessedDaysAgo: share.accessed_at ? moment().diff(moment(share.accessed_at), 'days') : null
            }
        };

    } catch (error) {
        logger.error(`Error getting share analytics: ${error.message}`);
        throw error;
    }
}

module.exports = {
    shareComponentPrivately,
    acceptPrivateShare,
    checkPrivateAccess,
    getMyPrivateShares,
    getComponentsSharedWithMe,
    revokePrivateShare,
    linkPendingSharesOnSignup,
    cleanupExpiredShares,
    getShareStatistics,
    generatePublicShareableLink,
    getMyShareableLinks,
    generateAccessToken,
    refreshShareableLink,
    bulkRevokeShares,
    getShareAnalytics,
    getSharedWithMeStatistics
};