const crypto = require('crypto');
const mongoose = require('mongoose');
const moment = require('moment');

// Models
const { ComponentPrivateShares } = require('../models/component_private_shares.model');
const { Components } = require('../models/component.model');
const { Users } = require('../models/users.model');
const { ComponentUnlockHistory } = require('../models/component_unlock_history.model');

// Services
const { sendPrivateShareInvitationEmail, sendPrivateShareAcceptedNotification } = require('./send_email.service');
const logger = require('../config/logger');

// Constants
const { componentState, privateShareStatus, accessType, filterTypes, paymentFilterTypes } = require('../config/component.constant');
const { escapeRegex } = require('./general.helper');

/**
 * Generate a secure access token for private sharing
 */
function generateAccessToken() {
    return crypto.randomBytes(32).toString('hex');
}

/**
 * Generate a public shareable link for component
 */
async function generatePublicShareableLink(componentId, sharedBy, linkName, accessDuration = 'undefined', durationDays = null, accessControls = []) {
    try {
        // Validate component exists, user owns it, and it's private
        const component = await Components.findOne({
            _id: componentId,
            created_by_user: sharedBy,
            component_state: componentState.PRIVATE
        }).lean();

        if (!component) {
            throw new Error('Component not found, you do not have permission to share it, or component must be private to share privately');
        }

        const accessToken = generateAccessToken();
        const expiresAt = accessDuration === 'days' && durationDays ? moment().add(durationDays, 'days').toDate() : null;

        const shareData = {
            component_id: componentId,
            shared_by: sharedBy,
            access_type: 'by_link',
            access_token: accessToken,
            expires_at: expiresAt,
            access_duration: accessDuration,
            duration_days: durationDays,
            access_controls: accessControls,
            link_name: linkName,
            status: 'accepted', // Links are immediately active
            // Public shareable links should NOT have shared_with_user or shared_with_email
            // They should be accessible by anyone with the token
            shared_with_user: null,
            shared_with_email: null
        };

        const share = await ComponentPrivateShares.create(shareData);
        
        return {
            success: true,
            share,
            shareableUrl: `${process.env.SITE_URL}private-share/${accessToken}`
        };

    } catch (error) {
        logger.error(`Error in generatePublicShareableLink: ${error.message}`);
        throw error;
    }
}

/**
 * Validate email format
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Share a component privately with multiple emails (by invite)
 */
async function shareComponentPrivately(componentId, sharedBy, emails, personalMessage = '', accessDuration = 'undefined', durationDays = null, accessControls = []) {
    try {
        // Validate component exists, user owns it, and it's private
        const component = await Components.findOne({
            _id: componentId,
            created_by_user: sharedBy,
            component_state: componentState.PRIVATE
        }).lean();

        if (!component) {
            throw new Error('Component not found, you do not have permission to share it, or component must be private to share privately');
        }

        // Validate emails
        const validEmails = [];
        const invalidEmails = [];
        const duplicateEmails = [];

        for (const email of emails) {
            const trimmedEmail = email.trim().toLowerCase();
            
            if (!isValidEmail(trimmedEmail)) {
                invalidEmails.push(email);
                continue;
            }

            // Check if already shared with this email
            const existingShare = await ComponentPrivateShares.findOne({
                component_id: componentId,
                shared_with_email: trimmedEmail,
                status: { $in: [privateShareStatus.PENDING, privateShareStatus.ACCEPTED] },
                expires_at: { $gt: new Date() },
                is_active: true
            });

            if (existingShare) {
                duplicateEmails.push(trimmedEmail);
                continue;
            }

            validEmails.push(trimmedEmail);
        }

        if (validEmails.length === 0) {
            if(duplicateEmails.length > 0){
                throw new Error(`This component is already shared with: ${duplicateEmails.join(', ')}`);
            }
            throw new Error('No valid emails to share with');
        }

        // Get sharer details for email
        const sharer = await Users.findById(sharedBy).select('first_name last_name username email').lean();
        
        const shares = [];
        const expiresAt = accessDuration === 'days' && durationDays ? moment().add(durationDays, 'days').toDate() : null;

        // Create shares for valid emails
        for (const email of validEmails) {
            const accessToken = generateAccessToken();
            
            const shareData = {
                component_id: componentId,
                shared_by: sharedBy,
                access_type: 'by_invite',
                shared_with_email: email,
                access_token: accessToken,
                expires_at: expiresAt,
                access_duration: accessDuration,
                duration_days: durationDays,
                access_controls: accessControls,
                personal_message: personalMessage
            };

            const share = await ComponentPrivateShares.create(shareData);
            shares.push(share);

            // Send invitation email
            try {
                await sendPrivateShareInvitationEmail(
                    email,
                    sharer,
                    component,
                    accessToken,
                    personalMessage,
                    expiresAt
                );
            } catch (emailError) {
                logger.error(`Failed to send invitation email to ${email}: ${emailError.message}`);
                // Continue with other emails even if one fails
            }
        }

        return {
            success: true,
            shares,
            summary: {
                total: emails.length,
                successful: validEmails.length,
                invalid: invalidEmails,
                duplicates: duplicateEmails
            }
        };

    } catch (error) {
        logger.error(`Error in shareComponentPrivately: ${error.message}`);
        throw error;
    }
}

/**
 * Validate and process access token for both public links and private invites
 */
async function acceptPrivateShare(accessToken, userId = null, userEmail = null) {
    try {
        // Use the new token validation method
        const validation = await ComponentPrivateShares.validateTokenAccess(accessToken, userId, userEmail);

        if (!validation.isValid) {
            throw new Error(validation.error);
        }

        const { share, accessType, requiresSignup, requiresLogin } = validation;

        // Check if component still exists and is accessible
        if (!share.component_id) {
            throw new Error('The shared component is no longer available');
        }

        // Check if component is still private (security check)
        if (share.component_id.component_state !== componentState.PRIVATE) {
            throw new Error('This component is no longer private and can be accessed directly');
        }

        // If authentication is required, don't proceed with access
        if (requiresSignup || requiresLogin) {
            return {
                success: true,
                component: share.component_id,
                sharedBy: share.shared_by,
                message: share.personal_message,
                accessControls: share.access_controls || [],
                accessType: accessType,
                requiresSignup: requiresSignup,
                requiresLogin: requiresLogin,
                authRequired: true
            };
        }

        // Update access information only if user is authenticated
        const updateData = {
            accessed_at: new Date(),
            $inc: { access_count: 1 }
        };

        // For private invites, link to user account if logged in and not already linked
        if (accessType === 'private_invite' && userId && !share.shared_with_user) {
            updateData.shared_with_user = userId;
            updateData.status = privateShareStatus.ACCEPTED;
            updateData.accepted_at = new Date();

        }

        // For public links, update last accessed info
        if (accessType === 'public_link' && userId) {
            updateData.last_accessed_by = userId;
        }

        await ComponentPrivateShares.updateOne(
            { _id: share._id },
            updateData
        );

        // Send notification to share creator for private invites when first accepted
        if (accessType === 'private_invite' && userId && !share.shared_with_user) {
            try {
                const [sharedBy, sharedWith] = await Promise.all([
                    Users.findById(share.shared_by).select('email first_name').lean(),
                    Users.findById(userId).select('first_name username').lean()
                ]);

                if (sharedBy && sharedWith) {
                    await sendPrivateShareAcceptedNotification(
                        sharedBy.email,
                        sharedWith.first_name || sharedWith.username,
                        share.component_id.title
                    );
                }
            } catch (notificationError) {
                logger.error(`Failed to send share accepted notification: ${notificationError.message}`);
                // Don't fail the main operation if notification fails
            }
        }

        return {
            success: true,
            component: share.component_id,
            sharedBy: share.shared_by,
            message: share.personal_message,
            accessControls: share.access_controls || [],
            accessType: accessType,
            requiresSignup: false,
            requiresLogin: false,
            authRequired: false
        };

    } catch (error) {
        logger.error(`Error in acceptPrivateShare: ${error.message}`);
        throw error;
    }
}

/**
 * Check if user has access to a private component
 */
async function checkPrivateAccess(componentId, userId, userEmail) {
    try {
        if (!userId && !userEmail) {
            return { hasAccess: false, accessType: null };
        }

        // Check if user owns the component
        if (userId) {
            const isOwner = await Components.exists({
                _id: componentId,
                created_by_user: userId
            });

            if (isOwner) {
                return { hasAccess: true, accessType: accessType.OWNER };
            }
        }

        // Check if component is public
        const component = await Components.findById(componentId).select('component_state is_paid').lean();
        if (!component) {
            return { hasAccess: false, accessType: null };
        }

        if (component.component_state === componentState.PUBLISHED) {
            return { hasAccess: true, accessType: accessType.PUBLIC };
        }

        // Check private share access
        const hasPrivateAccess = await ComponentPrivateShares.hasAccess(componentId, userId, userEmail);
        if (hasPrivateAccess) {
            // If component is paid, check if user has unlocked it
            if (component.is_paid && userId) {
                const unlockHistory = await ComponentUnlockHistory.findOne({
                    component_id: componentId,
                    unlock_by: userId,
                    is_active: true
                }).lean();

                if (unlockHistory) {
                    return { hasAccess: true, accessType: accessType.UNLOCKED, requiresPayment: false };
                } else {
                    return { hasAccess: true, accessType: accessType.PRIVATE_SHARE, requiresPayment: true };
                }
            }
            return { hasAccess: true, accessType: accessType.PRIVATE_SHARE, requiresPayment: false };
        }

        return { hasAccess: false, accessType: null };

    } catch (error) {
        logger.error(`Error in checkPrivateAccess: ${error.message}`);
        return { hasAccess: false, accessType: null };
    }
}

/**
 * Get components shared by a user with pagination
 */
async function getMyPrivateShares(userId, options = {}) {
    try {
        const { status = null, searchText = null, sort_by = null } = options;
        const limit = parseInt(options.limit) || 12;
        const skip = parseInt(options.skip) || 0;

        // Build match conditions
        const conditions = {
            shared_by: new mongoose.Types.ObjectId(userId),
            is_active: true
        };

        if (status) {
            conditions.status = status;
        }

        // Add search functionality
        if (searchText) {
            const escapedSearchText = escapeRegex(searchText);
            conditions.$or = [
                { personal_message: { $regex: escapedSearchText, $options: 'i' } },
                { link_name: { $regex: escapedSearchText, $options: 'i' } },
                { shared_with_email: { $regex: escapedSearchText, $options: 'i' } }
            ];
        }

        // Get counts for pagination - use same conditions for both
        const recordsFiltered = await ComponentPrivateShares.countDocuments(conditions);
        const recordsTotal = await ComponentPrivateShares.countDocuments({ shared_by: new mongoose.Types.ObjectId(userId) });

        // Set default sort
        let sort = { created_at: -1 };

        // Apply sorting based on sort_by parameter
        if (sort_by) {
            if (sort_by === filterTypes.MOST_POPULAR) {
                sort = { access_count: -1, created_at: -1 };
            } else if (sort_by === filterTypes.RECENT_ADDITIONS) {
                sort = { created_at: -1 };
            } else if (sort_by === filterTypes.MOST_LIKED) {
                sort = { created_at: -1 }; // For shares, we don't have likes, so use created_at
            }
        }

        const pipeline = [
            { $match: conditions },
            { $sort: sort },
            { $skip: skip },
            { $limit: limit },
            {
                $lookup: {
                    from: 'components',
                    localField: 'component_id',
                    foreignField: '_id',
                    as: 'component',
                    pipeline: [
                        {
                            $project: {
                                title: 1,
                                slug: 1,
                                image_url: 1,
                                thumbnail_url: 1
                            }
                        }
                    ]
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'shared_with_user',
                    foreignField: '_id',
                    as: 'shared_with_user',
                    pipeline: [
                        {
                            $project: {
                                first_name: 1,
                                last_name: 1,
                                username: 1,
                                avatar: 1
                            }
                        }
                    ]
                }
            },
            {
                $project: {
                    access_token: 1,
                    shared_with_email: 1,
                    status: 1,
                    expires_at: 1,
                    accessed_at: 1,
                    access_count: 1,
                    created_at: 1,
                    personal_message: 1,
                    access_type: 1,
                    link_name: 1,
                    component: { $arrayElemAt: ['$component', 0] },
                    shared_with_user: { $arrayElemAt: ['$shared_with_user', 0] }
                }
            }
        ];

        const list = await ComponentPrivateShares.aggregate(pipeline);

        return {
            recordsTotal,
            recordsFiltered,
            list
        };
    } catch (error) {
        logger.error(`Error in getMyPrivateShares: ${error.message}`, {
            error: error.stack,
            userId,
            options
        });
        throw error;
    }
}

/**
 * Get components shared with a user with pagination and Free/Unlocked filtering
 */
async function getComponentsSharedWithMe(userId, userEmail, options = {}) {
    try {
        const { status = null, paymentFilter = null, searchText = null, sort_by = null } = options;
        const limit = parseInt(options.limit) || 12;
        const skip = parseInt(options.skip) || 0;

        // Build match conditions for user-specific invites and public links
        const conditions = {
            $or: [
                // User-specific private invites
                {
                    access_type: 'by_invite',
                    $or: [
                        { shared_with_user: new mongoose.Types.ObjectId(userId) },
                        { shared_with_email: userEmail }
                    ]
                },
                // Public shareable links (accessible by anyone)
                {
                    access_type: 'by_link',
                    shared_with_user: null,
                    shared_with_email: null
                }
            ],
            is_active: true,
            status: { $in: ['pending', 'accepted'] },
            $or: [
                { expires_at: { $gt: new Date() } },
                { expires_at: null }
            ]
        };

        if (status) {
            conditions.status = status;
        }

        // Get counts for pagination - optimize by using same base conditions
        const recordsFiltered = await ComponentPrivateShares.countDocuments(conditions);
        const recordsTotal = recordsFiltered; // For shared with me, total = filtered

        // Set default sort
        let sort = { created_at: -1 };

        // Apply sorting based on payment filter (for shared-with-me, we sort by payment status)
        if (sort_by) {
            if (sort_by === paymentFilterTypes.FREE) {
                // Sort free components first, then by created_at
                sort = { 'component.is_paid': 1, created_at: -1 };
            } else if (sort_by === paymentFilterTypes.UNLOCKED) {
                // Sort unlocked components first, then by created_at
                sort = { is_unlocked: -1, created_at: -1 };
            } else if (sort_by === paymentFilterTypes.LOCKED) {
                // Sort locked components first, then by created_at
                sort = { 'component.is_paid': -1, is_unlocked: 1, created_at: -1 };
            } else {
                // Default sorting for 'all' or other values
                sort = { created_at: -1 };
            }
        }

        const pipeline = [
            { $match: conditions },
            {
                $lookup: {
                    from: 'components',
                    localField: 'component_id',
                    foreignField: '_id',
                    as: 'component',
                    pipeline: [
                        {
                            $project: {
                                title: 1,
                                slug: 1,
                                image_url: 1,
                                thumbnail_url: 1,
                                short_description: 1,
                                views: 1,
                                likes: 1,
                                bookmarks: 1,
                                created_at: 1,
                                is_paid: 1,
                                purchase_price: 1,
                                component_state: 1
                            }
                        }
                    ]
                }
            },
            // Add search filter if provided
            ...(searchText ? [{
                $match: {
                    $or: [
                        { 'component.title': { $regex: escapeRegex(searchText), $options: 'i' } },
                        { 'component.short_description': { $regex: escapeRegex(searchText), $options: 'i' } },
                        { personal_message: { $regex: escapeRegex(searchText), $options: 'i' } }
                    ]
                }
            }] : []),
            {
                $lookup: {
                    from: 'users',
                    localField: 'shared_by',
                    foreignField: '_id',
                    as: 'shared_by_user',
                    pipeline: [
                        {
                            $project: {
                                first_name: 1,
                                last_name: 1,
                                username: 1,
                                avatar: 1
                            }
                        }
                    ]
                }
            },
            {
                $lookup: {
                    from: 'component_unlock_histories',
                    let: { componentId: '$component_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$component_id', '$$componentId'] },
                                        { $eq: ['$unlock_by', new mongoose.Types.ObjectId(userId)] },
                                        { $eq: ['$is_active', true] }
                                    ]
                                }
                            }
                        },
                        { $limit: 1 }
                    ],
                    as: 'unlock_history'
                }
            },
            {
                $set: {
                    component: { $arrayElemAt: ['$component', 0] },
                    shared_by_user: { $arrayElemAt: ['$shared_by_user', 0] },
                    is_unlocked: { $gt: [{ $size: '$unlock_history' }, 0] },
                    payment_status: {
                        $switch: {
                            branches: [
                                {
                                    case: { $eq: [{ $arrayElemAt: ['$component.is_paid', 0] }, false] },
                                    then: 'free'
                                },
                                {
                                    case: { $gt: [{ $size: '$unlock_history' }, 0] },
                                    then: 'unlocked'
                                }
                            ],
                            default: 'locked'
                        }
                    }
                }
            },
            // Filter by payment status if specified
            ...(paymentFilter && paymentFilter !== paymentFilterTypes.ALL ? [{ $match: { payment_status: paymentFilter } }] : []),
            { $sort: sort },
            { $skip: skip },
            { $limit: limit },
            {
                $project: {
                    access_token: 1,
                    status: 1,
                    expires_at: 1,
                    accessed_at: 1,
                    access_count: 1,
                    created_at: 1,
                    personal_message: 1,
                    access_type: 1,
                    shared_with_email: 1,
                    shared_with_user: 1,
                    is_unlocked: 1,
                    payment_status: 1,
                    component: 1,
                    shared_by_user: 1
                }
            }
        ];

        logger.info(`getComponentsSharedWithMe aggregation conditions:`, JSON.stringify(conditions, null, 2));
        logger.info(`getComponentsSharedWithMe pipeline length:`, pipeline.length);

        const list = await ComponentPrivateShares.aggregate(pipeline);

        logger.info(`getComponentsSharedWithMe aggregation result:`, {
            recordsTotal,
            recordsFiltered,
            listLength: list.length,
            firstItem: list[0] || null
        });

        return {
            recordsTotal,
            recordsFiltered,
            list
        };
    } catch (error) {
        logger.error(`Error in getComponentsSharedWithMe: ${error.message}`, {
            error: error.stack,
            userId,
            userEmail,
            options
        });
        throw error;
    }
}

/**
 * Get statistics for components shared with user (Free/Unlocked counts)
 */
async function getSharedWithMeStatistics(userId, userEmail) {
    try {
        const conditions = {
            $or: [
                // User-specific private invites
                {
                    access_type: 'by_invite',
                    $or: [
                        { shared_with_user: new mongoose.Types.ObjectId(userId) },
                        { shared_with_email: userEmail }
                    ]
                },
                // Public shareable links (accessible by anyone)
                {
                    access_type: 'by_link',
                    shared_with_user: null,
                    shared_with_email: null
                }
            ],
            is_active: true,
            status: { $in: ['pending', 'accepted'] },
            $or: [
                { expires_at: { $gt: new Date() } },
                { expires_at: null }
            ]
        };

        const pipeline = [
            { $match: conditions },
            {
                $lookup: {
                    from: 'components',
                    localField: 'component_id',
                    foreignField: '_id',
                    as: 'component',
                    pipeline: [
                        {
                            $project: {
                                is_paid: 1
                            }
                        }
                    ]
                }
            },
            {
                $lookup: {
                    from: 'component_unlock_histories',
                    let: { componentId: '$component_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$component_id', '$$componentId'] },
                                        { $eq: ['$unlock_by', new mongoose.Types.ObjectId(userId)] },
                                        { $eq: ['$is_active', true] }
                                    ]
                                }
                            }
                        },
                        { $limit: 1 }
                    ],
                    as: 'unlock_history'
                }
            },
            {
                $set: {
                    component: { $arrayElemAt: ['$component', 0] },
                    payment_status: {
                        $switch: {
                            branches: [
                                {
                                    case: { $eq: [{ $arrayElemAt: ['$component.is_paid', 0] }, false] },
                                    then: 'free'
                                },
                                {
                                    case: { $gt: [{ $size: '$unlock_history' }, 0] },
                                    then: 'unlocked'
                                }
                            ],
                            default: 'locked'
                        }
                    }
                }
            },
            {
                $group: {
                    _id: '$payment_status',
                    count: { $sum: 1 }
                }
            }
        ];

        const results = await ComponentPrivateShares.aggregate(pipeline);

        // Format results
        const statistics = {
            total: 0,
            free: 0,
            unlocked: 0,
            locked: 0
        };

        results.forEach(result => {
            statistics[result._id] = result.count;
            statistics.total += result.count;
        });

        return statistics;

    } catch (error) {
        logger.error(`Error in getSharedWithMeStatistics: ${error.message}`);
        throw error;
    }
}

/**
 * Revoke a private share
 */
async function revokePrivateShare(shareId, userId) {
    try {
        const share = await ComponentPrivateShares.findOne({
            _id: shareId,
            shared_by: userId,
            status: { $in: [privateShareStatus.PENDING, privateShareStatus.ACCEPTED] },
            is_active: true
        });

        if (!share) {
            throw new Error('Share not found or you do not have permission to revoke it');
        }

        await ComponentPrivateShares.updateOne(
            { _id: shareId },
            {
                status: privateShareStatus.REVOKED,
                revoked_at: new Date(),
                revoked_by: userId
            }
        );

        return { success: true, message: 'Share revoked successfully' };

    } catch (error) {
        logger.error(`Error in revokePrivateShare: ${error.message}`);
        throw error;
    }
}

/**
 * Link pending shares when user signs up and notify share creators
 */
async function linkPendingSharesOnSignup(userId, email) {
    const session = await mongoose.startSession();
    session.startTransaction();
    
    try {
        // Get and update pending shares in a single atomic operation
        const pendingShares = await ComponentPrivateShares.find({
            shared_with_email: email,
            shared_with_user: null,
            status: 'pending',
            expires_at: { $gt: new Date() },
            is_active: true
        }).session(session);

        if (pendingShares.length === 0) {
            await session.commitTransaction();
            session.endSession();
            return 0;
        }

        const now = new Date();
        const shareIds = pendingShares.map(share => share._id);
        
        // Update all shares in bulk
        await ComponentPrivateShares.updateMany(
            { _id: { $in: shareIds } },
            {
                $set: {
                    shared_with_user: userId,
                    status: 'accepted',
                    accepted_at: now,
                    updated_at: now
                }
            }
        ).session(session);

        // Get component and user details for notifications
        const componentIds = [...new Set(pendingShares.map(share => share.component_id))];
        const components = await Components.find({ _id: { $in: componentIds } })
            .select('name created_by_user')
            .lean();

        const componentMap = components.reduce((acc, comp) => {
            acc[comp._id.toString()] = comp;
            return acc;
        }, {});

        // Send notifications to share creators
        const notificationPromises = pendingShares.map(async (share) => {
            const component = componentMap[share.component_id.toString()];
            if (!component) return;

            const [sharedBy, sharedWith] = await Promise.all([
                Users.findById(share.shared_by).select('email first_name').lean(),
                Users.findById(userId).select('email first_name username').lean()
            ]);

            if (sharedBy && sharedWith) {
                try {
                    await sendPrivateShareAcceptedNotification(
                        sharedBy.email, // to
                        sharedWith.first_name || sharedWith.username, // recipientName
                        component.name // componentTitle
                    );
                } catch (error) {
                    logger.error(`Failed to send share accepted notification for share ${share._id}: ${error.message}`);
                    // Don't fail the whole operation if notification fails
                }
            }
        });

        await Promise.all(notificationPromises);
        await session.commitTransaction();
        
        logger.info(`Successfully linked ${pendingShares.length} pending shares for user ${userId} (${email})`);
        return pendingShares.length;
        
    } catch (error) {
        await session.abortTransaction();
        logger.error(`Error linking pending shares for user ${userId} (${email}): ${error.message}`, {
            error: error.stack,
            userId,
            email
        });
        throw error; // Re-throw to be handled by the caller
    } finally {
        session.endSession();
    }
}

/**
 * Cleanup expired shares (to be called by cron job)
 */
async function cleanupExpiredShares() {
    try {
        const expiredCount = await ComponentPrivateShares.cleanupExpiredShares();
        logger.info(`Marked ${expiredCount} shares as expired`);
        return expiredCount;
    } catch (error) {
        logger.error(`Error cleaning up expired shares: ${error.message}`);
        return 0;
    }
}

/**
 * Get user's generated shareable links
 */
async function getMyShareableLinks(userId, options = {}) {
    try {
        const { searchText = null, sort_by = null } = options;
        const limit = parseInt(options.limit) || 12;
        const skip = parseInt(options.skip) || 0;

        const conditions = {
            shared_by: new mongoose.Types.ObjectId(userId),
            access_type: 'by_link',
            is_active: true
        };

        // Add search functionality
        if (searchText) {
            const escapedSearchText = escapeRegex(searchText);
            conditions.$or = [
                { link_name: { $regex: escapedSearchText, $options: 'i' } }
            ];
        }

        // Get counts for pagination - optimize by using same conditions
        const recordsFiltered = await ComponentPrivateShares.countDocuments(conditions);
        const recordsTotal = await ComponentPrivateShares.countDocuments({
            shared_by: new mongoose.Types.ObjectId(userId),
            access_type: 'by_link'
        });

        // Set default sort
        let sort = { created_at: -1 };

        // Apply sorting based on sort_by parameter
        if (sort_by) {
            if (sort_by === filterTypes.MOST_POPULAR) {
                sort = { access_count: -1, created_at: -1 };
            } else if (sort_by === filterTypes.RECENT_ADDITIONS) {
                sort = { created_at: -1 };
            } else if (sort_by === filterTypes.MOST_LIKED) {
                sort = { created_at: -1 }; // For links, we don't have likes, so use created_at
            }
        }

        const pipeline = [
            { $match: conditions },
            { $sort: sort },
            { $skip: skip },
            { $limit: limit },
            {
                $lookup: {
                    from: 'components',
                    localField: 'component_id',
                    foreignField: '_id',
                    as: 'component',
                    pipeline: [
                        {
                            $project: {
                                title: 1,
                                slug: 1,
                                image_url: 1,
                                thumbnail_url: 1
                            }
                        }
                    ]
                }
            },
            {
                $project: {
                    link_name: 1,
                    access_token: 1,
                    status: 1,
                    expires_at: 1,
                    access_duration: 1,
                    duration_days: 1,
                    access_controls: 1,
                    access_count: 1,
                    created_at: 1,
                    component: { $arrayElemAt: ['$component', 0] },
                    shareableUrl: { $concat: [`${process.env.SITE_URL}private-share/`, '$access_token'] }
                }
            }
        ];

        const list = await ComponentPrivateShares.aggregate(pipeline);

        return {
            recordsTotal,
            recordsFiltered,
            list
        };
    } catch (error) {
        logger.error(`Error in getMyShareableLinks: ${error.message}`);
        throw error;
    }
}

/**
 * Get share statistics for a user
 */
async function getShareStatistics(userId) {
    try {
        const pipeline = [
            {
                $match: {
                    shared_by: new mongoose.Types.ObjectId(userId),
                    is_active: true
                }
            },
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ];

        const stats = await ComponentPrivateShares.aggregate(pipeline);

        const result = {
            total: 0,
            pending: 0,
            accepted: 0,
            expired: 0,
            revoked: 0
        };

        stats.forEach(stat => {
            result[stat._id] = stat.count;
            result.total += stat.count;
        });

        return result;
    } catch (error) {
        logger.error(`Error getting share statistics: ${error.message}`);
        throw error;
    }
}

/**
 * Refresh/regenerate a shareable link with new token
 */
async function refreshShareableLink(shareId, userId) {
    try {
        // Find the existing share and validate ownership
        const existingShare = await ComponentPrivateShares.findOne({
            _id: shareId,
            shared_by: userId,
            access_type: 'by_link',
            is_active: true
        }).populate('component_id', 'title component_state created_by_user').lean();

        if (!existingShare) {
            throw new Error('Shareable link not found or you do not have permission to refresh it');
        }

        // Validate component is still private
        if (existingShare.component_id.component_state !== componentState.PRIVATE) {
            throw new Error('Component must be private to refresh shareable link');
        }

        // Generate new access token
        const newAccessToken = generateAccessToken();

        // Update the existing share with new token and mark as refreshed
        const updatedShare = await ComponentPrivateShares.findByIdAndUpdate(
            shareId,
            {
                access_token: newAccessToken,
                updated_at: new Date(),
                access_count: 0 // Reset access count for new link
            },
            { new: true }
        ).lean();

        return {
            success: true,
            share: updatedShare,
            shareableUrl: `${process.env.SITE_URL}private-share/${newAccessToken}`,
            message: 'Shareable link refreshed successfully. Previous link is no longer valid.'
        };

    } catch (error) {
        logger.error(`Error refreshing shareable link: ${error.message}`);
        throw error;
    }
}

/**
 * Bulk revoke multiple shares
 */
async function bulkRevokeShares(shareIds, userId) {
    try {
        if (!Array.isArray(shareIds) || shareIds.length === 0) {
            throw new Error('Share IDs array is required');
        }

        if (shareIds.length > 50) {
            throw new Error('Maximum 50 shares can be revoked at once');
        }

        const result = await ComponentPrivateShares.updateMany(
            {
                _id: { $in: shareIds },
                shared_by: userId,
                status: { $in: ['pending', 'accepted'] },
                is_active: true
            },
            {
                status: 'revoked',
                is_active: false,
                revoked_at: new Date(),
                revoked_by: userId
            }
        );

        return {
            success: true,
            revokedCount: result.modifiedCount,
            message: `${result.modifiedCount} share(s) revoked successfully`
        };

    } catch (error) {
        logger.error(`Error in bulk revoke shares: ${error.message}`);
        throw error;
    }
}

/**
 * Get detailed analytics for a specific share (optimized)
 */
async function getShareAnalytics(shareId, userId) {
    try {
        const pipeline = [
            {
                $match: {
                    _id: new mongoose.Types.ObjectId(shareId),
                    shared_by: new mongoose.Types.ObjectId(userId)
                }
            },
            {
                $lookup: {
                    from: 'components',
                    localField: 'component_id',
                    foreignField: '_id',
                    as: 'component',
                    pipeline: [
                        {
                            $project: {
                                title: 1,
                                slug: 1,
                                component_state: 1
                            }
                        }
                    ]
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'shared_with_user',
                    foreignField: '_id',
                    as: 'shared_with_user',
                    pipeline: [
                        {
                            $project: {
                                username: 1,
                                first_name: 1,
                                last_name: 1,
                                email: 1
                            }
                        }
                    ]
                }
            },
            {
                $set: {
                    component: { $arrayElemAt: ['$component', 0] },
                    shared_with_user: { $arrayElemAt: ['$shared_with_user', 0] },
                    daysSinceCreated: {
                        $divide: [
                            { $subtract: [new Date(), '$created_at'] },
                            86400000 // milliseconds in a day
                        ]
                    },
                    isExpired: {
                        $cond: {
                            if: '$expires_at',
                            then: { $gt: [new Date(), '$expires_at'] },
                            else: false
                        }
                    },
                    daysUntilExpiry: {
                        $cond: {
                            if: '$expires_at',
                            then: {
                                $divide: [
                                    { $subtract: ['$expires_at', new Date()] },
                                    86400000
                                ]
                            },
                            else: null
                        }
                    },
                    lastAccessedDaysAgo: {
                        $cond: {
                            if: '$accessed_at',
                            then: {
                                $divide: [
                                    { $subtract: [new Date(), '$accessed_at'] },
                                    86400000
                                ]
                            },
                            else: null
                        }
                    }
                }
            },
            {
                $set: {
                    accessRate: {
                        $cond: {
                            if: { $gt: ['$access_count', 0] },
                            then: {
                                $round: [
                                    { $divide: ['$access_count', { $max: ['$daysSinceCreated', 1] }] },
                                    2
                                ]
                            },
                            else: 0
                        }
                    }
                }
            }
        ];

        const results = await ComponentPrivateShares.aggregate(pipeline);
        const share = results[0];

        if (!share) {
            throw new Error('Share not found or you do not have permission to view it');
        }

        return share;

    } catch (error) {
        logger.error(`Error getting share analytics: ${error.message}`);
        throw error;
    }
}

module.exports = {
    // Core sharing functions
    shareComponentPrivately,
    generatePublicShareableLink,
    acceptPrivateShare,

    // Access control
    checkPrivateAccess,

    // Data retrieval functions
    getMyPrivateShares,
    getMyShareableLinks,
    getComponentsSharedWithMe,
    getSharedWithMeStatistics,

    // Management functions
    revokePrivateShare,
    bulkRevokeShares,
    refreshShareableLink,

    // Statistics and analytics
    getShareStatistics,
    getShareAnalytics,

    // Utility functions
    linkPendingSharesOnSignup,
    cleanupExpiredShares,
    generateAccessToken
};