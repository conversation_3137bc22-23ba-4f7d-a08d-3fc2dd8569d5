const crypto = require('crypto');
const mongoose = require('mongoose');
const moment = require('moment');

// Models
const { ComponentPrivateShares } = require('../models/component_private_shares.model');
const { Components } = require('../models/component.model');
const { Users } = require('../models/users.model');
const { ComponentUnlockHistory } = require('../models/component_unlock_history.model');

// Services
const { sendPrivateShareInvitationEmail, sendPrivateShareAcceptedNotification } = require('./send_email.service');
const logger = require('../config/logger');

// Constants
const { componentState, privateShareStatus, accessType, filterTypes, paymentFilterTypes, shareType, accessControls, accessDuration } = require('../config/component.constant');
const { escapeRegex } = require('./general.helper');

/**
 * Generate a secure access token for private sharing
 */
function generateAccessToken() {
    return crypto.randomBytes(32).toString('hex');
}

/**
 * Generate a public shareable link for component
 */
async function generatePublicShareableLink(componentId, sharedBy, linkName, accessDuration = 'undefined', durationDays = null, accessControls = []) {
    try {
        // Validate component exists, user owns it, and it's private
        const component = await Components.findOne({
            _id: componentId,
            created_by_user: sharedBy,
            component_state: componentState.PRIVATE
        }).lean();

        if (!component) {
            throw new Error('Component not found, you do not have permission to share it, or component must be private to share privately');
        }

        const accessToken = generateAccessToken();
        const expiresAt = accessDuration === accessDuration.DAYS && durationDays ? moment().add(durationDays, 'days').toDate() : null;

        const shareData = {
            component_id: componentId,
            shared_by: sharedBy,
            access_type: shareType.BY_LINK,
            access_token: accessToken,
            expires_at: expiresAt,
            access_duration: accessDuration,
            duration_days: durationDays,
            access_controls: accessControls,
            link_name: linkName,
            status: privateShareStatus.PENDING, // All shares start as pending until validated
            // Public shareable links should NOT have shared_with_user or shared_with_email initially
            // They get assigned when someone accesses and validates the link
            shared_with_user: null,
            shared_with_email: null
        };

        // Auto-expire previous active links for the same component by the same user
        logger.info(`🔄 Auto-expiring previous links for component ${componentId} by user ${sharedBy}`);

        const expiredCount = await ComponentPrivateShares.updateMany(
            {
                component_id: componentId,
                shared_by: sharedBy,
                access_type: 'by_link',
                is_active: true,
                status: { $in: ['pending', 'accepted'] }
            },
            {
                $set: {
                    is_active: false,
                    status: 'revoked',
                    updated_at: new Date(),
                    revoked_reason: 'Auto-expired due to new link generation'
                }
            }
        );

        logger.info(`✅ Auto-expired ${expiredCount.modifiedCount} previous links`);

        // Debug logging
        logger.info(`🔍 generatePublicShareableLink DEBUG:`, {
            componentId: componentId.toString(),
            sharedBy: sharedBy.toString(),
            expiredPreviousLinks: expiredCount.modifiedCount,
            shareDataBeforeCreate: {
                ...shareData,
                component_id: shareData.component_id.toString(),
                shared_by: shareData.shared_by.toString()
            },
            linkName: linkName
        });

        const share = await ComponentPrivateShares.create(shareData);

        // Debug logging after creation
        logger.info(`📝 Share created:`, {
            shareId: share._id.toString(),
            shared_with_user: share.shared_with_user,
            shared_with_email: share.shared_with_email,
            status: share.status,
            access_type: share.access_type
        });

        // Verify the share was created correctly
        if (share.shared_with_user !== null) {
            logger.error(`🚨 CRITICAL ERROR: Link share was created with shared_with_user set to ${share.shared_with_user}!`);
            // Force fix the share
            await ComponentPrivateShares.findByIdAndUpdate(share._id, {
                shared_with_user: null,
                shared_with_email: null,
                status: 'pending'
            });
            logger.info(`🔧 Fixed share ${share._id} - reset user assignment and status`);

            // Reload the share to return correct data
            const fixedShare = await ComponentPrivateShares.findById(share._id).lean();
            return {
                success: true,
                share: fixedShare,
                shareableUrl: `${process.env.SITE_URL}private-share/${accessToken}`
            };
        }
        
        return {
            success: true,
            share,
            shareableUrl: `${process.env.SITE_URL}private-share/${accessToken}`
        };

    } catch (error) {
        logger.error(`Error in generatePublicShareableLink: ${error.message}`);
        throw error;
    }
}

/**
 * Validate email format
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Share a component privately with multiple emails (by invite)
 */
async function shareComponentPrivately(componentId, sharedBy, emails, personalMessage = '', accessDuration = 'undefined', durationDays = null, accessControls = []) {
    try {
        // Validate component exists, user owns it, and it's private
        const component = await Components.findOne({
            _id: componentId,
            created_by_user: sharedBy,
            component_state: componentState.PRIVATE
        }).lean();

        if (!component) {
            throw new Error('Component not found, you do not have permission to share it, or component must be private to share privately');
        }

        // Validate emails
        const validEmails = [];
        const invalidEmails = [];
        const duplicateEmails = [];

        for (const email of emails) {
            const trimmedEmail = email.trim().toLowerCase();
            
            if (!isValidEmail(trimmedEmail)) {
                invalidEmails.push(email);
                continue;
            }

            // Check if already shared with this email
            const existingShare = await ComponentPrivateShares.findOne({
                component_id: componentId,
                shared_with_email: trimmedEmail,
                status: { $in: [privateShareStatus.PENDING, privateShareStatus.ACCEPTED] },
                expires_at: { $gt: new Date() },
                is_active: true
            });

            if (existingShare) {
                duplicateEmails.push(trimmedEmail);
                continue;
            }

            validEmails.push(trimmedEmail);
        }

        if (validEmails.length === 0) {
            if(duplicateEmails.length > 0){
                throw new Error(`This component is already shared with: ${duplicateEmails.join(', ')}`);
            }
            throw new Error('No valid emails to share with');
        }

        // Get sharer details for email
        const sharer = await Users.findById(sharedBy).select('first_name last_name username email').lean();
        
        const shares = [];
        const expiresAt = accessDuration === 'days' && durationDays ? moment().add(durationDays, 'days').toDate() : null;

        // Create shares for valid emails
        for (const email of validEmails) {
            const accessToken = generateAccessToken();
            
            const shareData = {
                component_id: componentId,
                shared_by: sharedBy,
                access_type: 'by_invite',
                shared_with_email: email,
                access_token: accessToken,
                expires_at: expiresAt,
                access_duration: accessDuration,
                duration_days: durationDays,
                access_controls: accessControls,
                personal_message: personalMessage
            };

            const share = await ComponentPrivateShares.create(shareData);
            shares.push(share);

            // Send invitation email
            try {
                await sendPrivateShareInvitationEmail(
                    email,
                    sharer,
                    component,
                    accessToken,
                    personalMessage,
                    expiresAt
                );
            } catch (emailError) {
                logger.error(`Failed to send invitation email to ${email}: ${emailError.message}`);
                // Continue with other emails even if one fails
            }
        }

        return {
            success: true,
            shares,
            summary: {
                total: emails.length,
                successful: validEmails.length,
                invalid: invalidEmails,
                duplicates: duplicateEmails
            }
        };

    } catch (error) {
        logger.error(`Error in shareComponentPrivately: ${error.message}`);
        throw error;
    }
}

/**
 * Validate and process access token for both public links and private invites
 */
async function acceptPrivateShare(accessToken, userId = null, userEmail = null) {
    try {
        // Use the new token validation method
        const validation = await ComponentPrivateShares.validateTokenAccess(accessToken, userId, userEmail);

        if (!validation.isValid) {
            throw new Error(validation.error);
        }

        const { share, accessType, requiresSignup, requiresLogin } = validation;

        // Check if component still exists and is accessible
        if (!share.component_id) {
            throw new Error('The shared component is no longer available');
        }

        // Check if component is still private (security check)
        if (share.component_id.component_state !== componentState.PRIVATE) {
            throw new Error('This component is no longer private and can be accessed directly');
        }

        // If authentication is required, don't proceed with access
        if (requiresSignup || requiresLogin) {
            return {
                success: true,
                component: share.component_id,
                sharedBy: share.shared_by,
                message: share.personal_message,
                accessControls: share.access_controls || [],
                accessType: accessType,
                requiresSignup: requiresSignup,
                requiresLogin: requiresLogin,
                authRequired: true
            };
        }

        // Update access information only if user is authenticated
        const updateData = {
            accessed_at: new Date(),
            $inc: { access_count: 1 }
        };

        // For ANY share type, if user is authenticated and share is not already assigned to a user
        if (userId && !share.shared_with_user) {
            updateData.shared_with_user = userId;
            updateData.shared_with_email = userEmail; // Set email for tracking
            updateData.status = privateShareStatus.ACCEPTED;
            updateData.accepted_at = new Date();

        }

        // If share is already assigned to this user, just update access info
        if (userId && share.shared_with_user && share.shared_with_user.toString() === userId.toString()) {
            updateData.status = privateShareStatus.ACCEPTED;
            if (!share.accepted_at) {
                updateData.accepted_at = new Date();
            }
        }

        await ComponentPrivateShares.updateOne(
            { _id: share._id },
            updateData
        );

        // Send notification to share creator for private invites when first accepted
        if (accessType === 'private_invite' && userId && !share.shared_with_user) {
            try {
                const [sharedBy, sharedWith] = await Promise.all([
                    Users.findById(share.shared_by).select('email first_name').lean(),
                    Users.findById(userId).select('first_name username').lean()
                ]);

                if (sharedBy && sharedWith) {
                    await sendPrivateShareAcceptedNotification(
                        sharedBy.email,
                        sharedWith.first_name || sharedWith.username,
                        share.component_id.title
                    );
                }
            } catch (notificationError) {
                logger.error(`Failed to send share accepted notification: ${notificationError.message}`);
                // Don't fail the main operation if notification fails
            }
        }

        return {
            success: true,
            component: share.component_id,
            sharedBy: share.shared_by,
            message: share.personal_message,
            accessControls: share.access_controls || [],
            accessType: accessType,
            accessToken: accessToken, // Include access token for link tracking
            requiresSignup: false,
            requiresLogin: false,
            authRequired: false
        };

    } catch (error) {
        logger.error(`Error in acceptPrivateShare: ${error.message}`);
        throw error;
    }
}

/**
 * Check if user has access to a private component
 */
async function checkPrivateAccess(componentId, userId, userEmail) {
    try {
        if (!userId && !userEmail) {
            return { hasAccess: false, accessType: null };
        }

        // Check if user owns the component
        if (userId) {
            const isOwner = await Components.exists({
                _id: componentId,
                created_by_user: userId
            });

            if (isOwner) {
                return { hasAccess: true, accessType: accessType.OWNER };
            }
        }

        // Check if component is public
        const component = await Components.findById(componentId).select('component_state is_paid').lean();
        if (!component) {
            return { hasAccess: false, accessType: null };
        }

        if (component.component_state === componentState.PUBLISHED) {
            return { hasAccess: true, accessType: accessType.PUBLIC };
        }

        // Check private share access
        const hasPrivateAccess = await ComponentPrivateShares.hasAccess(componentId, userId, userEmail);
        if (hasPrivateAccess) {
            // If component is paid, check if user has unlocked it
            if (component.is_paid && userId) {
                const unlockHistory = await ComponentUnlockHistory.findOne({
                    component_id: componentId,
                    unlock_by: userId,
                    is_active: true
                }).lean();

                if (unlockHistory) {
                    return { hasAccess: true, accessType: accessType.UNLOCKED, requiresPayment: false };
                } else {
                    return { hasAccess: true, accessType: accessType.PRIVATE_SHARE, requiresPayment: true };
                }
            }
            return { hasAccess: true, accessType: accessType.PRIVATE_SHARE, requiresPayment: false };
        }

        return { hasAccess: false, accessType: null };

    } catch (error) {
        logger.error(`Error in checkPrivateAccess: ${error.message}`);
        return { hasAccess: false, accessType: null };
    }
}

/**
 * Get components shared by a user with pagination
 */
async function getMyPrivateShares(userId, options = {}) {
    try {
        const { status = null, searchText = null, sort_by = null } = options;
        const limit = parseInt(options.limit) || 12;
        const skip = parseInt(options.skip) || 0;

        // Step 1: Build conditions for ComponentPrivateShares to get relevant componentIds
        const shareConditions = {
            shared_by: new mongoose.Types.ObjectId(userId),
            is_active: true,
            status: { $ne: privateShareStatus.DELETED } // Exclude soft-deleted shares
        };

        if (status) {
            shareConditions.status = status;
        }

        // Add search functionality for share-specific fields
        if (searchText) {
            const escapedSearchText = escapeRegex(searchText);
            shareConditions.$or = [
                { personal_message: { $regex: escapedSearchText, $options: 'i' } },
                { link_name: { $regex: escapedSearchText, $options: 'i' } },
                { shared_with_email: { $regex: escapedSearchText, $options: 'i' } }
            ];
        }

        // Step 2: Get componentIds from ComponentPrivateShares (optimized approach)
        const componentIds = (await ComponentPrivateShares.distinct('component_id', shareConditions))
            .map((id) => new mongoose.Types.ObjectId(id));

        logger.info(`🔍 Found ${componentIds.length} components shared by user ${userId}`);

        if (componentIds.length === 0) {
            return {
                recordsTotal: 0,
                recordsFiltered: 0,
                list: []
            };
        }

        // Step 3: Build filters for Components collection
        const componentFilters = {
            '_id': { $in: componentIds }
        };

        // Add component-level search if provided (in addition to share-level search)
        if (searchText && !shareConditions.$or) {
            const searchRegex = new RegExp(escapeRegex(searchText), 'i');
            componentFilters.$or = [
                { 'title': searchRegex },
                { 'short_description': searchRegex }
            ];
        }

        // Step 4: Get counts for pagination
        const recordsFiltered = await ComponentPrivateShares.countDocuments(shareConditions);
        const recordsTotal = await ComponentPrivateShares.countDocuments({
            shared_by: new mongoose.Types.ObjectId(userId),
            is_active: true,
            status: { $ne: privateShareStatus.DELETED } // Exclude soft-deleted shares
        });

        // Step 5: Set up sorting
        let sort = { created_at: -1 };

        // Apply sorting based on sort_by parameter
        if (sort_by) {
            if (sort_by === filterTypes.MOST_POPULAR) {
                sort = { 'share_info.access_count': -1, created_at: -1 };
            } else if (sort_by === filterTypes.RECENT_ADDITIONS) {
                sort = { created_at: -1 };
            } else if (sort_by === filterTypes.MOST_LIKED) {
                sort = { created_at: -1 }; // For shares, we don't have likes, so use created_at
            }
        }

        // Step 6: Build aggregation pipeline on Components collection
        const pipeline = [
            { $match: componentFilters },
            // Lookup private share info for this user's shares
            {
                $lookup: {
                    from: 'component_private_shares',
                    let: { componentId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$component_id', '$$componentId'] },
                                        { $eq: ['$shared_by', new mongoose.Types.ObjectId(userId)] },
                                        { $eq: ['$is_active', true] },
                                        { $ne: ['$status', privateShareStatus.DELETED] }, // Exclude soft-deleted shares
                                        ...(status ? [{ $eq: ['$status', status] }] : [])
                                    ]
                                }
                            }
                        },
                        {
                            $project: {
                                access_token: 1,
                                shared_with_email: 1,
                                status: 1,
                                expires_at: 1,
                                accessed_at: 1,
                                access_count: 1,
                                created_at: 1,
                                personal_message: 1,
                                access_type: 1,
                                link_name: 1,
                                shared_with_user: 1
                            }
                        }
                    ],
                    as: 'share_info'
                }
            },
            // Lookup users who received the shares
            {
                $lookup: {
                    from: 'users',
                    let: { sharedWithUser: { $arrayElemAt: ['$share_info.shared_with_user', 0] } },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$_id', '$$sharedWithUser'] }
                            }
                        },
                        {
                            $project: {
                                first_name: 1,
                                last_name: 1,
                                username: 1,
                                avatar: 1
                            }
                        }
                    ],
                    as: 'shared_with_user'
                }
            },
            // Add computed fields
            {
                $addFields: {
                    share_info: { $arrayElemAt: ['$share_info', 0] },
                    shared_with_user: { $arrayElemAt: ['$shared_with_user', 0] }
                }
            },
            { $sort: sort },
            { $skip: skip },
            { $limit: limit },
            // Final projection to match expected response format
            {
                $project: {
                    // Component fields
                    _id: 1,
                    title: 1,
                    slug: 1,
                    image_url: 1,
                    thumbnail_url: 1,
                    // Share-specific fields
                    access_token: '$share_info.access_token',
                    shared_with_email: '$share_info.shared_with_email',
                    status: '$share_info.status',
                    expires_at: '$share_info.expires_at',
                    accessed_at: '$share_info.accessed_at',
                    access_count: '$share_info.access_count',
                    created_at: '$share_info.created_at',
                    personal_message: '$share_info.personal_message',
                    access_type: '$share_info.access_type',
                    link_name: '$share_info.link_name',
                    shared_with_user: 1
                }
            }
        ];

        logger.info(`getMyPrivateShares optimized pipeline:`, {
            componentFilters: JSON.stringify(componentFilters, null, 2),
            pipelineLength: pipeline.length,
            componentIdsCount: componentIds.length
        });

        // Execute aggregation on Components collection (optimized approach)
        const list = await Components.aggregate(pipeline);

        logger.info(`getMyPrivateShares optimized result:`, {
            recordsTotal,
            recordsFiltered,
            listLength: list.length
        });

        return {
            recordsTotal,
            recordsFiltered,
            list
        };
    } catch (error) {
        logger.error(`Error in getMyPrivateShares: ${error.message}`, {
            error: error.stack,
            userId,
            options
        });
        throw error;
    }
}

/**
 * Get components shared with a user with pagination and Free/Unlocked filtering
 */
async function getComponentsSharedWithMe(userId, userEmail, options = {}) {
    try {
        const { status = null, paymentFilter = null, searchText = null, sort_by = null } = options;
        const limit = parseInt(options.limit) || 12;
        const skip = parseInt(options.skip) || 0;

        // Step 1: Build conditions for ComponentPrivateShares to get relevant componentIds
        const shareConditions = {
            // Must be specifically shared with this user (either by ID or email)
            $and: [
                {
                    $or: [
                        // Shares assigned to this user's ID (accepted shares)
                        {
                            shared_with_user: new mongoose.Types.ObjectId(userId),
                            status: privateShareStatus.ACCEPTED
                        },
                        // Shares assigned to this user's email (pending invites)
                        {
                            shared_with_email: userEmail,
                            shared_with_user: null, // Ensure it's not assigned to another user
                            status: { $in: [privateShareStatus.PENDING, privateShareStatus.ACCEPTED] }
                        }
                    ]
                },
                // Additional security filters
                {
                    is_active: true,
                    $or: [
                        { expires_at: { $gt: new Date() } },
                        { expires_at: null }
                    ]
                }
            ]
        };

        // Add additional status filter if provided (this will further filter the results)
        if (status) {
            // Override the status conditions in the $or clause
            shareConditions.$and[0].$or[0].status = status;
            shareConditions.$and[0].$or[1].status = status;
        }

        // Step 2: Get componentIds from ComponentPrivateShares (similar to getAllLikedComponents pattern)
        const componentIds = (await ComponentPrivateShares.distinct('component_id', shareConditions))
            .map((id) => new mongoose.Types.ObjectId(id));

        logger.info(`🔍 Found ${componentIds.length} shared components for user ${userId}`);

        if (componentIds.length === 0) {
            return {
                recordsTotal: 0,
                recordsFiltered: 0,
                list: []
            };
        }

        // Step 3: Build filters for Components collection (similar to getAllLikedComponents)
        const componentFilters = {
            '_id': { $in: componentIds },
            'component_state': { $in: [componentState.PUBLISHED] }
        };

        // Add search filter if provided
        if (searchText) {
            const searchRegex = new RegExp(escapeRegex(searchText), 'i');
            componentFilters.$or = [
                { 'title': searchRegex },
                { 'short_description': searchRegex }
            ];
        }

        // Step 4: Get total counts for pagination
        const recordsFiltered = await Components.countDocuments(componentFilters);
        const recordsTotal = recordsFiltered; // For shared with me, total = filtered

        logger.info(`📊 Component counts for shared components:`, {
            recordsFiltered,
            recordsTotal,
            componentIds: componentIds.length
        });

        // Step 5: Set up sorting (similar to getAllLikedComponents)
        let sort = { created_at: -1 };

        // Apply sorting based on payment filter
        if (sort_by) {
            if (sort_by === paymentFilterTypes.FREE) {
                sort = { 'is_paid': 1, created_at: -1 };
            } else if (sort_by === paymentFilterTypes.UNLOCKED) {
                sort = { is_unlocked: -1, created_at: -1 };
            } else if (sort_by === paymentFilterTypes.LOCKED) {
                sort = { 'is_paid': -1, is_unlocked: 1, created_at: -1 };
            } else {
                sort = { created_at: -1 };
            }
        }

        // Step 6: Build aggregation pipeline on Components collection (similar to getAllLikedComponents)
        const pipeline = [
            { $match: componentFilters },
            // Lookup private share info for this user
            {
                $lookup: {
                    from: 'component_private_shares',
                    let: { componentId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$component_id', '$$componentId'] },
                                        {
                                            $or: [
                                                { $eq: ['$shared_with_user', new mongoose.Types.ObjectId(userId)] },
                                                { $eq: ['$shared_with_email', userEmail] }
                                            ]
                                        },
                                        { $eq: ['$is_active', true] },
                                        { $in: ['$status', [privateShareStatus.PENDING, privateShareStatus.ACCEPTED]] }
                                    ]
                                }
                            }
                        },
                        {
                            $project: {
                                access_token: 1,
                                status: 1,
                                expires_at: 1,
                                accessed_at: 1,
                                access_count: 1,
                                created_at: 1,
                                personal_message: 1,
                                access_type: 1,
                                shared_with_email: 1,
                                shared_with_user: 1,
                                shared_by: 1
                            }
                        },
                        { $limit: 1 }
                    ],
                    as: 'share_info'
                }
            },
            // Lookup user who shared this component
            {
                $lookup: {
                    from: 'users',
                    let: { sharedBy: { $arrayElemAt: ['$share_info.shared_by', 0] } },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$_id', '$$sharedBy'] }
                            }
                        },
                        {
                            $project: {
                                first_name: 1,
                                last_name: 1,
                                username: 1,
                                avatar: 1
                            }
                        }
                    ],
                    as: 'shared_by_user'
                }
            },
            // Lookup unlock history for this user
            {
                $lookup: {
                    from: 'component_unlock_histories',
                    let: { componentId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$component_id', '$$componentId'] },
                                        { $eq: ['$unlock_by', new mongoose.Types.ObjectId(userId)] },
                                        { $eq: ['$is_active', true] }
                                    ]
                                }
                            }
                        },
                        { $limit: 1 }
                    ],
                    as: 'unlock_history'
                }
            },
            // Add computed fields
            {
                $addFields: {
                    share_info: { $arrayElemAt: ['$share_info', 0] },
                    shared_by_user: { $arrayElemAt: ['$shared_by_user', 0] },
                    is_unlocked: { $gt: [{ $size: '$unlock_history' }, 0] },
                    payment_status: {
                        $switch: {
                            branches: [
                                {
                                    case: { $eq: ['$is_paid', false] },
                                    then: 'free'
                                },
                                {
                                    case: { $gt: [{ $size: '$unlock_history' }, 0] },
                                    then: 'unlocked'
                                }
                            ],
                            default: 'locked'
                        }
                    }
                }
            },
            // Filter by payment status if specified
            ...(paymentFilter && paymentFilter !== paymentFilterTypes.ALL ? [{ $match: { payment_status: paymentFilter } }] : []),
            { $sort: sort },
            { $skip: skip },
            { $limit: limit },
            // Final projection to match expected response format
            {
                $project: {
                    // Component fields
                    _id: 1,
                    title: 1,
                    slug: 1,
                    image_url: 1,
                    thumbnail_url: 1,
                    short_description: 1,
                    views: 1,
                    likes: 1,
                    bookmarks: 1,
                    created_at: 1,
                    is_paid: 1,
                    purchase_price: 1,
                    component_state: 1,
                    component_type: 1,
                    created_by_user: 1,
                    // Share-specific fields
                    access_token: '$share_info.access_token',
                    status: '$share_info.status',
                    expires_at: '$share_info.expires_at',
                    accessed_at: '$share_info.accessed_at',
                    access_count: '$share_info.access_count',
                    personal_message: '$share_info.personal_message',
                    access_type: '$share_info.access_type',
                    shared_with_email: '$share_info.shared_with_email',
                    shared_with_user: '$share_info.shared_with_user',
                    shared_by_user: 1,
                    is_unlocked: 1,
                    payment_status: 1
                }
            }
        ];

        logger.info(`getComponentsSharedWithMe optimized pipeline:`, {
            componentFilters: JSON.stringify(componentFilters, null, 2),
            pipelineLength: pipeline.length,
            componentIdsCount: componentIds.length
        });

        // Execute aggregation on Components collection (optimized approach)
        const list = await Components.aggregate(pipeline);

        logger.info(`getComponentsSharedWithMe optimized result:`, {
            recordsTotal,
            recordsFiltered,
            listLength: list.length,
            firstItem: list[0] || null
        });

        return {
            recordsTotal,
            recordsFiltered,
            list
        };
    } catch (error) {
        logger.error(`Error in getComponentsSharedWithMe: ${error.message}`, {
            error: error.stack,
            userId,
            userEmail,
            options
        });
        throw error;
    }
}

/**
 * Get statistics for components shared with user (Free/Unlocked counts)
 */
async function getSharedWithMeStatistics(userId, userEmail) {
    try {
        // Step 1: Build conditions for ComponentPrivateShares to get relevant componentIds
        const shareConditions = {
            // Must be specifically shared with this user (either by ID or email)
            $and: [
                {
                    $or: [
                        // Shares assigned to this user's ID (accepted shares)
                        { shared_with_user: new mongoose.Types.ObjectId(userId) },
                        // Shares assigned to this user's email (pending invites)
                        {
                            shared_with_email: userEmail,
                            shared_with_user: null // Ensure it's not assigned to another user
                        }
                    ]
                },
                // Additional security filters
                {
                    is_active: true,
                    status: { $in: [privateShareStatus.PENDING, privateShareStatus.ACCEPTED] },
                    $or: [
                        { expires_at: { $gt: new Date() } },
                        { expires_at: null }
                    ]
                }
            ]
        };

        // Step 2: Get componentIds from ComponentPrivateShares (optimized approach)
        const componentIds = (await ComponentPrivateShares.distinct('component_id', shareConditions))
            .map((id) => new mongoose.Types.ObjectId(id));

        logger.info(`🔍 Found ${componentIds.length} shared components for statistics for user ${userId}`);

        if (componentIds.length === 0) {
            return {
                total: 0,
                free: 0,
                unlocked: 0,
                locked: 0
            };
        }

        // Step 3: Build aggregation pipeline on Components collection (optimized approach)
        const pipeline = [
            { $match: { '_id': { $in: componentIds } } },
            // Lookup unlock history for this user
            {
                $lookup: {
                    from: 'component_unlock_histories',
                    let: { componentId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$component_id', '$$componentId'] },
                                        { $eq: ['$unlock_by', new mongoose.Types.ObjectId(userId)] },
                                        { $eq: ['$is_active', true] }
                                    ]
                                }
                            }
                        },
                        { $limit: 1 }
                    ],
                    as: 'unlock_history'
                }
            },
            // Add computed payment status
            {
                $addFields: {
                    payment_status: {
                        $switch: {
                            branches: [
                                {
                                    case: { $eq: ['$is_paid', false] },
                                    then: 'free'
                                },
                                {
                                    case: { $gt: [{ $size: '$unlock_history' }, 0] },
                                    then: 'unlocked'
                                }
                            ],
                            default: 'locked'
                        }
                    }
                }
            },
            // Group by payment status to get counts
            {
                $group: {
                    _id: '$payment_status',
                    count: { $sum: 1 }
                }
            }
        ];

        logger.info(`getSharedWithMeStatistics optimized pipeline:`, {
            pipelineLength: pipeline.length,
            componentIdsCount: componentIds.length
        });

        // Execute aggregation on Components collection (optimized approach)
        const results = await Components.aggregate(pipeline);

        // Format results
        const statistics = {
            total: 0,
            free: 0,
            unlocked: 0,
            locked: 0
        };

        results.forEach(result => {
            statistics[result._id] = result.count;
            statistics.total += result.count;
        });

        logger.info(`getSharedWithMeStatistics optimized result:`, statistics);

        return statistics;

    } catch (error) {
        logger.error(`Error in getSharedWithMeStatistics: ${error.message}`);
        throw error;
    }
}

/**
 * Revoke a private share
 */
async function revokePrivateShare(shareId, userId) {
    try {
        const share = await ComponentPrivateShares.findOne({
            _id: shareId,
            shared_by: userId,
            status: { $in: [privateShareStatus.PENDING, privateShareStatus.ACCEPTED] },
            is_active: true
        });

        if (!share) {
            throw new Error('Share not found or you do not have permission to revoke it');
        }

        await ComponentPrivateShares.updateOne(
            { _id: shareId },
            {
                status: privateShareStatus.REVOKED,
                revoked_at: new Date(),
                revoked_by: userId
            }
        );

        return { success: true, message: 'Share revoked successfully' };

    } catch (error) {
        logger.error(`Error in revokePrivateShare: ${error.message}`);
        throw error;
    }
}

/**
 * Link pending shares when user signs up and notify share creators
 * Handles both email-based invites and link-based shares
 */
async function linkPendingSharesOnSignup(userId, email, accessToken = null) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
        // Build query for pending shares that should be linked to this user
        const query = {
            $and: [
                {
                    $or: [
                        // Email-based invites for this email
                        {
                            shared_with_email: email,
                            shared_with_user: null,
                            access_type: 'by_invite'
                        }
                    ]
                },
                {
                    status: 'pending',
                    is_active: true,
                    $or: [
                        { expires_at: { $gt: new Date() } },
                        { expires_at: null }
                    ]
                }
            ]
        };

        // If user accessed via a specific link token, include that specific link
        if (accessToken) {
            query.$and[0].$or.push({
                access_token: accessToken,
                access_type: 'by_link',
                shared_with_user: null,
                shared_with_email: null
            });
        }

        // Get and update pending shares in a single atomic operation
        const pendingShares = await ComponentPrivateShares.find(query).session(session);

        if (pendingShares.length === 0) {
            await session.commitTransaction();
            session.endSession();
            return 0;
        }

        const now = new Date();
        const shareIds = pendingShares.map(share => share._id);
        
        // Update all shares in bulk
        await ComponentPrivateShares.updateMany(
            { _id: { $in: shareIds } },
            {
                $set: {
                    shared_with_user: userId,
                    shared_with_email: email, // Set email for link-based shares too
                    status: 'accepted',
                    accepted_at: now,
                    updated_at: now
                }
            }
        ).session(session);

        // Get component and user details for notifications
        const componentIds = [...new Set(pendingShares.map(share => share.component_id))];
        const components = await Components.find({ _id: { $in: componentIds } })
            .select('name created_by_user')
            .lean();

        const componentMap = components.reduce((acc, comp) => {
            acc[comp._id.toString()] = comp;
            return acc;
        }, {});

        // Send notifications to share creators
        const notificationPromises = pendingShares.map(async (share) => {
            const component = componentMap[share.component_id.toString()];
            if (!component) return;

            const [sharedBy, sharedWith] = await Promise.all([
                Users.findById(share.shared_by).select('email first_name').lean(),
                Users.findById(userId).select('email first_name username').lean()
            ]);

            if (sharedBy && sharedWith) {
                try {
                    await sendPrivateShareAcceptedNotification(
                        sharedBy.email, // to
                        sharedWith.first_name || sharedWith.username, // recipientName
                        component.name // componentTitle
                    );
                } catch (error) {
                    logger.error(`Failed to send share accepted notification for share ${share._id}: ${error.message}`);
                    // Don't fail the whole operation if notification fails
                }
            }
        });

        await Promise.all(notificationPromises);
        await session.commitTransaction();
        
        logger.info(`Successfully linked ${pendingShares.length} pending shares for user ${userId} (${email})`);
        return pendingShares.length;
        
    } catch (error) {
        await session.abortTransaction();
        logger.error(`Error linking pending shares for user ${userId} (${email}): ${error.message}`, {
            error: error.stack,
            userId,
            email
        });
        throw error; // Re-throw to be handled by the caller
    } finally {
        session.endSession();
    }
}

/**
 * Cleanup expired shares (to be called by cron job)
 */
async function cleanupExpiredShares() {
    try {
        const expiredCount = await ComponentPrivateShares.cleanupExpiredShares();
        logger.info(`Marked ${expiredCount} shares as expired`);
        return expiredCount;
    } catch (error) {
        logger.error(`Error cleaning up expired shares: ${error.message}`);
        return 0;
    }
}

/**
 * Get user's generated shareable links
 */
async function getMyShareableLinks(userId, options = {}) {
    try {
        const { searchText = null, sort_by = null } = options;
        const limit = parseInt(options.limit) || 12;
        const skip = parseInt(options.skip) || 0;

        // Step 1: Build conditions for ComponentPrivateShares to get relevant componentIds
        const shareConditions = {
            shared_by: new mongoose.Types.ObjectId(userId),
            access_type: shareType.BY_LINK,
            is_active: true,
            status: { $ne: privateShareStatus.DELETED } // Exclude soft-deleted shares
        };

        // Add search functionality for share-specific fields
        if (searchText) {
            const escapedSearchText = escapeRegex(searchText);
            shareConditions.$or = [
                { link_name: { $regex: escapedSearchText, $options: 'i' } }
            ];
        }

        // Step 2: Get componentIds from ComponentPrivateShares (optimized approach)
        const componentIds = (await ComponentPrivateShares.distinct('component_id', shareConditions))
            .map((id) => new mongoose.Types.ObjectId(id));

        logger.info(`🔍 Found ${componentIds.length} shareable links for user ${userId}`);

        if (componentIds.length === 0) {
            return {
                recordsTotal: 0,
                recordsFiltered: 0,
                list: []
            };
        }

        // Step 3: Build filters for Components collection
        const componentFilters = {
            '_id': { $in: componentIds }
        };

        // Add component-level search if provided (in addition to share-level search)
        if (searchText && !shareConditions.$or) {
            const searchRegex = new RegExp(escapeRegex(searchText), 'i');
            componentFilters.$or = [
                { 'title': searchRegex },
                { 'short_description': searchRegex }
            ];
        }

        // Step 4: Get counts for pagination - optimize by using same conditions
        const recordsFiltered = await ComponentPrivateShares.countDocuments(shareConditions);
        const recordsTotal = await ComponentPrivateShares.countDocuments({
            shared_by: new mongoose.Types.ObjectId(userId),
            access_type: shareType.BY_LINK,
            is_active: true,
            status: { $ne: privateShareStatus.DELETED } // Exclude soft-deleted shares
        });

        // Step 5: Set up sorting
        let sort = { created_at: -1 };

        // Apply sorting based on sort_by parameter
        if (sort_by) {
            if (sort_by === filterTypes.MOST_POPULAR) {
                sort = { 'share_info.access_count': -1, created_at: -1 };
            } else if (sort_by === filterTypes.RECENT_ADDITIONS) {
                sort = { created_at: -1 };
            } else if (sort_by === filterTypes.MOST_LIKED) {
                sort = { created_at: -1 }; // For links, we don't have likes, so use created_at
            }
        }

        // Step 6: Build aggregation pipeline on Components collection
        const pipeline = [
            { $match: componentFilters },
            // Lookup shareable link info for this user's links
            {
                $lookup: {
                    from: 'component_private_shares',
                    let: { componentId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$component_id', '$$componentId'] },
                                        { $eq: ['$shared_by', new mongoose.Types.ObjectId(userId)] },
                                        { $eq: ['$access_type', shareType.BY_LINK] },
                                        { $eq: ['$is_active', true] },
                                        { $ne: ['$status', privateShareStatus.DELETED] } // Exclude soft-deleted shares
                                    ]
                                }
                            }
                        },
                        {
                            $project: {
                                link_name: 1,
                                access_token: 1,
                                status: 1,
                                expires_at: 1,
                                access_duration: 1,
                                duration_days: 1,
                                access_controls: 1,
                                access_count: 1,
                                created_at: 1
                            }
                        }
                    ],
                    as: 'share_info'
                }
            },
            // Add computed fields
            {
                $addFields: {
                    share_info: { $arrayElemAt: ['$share_info', 0] }
                }
            },
            { $sort: sort },
            { $skip: skip },
            { $limit: limit },
            // Final projection to match expected response format
            {
                $project: {
                    // Component fields
                    _id: 1,
                    title: 1,
                    slug: 1,
                    image_url: 1,
                    thumbnail_url: 1,
                    // Share-specific fields
                    link_name: '$share_info.link_name',
                    access_token: '$share_info.access_token',
                    status: '$share_info.status',
                    expires_at: '$share_info.expires_at',
                    access_duration: '$share_info.access_duration',
                    duration_days: '$share_info.duration_days',
                    access_controls: '$share_info.access_controls',
                    access_count: '$share_info.access_count',
                    created_at: '$share_info.created_at',
                    shareableUrl: { $concat: [`${process.env.SITE_URL}private-share/`, '$share_info.access_token'] }
                }
            }
        ];

        logger.info(`getMyShareableLinks optimized pipeline:`, {
            componentFilters: JSON.stringify(componentFilters, null, 2),
            pipelineLength: pipeline.length,
            componentIdsCount: componentIds.length
        });

        // Execute aggregation on Components collection (optimized approach)
        const list = await Components.aggregate(pipeline);

        logger.info(`getMyShareableLinks optimized result:`, {
            recordsTotal,
            recordsFiltered,
            listLength: list.length
        });

        return {
            recordsTotal,
            recordsFiltered,
            list
        };
    } catch (error) {
        logger.error(`Error in getMyShareableLinks: ${error.message}`);
        throw error;
    }
}

/**
 * Get share statistics for a user
 */
async function getShareStatistics(userId) {
    try {
        const pipeline = [
            {
                $match: {
                    shared_by: new mongoose.Types.ObjectId(userId),
                    is_active: true
                }
            },
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ];

        const stats = await ComponentPrivateShares.aggregate(pipeline);

        const result = {
            total: 0,
            pending: 0,
            accepted: 0,
            expired: 0,
            revoked: 0
        };

        stats.forEach(stat => {
            result[stat._id] = stat.count;
            result.total += stat.count;
        });

        return result;
    } catch (error) {
        logger.error(`Error getting share statistics: ${error.message}`);
        throw error;
    }
}



/**
 * Bulk revoke multiple shares
 */
async function bulkRevokeShares(shareIds, userId) {
    try {
        if (!Array.isArray(shareIds) || shareIds.length === 0) {
            throw new Error('Share IDs array is required');
        }

        if (shareIds.length > 50) {
            throw new Error('Maximum 50 shares can be revoked at once');
        }

        const result = await ComponentPrivateShares.updateMany(
            {
                _id: { $in: shareIds },
                shared_by: userId,
                status: { $in: ['pending', 'accepted'] },
                is_active: true
            },
            {
                status: 'revoked',
                is_active: false,
                revoked_at: new Date(),
                revoked_by: userId
            }
        );

        return {
            success: true,
            revokedCount: result.modifiedCount,
            message: `${result.modifiedCount} share(s) revoked successfully`
        };

    } catch (error) {
        logger.error(`Error in bulk revoke shares: ${error.message}`);
        throw error;
    }
}

/**
 * Get detailed analytics for a specific share (optimized)
 */
async function getShareAnalytics(shareId, userId) {
    try {
        const pipeline = [
            {
                $match: {
                    _id: new mongoose.Types.ObjectId(shareId),
                    shared_by: new mongoose.Types.ObjectId(userId)
                }
            },
            {
                $lookup: {
                    from: 'components',
                    localField: 'component_id',
                    foreignField: '_id',
                    as: 'component',
                    pipeline: [
                        {
                            $project: {
                                title: 1,
                                slug: 1,
                                component_state: 1
                            }
                        }
                    ]
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'shared_with_user',
                    foreignField: '_id',
                    as: 'shared_with_user',
                    pipeline: [
                        {
                            $project: {
                                username: 1,
                                first_name: 1,
                                last_name: 1,
                                email: 1
                            }
                        }
                    ]
                }
            },
            {
                $set: {
                    component: { $arrayElemAt: ['$component', 0] },
                    shared_with_user: { $arrayElemAt: ['$shared_with_user', 0] },
                    daysSinceCreated: {
                        $divide: [
                            { $subtract: [new Date(), '$created_at'] },
                            86400000 // milliseconds in a day
                        ]
                    },
                    isExpired: {
                        $cond: {
                            if: '$expires_at',
                            then: { $gt: [new Date(), '$expires_at'] },
                            else: false
                        }
                    },
                    daysUntilExpiry: {
                        $cond: {
                            if: '$expires_at',
                            then: {
                                $divide: [
                                    { $subtract: ['$expires_at', new Date()] },
                                    86400000
                                ]
                            },
                            else: null
                        }
                    },
                    lastAccessedDaysAgo: {
                        $cond: {
                            if: '$accessed_at',
                            then: {
                                $divide: [
                                    { $subtract: [new Date(), '$accessed_at'] },
                                    86400000
                                ]
                            },
                            else: null
                        }
                    }
                }
            },
            {
                $set: {
                    accessRate: {
                        $cond: {
                            if: { $gt: ['$access_count', 0] },
                            then: {
                                $round: [
                                    { $divide: ['$access_count', { $max: ['$daysSinceCreated', 1] }] },
                                    2
                                ]
                            },
                            else: 0
                        }
                    }
                }
            }
        ];

        const results = await ComponentPrivateShares.aggregate(pipeline);
        const share = results[0];

        if (!share) {
            throw new Error('Share not found or you do not have permission to view it');
        }

        return share;

    } catch (error) {
        logger.error(`Error getting share analytics: ${error.message}`);
        throw error;
    }
}

/**
 * Smart delete for shareable links - handles soft/hard delete based on status
 *
 * Delete Logic:
 * - ACCEPTED: Soft delete (mark as deleted, keep record for access history)
 * - PENDING: Hard delete (remove from database completely)
 * - REVOKED: Hard delete (already inactive, safe to remove)
 * - EXPIRED: Hard delete (already inactive, safe to remove)
 */
async function deleteShareLink(shareId, userId) {
    try {
        logger.info(`🗑️ Smart deleteShareLink called:`, { shareId, userId });

        // Find the share and verify ownership
        const share = await ComponentPrivateShares.findOne({
            _id: shareId,
            shared_by: userId,
            access_type: shareType.BY_LINK
        });

        if (!share) {
            throw new Error('Shareable link not found or you do not have permission to delete it');
        }

        logger.info(`📊 Share status analysis:`, {
            shareId,
            status: share.status,
            hasBeenAccessed: share.access_count > 0,
            accessCount: share.access_count,
            sharedWithUser: !!share.shared_with_user,
            isActive: share.is_active
        });

        let deleteAction = '';
        let result = {};

        // Smart delete logic based on status and usage
        switch (share.status) {
            case privateShareStatus.ACCEPTED:
                // SOFT DELETE: Keep record for access history, but disable link
                await ComponentPrivateShares.findByIdAndUpdate(shareId, {
                    is_active: false,
                    status: privateShareStatus.DELETED,
                    deleted_at: new Date(),
                    deleted_by: userId,
                    // Keep all other fields for access history
                    updated_at: new Date()
                });
                deleteAction = 'soft_delete';
                result = {
                    success: true,
                    action: 'soft_delete',
                    message: 'Shareable link disabled successfully. Access history preserved.',
                    details: 'Link has been used and access history is preserved for security audit.'
                };
                break;

            case privateShareStatus.PENDING:
                // HARD DELETE: Safe to remove completely as no one has accessed it
                await ComponentPrivateShares.findByIdAndDelete(shareId);
                deleteAction = 'hard_delete';
                result = {
                    success: true,
                    action: 'hard_delete',
                    message: 'Shareable link deleted permanently.',
                    details: 'Link was never accessed, safe to remove completely.'
                };
                break;

            case privateShareStatus.REVOKED:
            case privateShareStatus.EXPIRED:
                // HARD DELETE: Already inactive, safe to remove for cleanup
                await ComponentPrivateShares.findByIdAndDelete(shareId);
                deleteAction = 'hard_delete';
                result = {
                    success: true,
                    action: 'hard_delete',
                    message: 'Inactive shareable link removed permanently.',
                    details: `Link was already ${share.status}, removed for cleanup.`
                };
                break;

            default:
                // FALLBACK: Soft delete for unknown statuses
                await ComponentPrivateShares.findByIdAndUpdate(shareId, {
                    is_active: false,
                    status: privateShareStatus.DELETED,
                    deleted_at: new Date(),
                    deleted_by: userId,
                    updated_at: new Date()
                });
                deleteAction = 'soft_delete_fallback';
                result = {
                    success: true,
                    action: 'soft_delete',
                    message: 'Shareable link disabled successfully.',
                    details: 'Unknown status, used soft delete for safety.'
                };
                break;
        }

        logger.info(`✅ Smart delete completed:`, {
            shareId,
            userId,
            originalStatus: share.status,
            deleteAction,
            accessCount: share.access_count
        });

        return result;

    } catch (error) {
        logger.error(`❌ Error in smart deleteShareLink: ${error.message}`, {
            error: error.stack,
            shareId,
            userId
        });
        throw error;
    }
}

/**
 * Refresh shareable link (generate new token)
 */
async function refreshShareableLink(shareId, userId) {
    try {
        logger.info(`🔄 refreshShareableLinkNew called:`, { shareId, userId });

        // Find the share and verify ownership
        const share = await ComponentPrivateShares.findOne({
            _id: shareId,
            shared_by: userId,
            access_type: 'by_link'
        });

        if (!share) {
            throw new Error('Shareable link not found or you do not have permission to refresh it');
        }

        // Generate new access token
        const newAccessToken = crypto.randomBytes(32).toString('hex');

        // Update the share with new token and reset status
        const updatedShare = await ComponentPrivateShares.findByIdAndUpdate(
            shareId,
            {
                access_token: newAccessToken,
                status: privateShareStatus.PENDING, // Reset to pending
                shared_with_user: null, // Reset user assignment
                shared_with_email: null, // Reset email assignment
                access_count: 0, // Reset access count
                accessed_at: null, // Reset access time
                accepted_at: null, // Reset acceptance time
                updated_at: new Date()
            },
            { new: true }
        ).populate('component_id', 'title slug');

        const shareableUrl = `${process.env.SITE_URL}private-share/${newAccessToken}`;

        logger.info(`✅ Share link refreshed successfully:`, {
            shareId,
            userId,
            newToken: newAccessToken.substring(0, 8) + '...'
        });

        return {
            success: true,
            share: updatedShare,
            shareableUrl: shareableUrl,
            message: 'Shareable link refreshed successfully'
        };

    } catch (error) {
        logger.error(`❌ Error in refreshShareableLinkNew: ${error.message}`, {
            error: error.stack,
            shareId,
            userId
        });
        throw error;
    }
}

module.exports = {
    // Core sharing functions
    shareComponentPrivately,
    generatePublicShareableLink,
    acceptPrivateShare,

    // Access control
    checkPrivateAccess,

    // Data retrieval functions
    getMyPrivateShares,
    getMyShareableLinks,
    getComponentsSharedWithMe,
    getSharedWithMeStatistics,

    // Management functions
    revokePrivateShare,
    bulkRevokeShares,
    refreshShareableLink,
    deleteShareLink,

    // Statistics and analytics
    getShareStatistics,
    getShareAnalytics,

    // Utility functions
    linkPendingSharesOnSignup,
    cleanupExpiredShares,
    generateAccessToken
};