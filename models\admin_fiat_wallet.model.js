const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const AdminFiatWalletSchema = new Schema({
    amount: {
        type: Number,
        required: true,
        default: 0
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const AdminFiatWallet = mongoose.model('admin_fiat_wallet', AdminFiatWalletSchema);

module.exports = {
    AdminFiatWallet
};  