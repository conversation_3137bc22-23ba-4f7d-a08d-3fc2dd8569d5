const Joi = require('joi');

const shareComponentPrivatelyValidation = (data) => {
    const schema = Joi.object({
        emails: Joi.array()
            .items(Joi.string().email().required())
            .min(1)
            .max(10)
            .required()
            .messages({
                'array.min': 'At least one email is required',
                'array.max': 'Maximum 10 emails allowed per share',
                'string.email': 'Please provide valid email addresses'
            }),
        personal_message: Joi.string()
            .max(500)
            .optional()
            .allow('')
            .messages({
                'string.max': 'Personal message cannot exceed 500 characters'
            }),
        access_duration: Joi.string()
            .valid('undefined', 'days')
            .default('undefined')
            .optional(),
        duration_days: Joi.number()
            .integer()
            .min(1)
            .max(365)
            .optional()
            .when('access_duration', {
                is: 'days',
                then: Joi.required(),
                otherwise: Joi.forbidden()
            })
            .messages({
                'number.min': 'Duration must be at least 1 day',
                'number.max': 'Duration cannot exceed 365 days'
            }),
        access_controls: Joi.array()
            .items(Joi.string().valid('fork', 'download', 'copy'))
            .optional()
            .default([])
    });

    return schema.validate(data);
};

const generateShareableLinkValidation = (data) => {
    const schema = Joi.object({
        link_name: Joi.string()
            .max(100)
            .required()
            .messages({
                'string.max': 'Link name cannot exceed 100 characters'
            }),
        access_duration: Joi.string()
            .valid('undefined', 'days')
            .default('undefined')
            .optional(),
        duration_days: Joi.number()
            .integer()
            .min(1)
            .max(365)
            .optional()
            .when('access_duration', {
                is: 'days',
                then: Joi.required(),
                otherwise: Joi.forbidden()
            })
            .messages({
                'number.min': 'Duration must be at least 1 day',
                'number.max': 'Duration cannot exceed 365 days'
            }),
        access_controls: Joi.array()
            .items(Joi.string().valid('fork', 'download', 'copy'))
            .optional()
            .default([])
    });

    return schema.validate(data);
};

const getPrivateSharesValidation = (data) => {
    const schema = Joi.object({
        page: Joi.number()
            .integer()
            .min(1)
            .default(1)
            .optional(),
        limit: Joi.number()
            .integer()
            .min(1)
            .max(50)
            .default(10)
            .optional(),
        status: Joi.string()
            .valid('pending', 'accepted', 'expired', 'revoked')
            .optional(),
        payment_filter: Joi.string()
            .valid('all', 'free', 'unlocked', 'locked')
            .optional()
            .messages({
                'any.only': 'Payment filter must be one of: all, free, unlocked, locked'
            })
    });

    return schema.validate(data);
};

const accessTokenValidation = (data) => {
    const schema = Joi.object({
        token: Joi.string()
            .length(64)
            .hex()
            .required()
            .messages({
                'string.length': 'Invalid access token format',
                'string.hex': 'Invalid access token format'
            })
    });

    return schema.validate(data);
};

const bulkRevokeSharesValidation = (data) => {
    const schema = Joi.object({
        shareIds: Joi.array()
            .items(Joi.string().hex().length(24).required())
            .min(1)
            .max(50)
            .required()
            .messages({
                'array.min': 'At least one share ID is required',
                'array.max': 'Maximum 50 shares can be revoked at once',
                'string.hex': 'Invalid share ID format',
                'string.length': 'Invalid share ID format'
            })
    });

    return schema.validate(data);
};

const shareIdValidation = (data) => {
    const schema = Joi.object({
        shareId: Joi.string()
            .hex()
            .length(24)
            .required()
            .messages({
                'string.hex': 'Invalid share ID format',
                'string.length': 'Invalid share ID format'
            })
    });

    return schema.validate(data);
};

module.exports = {
    shareComponentPrivatelyValidation,
    getPrivateSharesValidation,
    accessTokenValidation,
    generateShareableLinkValidation,
    bulkRevokeSharesValidation,
    shareIdValidation
};