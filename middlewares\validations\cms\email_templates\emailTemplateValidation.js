const { addEmailTemplate } = require('../../../../validations/cms/email_templates/emailTemplateValidation');

class EmailTemplateValidationMiddleware {
    addEmailTemplateValidation(req, res, next) {
        const { value, error } = addEmailTemplate(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

}
module.exports = new EmailTemplateValidationMiddleware();