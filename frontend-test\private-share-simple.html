<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Private Share Access - Simple</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="form-header">
                <h1>🔗 Private Share Access</h1>
                <p>Accessing shared component...</p>
            </div>

            <div class="card">
                <div id="loadingStep">
                    <div style="text-align: center; padding: 40px;">
                        <div class="loading" style="width: 40px; height: 40px; margin: 0 auto 20px;"></div>
                        <p>Validating access token...</p>
                    </div>
                </div>

                <div id="errorStep" style="display: none;">
                    <div class="access-type requires-payment">
                        ❌ Access Error
                    </div>
                    <div id="errorMessage"></div>
                    <div class="button-group">
                        <button onclick="window.location.href='index.html'" class="success">🏠 Go Home</button>
                    </div>
                </div>

                <div id="successStep" style="display: none;">
                    <div class="access-type public-link">
                        ✅ Access Granted
                    </div>
                    <div id="successMessage"></div>
                    <div class="button-group">
                        <button onclick="window.location.href='shared-with-me.html'" class="success">📥 Shared with Me</button>
                    </div>
                </div>
            </div>

            <div class="logs">
                <h3>📋 Debug Logs</h3>
                <div id="logContainer"></div>
            </div>
        </div>
    </div>

    <script>
        // Inline JavaScript to avoid external file loading issues
        let API_BASE_URL = 'http://localhost:7898/api/front'; // Update this to your backend URL
        let accessToken = null; // Global variable to store the access token
        
        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type}] ${message}`);
        }

        function showError(message) {
            document.getElementById('loadingStep').style.display = 'none';
            document.getElementById('errorMessage').innerHTML = `<p>${message}</p>`;
            document.getElementById('errorStep').style.display = 'block';
        }

        function showSuccess(message) {
            document.getElementById('loadingStep').style.display = 'none';
            document.getElementById('successMessage').innerHTML = `<p>${message}</p>`;
            document.getElementById('successStep').style.display = 'block';
        }

        async function validateAccess() {
            // First, try to get token from URL path
            const pathParts = window.location.pathname.split('/');
            const tokenIndex = pathParts.lastIndexOf('private-share') + 1;
            accessToken = pathParts[tokenIndex] || '';

            // If no token in path, try to get from query params
            if (!accessToken) {
                const urlParams = new URLSearchParams(window.location.search);
                accessToken = urlParams.get('token') || '';
            }

            // If still no token, show error
            if (!accessToken) {
                showError('No access token found in URL');
                return;
            }
            
            // Check if we're coming back from auth (has token in URL)
            const urlParams = new URLSearchParams(window.location.search);
            const authToken = urlParams.get('token');
            
            if (authToken && !localStorage.getItem('userInfo')) {
                // We're coming back from auth, but don't have user info yet
                try {
                    // Try to fetch user info using the token
                    const userResponse = await fetch(`${API_BASE_URL}/user/me`, {
                        headers: {
                            'Authorization': `Bearer ${authToken}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    if (userResponse.ok) {
                        const userData = await userResponse.json();
                        localStorage.setItem('authToken', authToken);
                        localStorage.setItem('userInfo', JSON.stringify(userData.data));
                        // Remove token from URL to prevent infinite loops
                        const cleanUrl = window.location.pathname;
                        window.history.replaceState({}, document.title, cleanUrl);
                        // Force reload to refresh auth state
                        window.location.reload();
                    } else {
                        // If we can't get user info, clear auth and redirect to login
                        localStorage.removeItem('authToken');
                        showAuthRequired('login');
                    }
                    return;
                } catch (error) {
                    console.error('Error fetching user info:', error);
                    localStorage.removeItem('authToken');
                    showAuthRequired('login');
                    return;
                }
            }
            
            // Clear any previous auth attempts when starting validation
            sessionStorage.removeItem('authAttempts');
            
            // Check if we're already on an auth page
            const isOnAuthPage = window.location.pathname.includes('login.html') || 
                               window.location.pathname.includes('signup.html');

            logMessage(`Extracted token: ${accessToken}`, 'info');
            logMessage(`API Base URL: ${API_BASE_URL}`, 'info');

            try {
                // Prepare headers with authentication if available
                const headers = {
                    'Content-Type': 'application/json'
                };

                const authToken = localStorage.getItem('authToken');
                if (authToken) {
                    headers['Authorization'] = `Bearer ${authToken}`;
                    // Also set cookie for session-based auth
                    document.cookie = `authToken=${authToken}; path=/; SameSite=Lax; Secure`;
                    // Add cache control headers
                    headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
                    headers['Pragma'] = 'no-cache';
                    headers['Expires'] = '0';
                }

                logMessage(`Making API call with headers: ${JSON.stringify(headers, null, 2)}`, 'info');

                const response = await fetch(`${API_BASE_URL}/component/private-share/${accessToken}/validate`, {
                    method: 'GET',
                    headers: headers,
                    credentials: 'include' // Include cookies for session auth
                });

                logMessage(`Response status: ${response.status}`, 'info');
                logMessage(`Response headers: ${JSON.stringify([...response.headers.entries()], null, 2)}`, 'info');

                // Check if response is JSON
                const contentType = response.headers.get('content-type');
                let data;

                if (contentType && contentType.includes('application/json')) {
                    data = await response.json();
                } else {
                    const text = await response.text();
                    logMessage(`Non-JSON response: ${text}`, 'error');
                    data = {
                        status: response.status,
                        message: text || 'Non-JSON response received',
                        error: true
                    };
                }

                logMessage(`Parsed response: ${JSON.stringify(data, null, 2)}`, 'info');

                if (data.status === 200) {
                    // If we have a valid session but still getting auth requirements
                    const hasValidSession = isUserAuthenticated();
                    
                    if ((data.data.requiresLogin || data.data.authRequired) && hasValidSession) {
                        logMessage('Backend requires login, but we have a valid session. Checking access...', 'warning');
                        
                        // Try to force a refresh of the token and user info
                        const authToken = localStorage.getItem('authToken');
                        if (authToken) {
                            try {
                                // Try to refresh the user info
                                const userResponse = await fetch(`${API_BASE_URL}/user/me`, {
                                    headers: {
                                        'Authorization': `Bearer ${authToken}`,
                                        'Content-Type': 'application/json'
                                    }
                                });
                                
                                if (userResponse.ok) {
                                    const userData = await userResponse.json();
                                    localStorage.setItem('userInfo', JSON.stringify(userData.data));
                                    logMessage('User info refreshed, reloading...', 'info');
                                    window.location.reload();
                                    return;
                                }
                            } catch (error) {
                                console.error('Error refreshing user info:', error);
                            }
                        }
                        
                        // If we get here, something is wrong with the session
                        logMessage('Session validation failed, clearing and redirecting to login...', 'warning');
                        localStorage.removeItem('authToken');
                        localStorage.removeItem('userInfo');
                        showAuthRequired('login');
                        return;
                    }
                    
                    // Handle authentication requirements
                    if (data.data.requiresSignup) {
                        logMessage('Signup required', 'info');
                        if (!isOnAuthPage) {
                            showAuthRequired('signup');
                        }
                        return;
                    } else if (data.data.requiresLogin) {
                        logMessage('Login required', 'info');
                        if (!isOnAuthPage) {
                            showAuthRequired('login');
                        }
                        return;
                    } else if (data.data.authRequired) {
                        logMessage('Authentication required', 'info');
                        const authType = data.data.requiresSignup ? 'signup' : 'login';
                        if (!isOnAuthPage) {
                            showAuthRequired(authType);
                        }
                        return;
                    } else {
                        // Success - access granted, clear auth attempts counter
                        localStorage.removeItem('authAttempts');
                        showSuccess(`
                            <h3>🎉 Success!</h3>
                            <p>You have successfully accessed this shared component!</p>
                            <p><strong>Component:</strong> ${data.data.component?.title || 'Unknown'}</p>
                            <p><strong>Access Type:</strong> ${data.data.accessType || 'Unknown'}</p>
                            <p>Check out more shared components in your "Shared with Me" section.</p>
                        `);
                    }
                } else if (data.status === 401) {
                    logMessage('Unauthorized - authentication required', 'warning');
                    showAuthRequired('signup');
                } else {
                    showError(data.message || 'Failed to validate access token');
                }
            } catch (error) {
                logMessage(`Network error: ${error.message}`, 'error');
                logMessage(`Error stack: ${error.stack}`, 'error');
                showError(`Network error: ${error.message}. Please check if the backend server is running.`);
            }
        }

        async function showAuthRequired(type = 'signup') {
            document.getElementById('loadingStep').style.display = 'none';
            document.getElementById('successMessage').style.display = 'block';

            // Create a clean return URL without any query params to prevent loops
            const currentUrl = new URL(window.location.href);
            const cleanUrl = `${currentUrl.origin}${currentUrl.pathname}`;
            const returnPath = encodeURIComponent(cleanUrl);
            
            // If we're already on an auth page, don't redirect
            const isOnAuthPage = window.location.pathname.includes('login.html') || 
                               window.location.pathname.includes('signup.html');
            
            if (isOnAuthPage) {
                return; // Already on the correct page, let the form handle submission
            }

            // Check if we're in a redirect loop
            const authAttempts = parseInt(sessionStorage.getItem('authAttempts') || '0');
            
            if (authAttempts > 2) {
                logMessage('Possible redirect loop detected, clearing auth and reloading', 'warning');
                // Clear everything and reload
                localStorage.clear();
                sessionStorage.clear();
                window.location.href = window.location.pathname; // Reload without query params
                return;
            }

            // Increment auth attempts counter in session storage
            sessionStorage.setItem('authAttempts', (authAttempts + 1).toString());
            
            // Clear any existing auth data
            localStorage.removeItem('authToken');
            localStorage.removeItem('userInfo');
            
            logMessage(`Redirecting to ${type} page (attempt ${authAttempts + 1})`, 'info');
            
            // Add a small delay before redirecting to prevent race conditions
            await new Promise(resolve => setTimeout(resolve, 100));
            
            // Set up the message and button text based on auth type
            const authMessage = type === 'login' ? `
                <h3>🔐 Login Required</h3>
                <p>You need to login to access this shared component.</p>
                <p>You will be redirected to the login page in <span id="countdown">5</span> seconds...</p>
            ` : `
                <h3>📝 Signup Required</h3>
                <p>You need to create an account to access this shared component.</p>
                <p>You will be redirected to the signup page in <span id="countdown">5</span> seconds...</p>
            `;
            
            document.getElementById('successMessage').innerHTML = `
                ${authMessage}
                <div class="button-group" style="margin-top: 20px;">
                    <button onclick="window.location.href='${type}.html?returnUrl=${returnPath}'" class="success">
                        ${type === 'login' ? '🔑 Login Now' : '📝 Sign Up Now'}
                    </button>
                    <button onclick="window.location.href='index.html'" class="secondary">🏠 Go Home</button>
                </div>
            `;
            document.getElementById('successStep').style.display = 'block';

            // Start countdown
            let countdown = 5;
            const countdownElement = document.getElementById('countdown');
            const countdownInterval = setInterval(() => {
                countdown--;
                if (countdownElement) {
                    countdownElement.textContent = countdown;
                }
                if (countdown <= 0) {
                    clearInterval(countdownInterval);
                    logMessage(`Auto-redirecting to ${type} page...`, 'info');
                    window.location.href = redirectUrl;
                }
            }, 1000);
        }

        // Check if user is already authenticated
        function isUserAuthenticated() {
            const authToken = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');
            
            // If we have a token but no user info, we're not fully authenticated
            if (authToken && !userInfo) {
                return false;
            }
            
            const isAuthenticated = !!(authToken && userInfo);

            logMessage(`Auth check - Token: ${authToken ? 'Present' : 'Missing'}, UserInfo: ${userInfo ? 'Present' : 'Missing'}, Authenticated: ${isAuthenticated}`, 'info');

            return isAuthenticated;
        }

        // Test API connection first
        async function testAPIConnection() {
            logMessage('Testing API connection...', 'info');

            try {
                const response = await fetch(`${API_BASE_URL}/health/health-check`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                logMessage(`API health check status: ${response.status}`, 'info');

                if (response.ok) {
                    logMessage('✅ API connection successful', 'success');
                    return true;
                } else {
                    logMessage('❌ API health check failed', 'error');
                    return false;
                }
            } catch (error) {
                logMessage(`❌ API connection failed: ${error.message}`, 'error');
                return false;
            }
        }

        // Start validation when page loads
        document.addEventListener('DOMContentLoaded', async function() {
            logMessage('Page loaded, starting validation...', 'info');
            logMessage(`API Base URL: ${API_BASE_URL}`, 'info');

            // Check if user is already authenticated
            const isAuthenticated = isUserAuthenticated();
            logMessage(`User authentication status: ${isAuthenticated ? 'Authenticated' : 'Not authenticated'}`, 'info');

            // Check if this is a return from login/signup
            const urlParams = new URLSearchParams(window.location.search);
            const fromAuth = urlParams.get('fromAuth');
            if (fromAuth) {
                logMessage('Returned from authentication, clearing URL params...', 'info');
                // Clean the URL to remove auth-related params
                const cleanUrl = window.location.pathname;
                window.history.replaceState({}, document.title, cleanUrl);
            }

            // Test API connection first
            const apiConnected = await testAPIConnection();

            if (!apiConnected) {
                showError(`
                    <h3>❌ Backend Connection Failed</h3>
                    <p>Cannot connect to the backend server at:</p>
                    <p><code>${API_BASE_URL}</code></p>
                    <p>Please ensure:</p>
                    <ul style="text-align: left; margin: 10px 0;">
                        <li>Backend server is running</li>
                        <li>CORS is properly configured</li>
                        <li>API URL is correct</li>
                    </ul>
                    <div class="button-group" style="margin-top: 20px;">
                        <button onclick="location.reload()" class="success">🔄 Retry</button>
                        <button onclick="window.location.href='index.html'" class="secondary">🏠 Go Home</button>
                    </div>
                `);
                return;
            }

            // Proceed with validation if API is connected
            validateAccess();
        });
    </script>
</body>
</html>
