<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Private Share Access - Simple</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="form-header">
                <h1>🔗 Private Share Access</h1>
                <p>Accessing shared component...</p>
            </div>

            <div class="card">
                <div id="loadingStep">
                    <div style="text-align: center; padding: 40px;">
                        <div class="loading" style="width: 40px; height: 40px; margin: 0 auto 20px;"></div>
                        <p>Validating access token...</p>
                    </div>
                </div>

                <div id="errorStep" style="display: none;">
                    <div class="access-type requires-payment">
                        ❌ Access Error
                    </div>
                    <div id="errorMessage"></div>
                    <div class="button-group">
                        <button onclick="window.location.href='index.html'" class="success">🏠 Go Home</button>
                    </div>
                </div>

                <div id="successStep" style="display: none;">
                    <div class="access-type public-link">
                        ✅ Access Granted
                    </div>
                    <div id="successMessage"></div>
                    <div class="button-group">
                        <button onclick="window.location.href='shared-with-me.html'" class="success">📥 Shared with Me</button>
                    </div>
                </div>
            </div>

            <div class="logs">
                <h3>📋 Debug Logs</h3>
                <div id="logContainer"></div>
            </div>
        </div>
    </div>

    <script>
        // Inline JavaScript to avoid external file loading issues
        let API_BASE_URL = 'http://localhost:7898/api/front'; // Update this to your backend URL
        
        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type}] ${message}`);
        }

        function showError(message) {
            document.getElementById('loadingStep').style.display = 'none';
            document.getElementById('errorMessage').innerHTML = `<p>${message}</p>`;
            document.getElementById('errorStep').style.display = 'block';
        }

        function showSuccess(message) {
            document.getElementById('loadingStep').style.display = 'none';
            document.getElementById('successMessage').innerHTML = `<p>${message}</p>`;
            document.getElementById('successStep').style.display = 'block';
        }

        async function validateAccess() {
            // Extract token from URL
            const pathParts = window.location.pathname.split('/');
            const tokenIndex = pathParts.indexOf('private-share') + 1;
            let accessToken = null;
            
            if (tokenIndex > 0 && pathParts[tokenIndex]) {
                accessToken = pathParts[tokenIndex];
            } else {
                const params = new URLSearchParams(window.location.search);
                accessToken = params.get('token');
            }

            if (!accessToken) {
                showError('No access token found in URL');
                return;
            }

            logMessage(`Extracted token: ${accessToken}`, 'info');
            logMessage(`API Base URL: ${API_BASE_URL}`, 'info');

            try {
                const response = await fetch(`${API_BASE_URL}/component/private-share/${accessToken}/validate`, {
                    method: 'GET',
                    <!-- headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                    } -->
                });

                const data = await response.json();
                logMessage(`Response: ${JSON.stringify(data, null, 2)}`, 'info');

                if (data.status === 200) {
                    showSuccess(`
                        <h3>🎉 Success!</h3>
                        <p>You have successfully accessed this shared component!</p>
                        <p><strong>Component:</strong> ${data.data.component?.title || 'Unknown'}</p>
                        <p><strong>Access Type:</strong> ${data.data.accessType || 'Unknown'}</p>
                        <p>Check out more shared components in your "Shared with Me" section.</p>
                    `);
                } else {
                    showError(data.message || 'Failed to validate access token');
                }
            } catch (error) {
                logMessage(`Error: ${error.message}`, 'error');
                showError(`Network error: ${error.message}`);
            }
        }

        // Start validation when page loads
        document.addEventListener('DOMContentLoaded', function() {
            logMessage('Page loaded, starting validation...', 'info');
            validateAccess();
        });
    </script>
</body>
</html>
