<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Private Share Access - Simple</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="form-header">
                <h1>🔗 Private Share Access</h1>
                <p>Accessing shared component...</p>
            </div>

            <div class="card">
                <div id="loadingStep">
                    <div style="text-align: center; padding: 40px;">
                        <div class="loading" style="width: 40px; height: 40px; margin: 0 auto 20px;"></div>
                        <p>Validating access token...</p>
                    </div>
                </div>

                <div id="errorStep" style="display: none;">
                    <div class="access-type requires-payment">
                        ❌ Access Error
                    </div>
                    <div id="errorMessage"></div>
                    <div class="button-group">
                        <button onclick="window.location.href='index.html'" class="success">🏠 Go Home</button>
                    </div>
                </div>

                <div id="successStep" style="display: none;">
                    <div class="access-type public-link">
                        ✅ Access Granted
                    </div>
                    <div id="successMessage"></div>
                    <div class="button-group">
                        <button onclick="window.location.href='shared-with-me.html'" class="success">📥 Shared with Me</button>
                    </div>
                </div>
            </div>

            <div class="logs">
                <h3>📋 Debug Logs</h3>
                <div id="logContainer"></div>
            </div>
        </div>
    </div>

    <script>
        // Inline JavaScript to avoid external file loading issues
        let API_BASE_URL = 'http://localhost:7898/api/front'; // Update this to your backend URL
        let accessToken = null; // Global variable to store the access token
        
        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type}] ${message}`);
        }

        function showError(message) {
            document.getElementById('loadingStep').style.display = 'none';
            document.getElementById('errorMessage').innerHTML = `<p>${message}</p>`;
            document.getElementById('errorStep').style.display = 'block';
        }

        function showSuccess(message) {
            document.getElementById('loadingStep').style.display = 'none';
            document.getElementById('successMessage').innerHTML = `<p>${message}</p>`;
            document.getElementById('successStep').style.display = 'block';
        }

        async function validateAccess() {
            // First, try to get token from URL path
            const pathParts = window.location.pathname.split('/');
            const tokenIndex = pathParts.indexOf('private-share') + 1;
            accessToken = pathParts[tokenIndex] || '';

            // If no token in path, try to get from query params
            if (!accessToken) {
                const urlParams = new URLSearchParams(window.location.search);
                accessToken = urlParams.get('token') || '';
            }

            // If still no token, show error
            if (!accessToken) {
                showError('No access token found in URL');
                return;
            }

            logMessage(`Extracted token: ${accessToken}`, 'info');
            logMessage(`API Base URL: ${API_BASE_URL}`, 'info');

            try {
                // Prepare headers with authentication if available
                const headers = {
                    'Content-Type': 'application/json'
                };

                const authToken = localStorage.getItem('authToken');
                if (authToken) {
                    headers['Authorization'] = `Bearer ${authToken}`;
                    // Also set cookie for session-based auth
                    document.cookie = `authToken=${authToken}; path=/; SameSite=Lax`;
                }

                logMessage(`Making API call with headers: ${JSON.stringify(headers, null, 2)}`, 'info');

                const response = await fetch(`${API_BASE_URL}/component/private-share/${accessToken}/validate`, {
                    method: 'GET',
                    headers: headers,
                    credentials: 'include' // Include cookies for session auth
                });

                logMessage(`Response status: ${response.status}`, 'info');
                logMessage(`Response headers: ${JSON.stringify([...response.headers.entries()], null, 2)}`, 'info');

                // Check if response is JSON
                const contentType = response.headers.get('content-type');
                let data;

                if (contentType && contentType.includes('application/json')) {
                    data = await response.json();
                } else {
                    const text = await response.text();
                    logMessage(`Non-JSON response: ${text}`, 'error');
                    data = {
                        status: response.status,
                        message: text || 'Non-JSON response received',
                        error: true
                    };
                }

                logMessage(`Parsed response: ${JSON.stringify(data, null, 2)}`, 'info');

                if (data.status === 200) {
                    // Handle authentication requirements
                    if (data.data.requiresSignup) {
                        logMessage('Signup required - redirecting...', 'warning');
                        // Only redirect if not already on the signup page
                        if (!window.location.pathname.includes('signup.html')) {
                            showAuthRequired('signup');
                        }
                    } else if (data.data.requiresLogin) {
                        logMessage('Login required - redirecting...', 'warning');
                        // Only redirect if not already on the login page
                        if (!window.location.pathname.includes('login.html')) {
                            showAuthRequired('login');
                        }
                    } else if (data.data.authRequired) {
                        logMessage('Authentication required - redirecting...', 'warning');
                        const authType = data.data.requiresSignup ? 'signup' : 'login';
                        if (!window.location.pathname.includes(`${authType}.html`)) {
                            showAuthRequired(authType);
                        }
                    } else {
                        // Success - access granted
                        showSuccess(`
                            <h3>🎉 Success!</h3>
                            <p>You have successfully accessed this shared component!</p>
                            <p><strong>Component:</strong> ${data.data.component?.title || 'Unknown'}</p>
                            <p><strong>Access Type:</strong> ${data.data.accessType || 'Unknown'}</p>
                            <p>Check out more shared components in your "Shared with Me" section.</p>
                        `);
                    }
                } else if (data.status === 401) {
                    logMessage('Unauthorized - authentication required', 'warning');
                    showAuthRequired('signup');
                } else {
                    showError(data.message || 'Failed to validate access token');
                }
            } catch (error) {
                logMessage(`Network error: ${error.message}`, 'error');
                logMessage(`Error stack: ${error.stack}`, 'error');
                showError(`Network error: ${error.message}. Please check if the backend server is running.`);
            }
        }

        function showAuthRequired(type = 'signup') {
            document.getElementById('loadingStep').style.display = 'none';

            // Get the current path and remove any existing auth-related query params
            const currentPath = window.location.pathname.split('/').pop();
            const cleanPath = currentPath.split('?')[0];
            const returnUrl = `private-share/${accessToken}`;
            
            let redirectUrl;
            let message;
            let buttonText;

            if (type === 'login') {
                redirectUrl = `login.html?returnUrl=${encodeURIComponent(returnUrl)}`;
                message = `
                    <h3>🔐 Login Required</h3>
                    <p>You need to login to access this shared component.</p>
                    <p>You will be redirected to the login page in <span id="countdown">5</span> seconds...</p>
                `;
                buttonText = '🔑 Login Now';
            } else {
                redirectUrl = `signup.html?returnUrl=${encodeURIComponent(returnUrl)}`;
                message = `
                    <h3>📝 Signup Required</h3>
                    <p>You need to create an account to access this shared component.</p>
                    <p>You will be redirected to the signup page in <span id="countdown">5</span> seconds...</p>
                `;
                buttonText = '📝 Sign Up Now';
            }

            document.getElementById('successMessage').innerHTML = `
                ${message}
                <div class="button-group" style="margin-top: 20px;">
                    <button onclick="window.location.href='${redirectUrl}'" class="success">${buttonText}</button>
                    <button onclick="window.location.href='index.html'" class="secondary">🏠 Go Home</button>
                </div>
            `;
            document.getElementById('successStep').style.display = 'block';

            // Start countdown
            let countdown = 5;
            const countdownElement = document.getElementById('countdown');
            const countdownInterval = setInterval(() => {
                countdown--;
                if (countdownElement) {
                    countdownElement.textContent = countdown;
                }
                if (countdown <= 0) {
                    clearInterval(countdownInterval);
                    logMessage(`Auto-redirecting to ${type} page...`, 'info');
                    window.location.href = redirectUrl;
                }
            }, 1000);
        }

        // Test API connection first
        async function testAPIConnection() {
            logMessage('Testing API connection...', 'info');

            try {
                const response = await fetch(`${API_BASE_URL}/health/health-check`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                logMessage(`API health check status: ${response.status}`, 'info');

                if (response.ok) {
                    logMessage('✅ API connection successful', 'success');
                    return true;
                } else {
                    logMessage('❌ API health check failed', 'error');
                    return false;
                }
            } catch (error) {
                logMessage(`❌ API connection failed: ${error.message}`, 'error');
                return false;
            }
        }

        // Start validation when page loads
        document.addEventListener('DOMContentLoaded', async function() {
            logMessage('Page loaded, starting validation...', 'info');
            logMessage(`API Base URL: ${API_BASE_URL}`, 'info');

            // Test API connection first
            const apiConnected = await testAPIConnection();

            if (!apiConnected) {
                showError(`
                    <h3>❌ Backend Connection Failed</h3>
                    <p>Cannot connect to the backend server at:</p>
                    <p><code>${API_BASE_URL}</code></p>
                    <p>Please ensure:</p>
                    <ul style="text-align: left; margin: 10px 0;">
                        <li>Backend server is running</li>
                        <li>CORS is properly configured</li>
                        <li>API URL is correct</li>
                    </ul>
                    <div class="button-group" style="margin-top: 20px;">
                        <button onclick="location.reload()" class="success">🔄 Retry</button>
                        <button onclick="window.location.href='index.html'" class="secondary">🏠 Go Home</button>
                    </div>
                `);
                return;
            }

            // Proceed with validation if API is connected
            validateAccess();
        });
    </script>
</body>
</html>
