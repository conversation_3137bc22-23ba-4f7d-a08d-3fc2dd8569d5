const express = require('express');
const router = express.Router();
const authRouter = express.Router();

// Controllers
const {
    shareComponentPrivatelyController,
    getMyPrivateSharesController,
    getComponentsSharedWithMeController,
    revokePrivateShareController,
    getShareStatisticsController,
    validatePrivateShareToken,
    getComponentWithPrivateToken,
    generateShareableLinkController,
    getMyShareableLinksController,
    refreshShareableLinkControllerNew,
    bulkRevokeSharesController,
    getShareAnalyticsController,
    getSharedWithMeStatisticsController,
    deleteShareLinkController
} = require('../../controller/front/component_private_share.controller');

// Middleware
const { validateShareComponentPrivately, validateGetPrivateShares, validateAccessToken, validateGenerateShareableLink, validateBulkRevokeShares, validateShareId } = require('../../middlewares/validatePrivateShare');
const { validateComponentOwnership } = require('../../middlewares/validatePrivateAccess');

// Public routes (no authentication required)
// Validate private share token (for frontend)
router.get('/private-share/:token/validate', validateAccessToken, validatePrivateShareToken);

// Get component with private token (for frontend)
router.get('/private/:slug', getComponentWithPrivateToken);

// Authenticated routes
// Share component privately (by invite)
authRouter.post('/:id/share-privately', validateComponentOwnership, validateShareComponentPrivately, shareComponentPrivatelyController);

// Generate shareable link (by link)
authRouter.post('/:id/generate-link', validateComponentOwnership, validateGenerateShareableLink, generateShareableLinkController);

// Get my private shares
authRouter.post('/my-private-shares', validateGetPrivateShares, getMyPrivateSharesController);

// Get my shareable links
authRouter.post('/my-shareable-links', validateGetPrivateShares, getMyShareableLinksController);

// Get components shared with me 
authRouter.post('/shared-with-me', validateGetPrivateShares, getComponentsSharedWithMeController);

// Revoke private share
authRouter.post('/revoke-share/:shareId', revokePrivateShareController);

// Delete shareable link
authRouter.delete('/delete-share/:shareId', deleteShareLinkController);

// Refresh shareable link (generate new token)
authRouter.post('/refresh-share-link/:shareId', refreshShareableLinkControllerNew);

// Get share statistics
authRouter.get('/share-statistics', getShareStatisticsController);

// Bulk revoke shares
authRouter.post('/bulk-revoke-shares', validateBulkRevokeShares, bulkRevokeSharesController);

// Get share analytics
authRouter.get('/share/:shareId/analytics', validateShareId, getShareAnalyticsController);

// Get shared with me statistics
authRouter.get('/shared-with-me/statistics', getSharedWithMeStatisticsController);

module.exports = {
    router,
    authRouter
};