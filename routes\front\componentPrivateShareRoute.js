const express = require('express');
const router = express.Router();
const authRouter = express.Router();

// Controllers
const {
    shareComponentPrivatelyController,
    acceptPrivateShareController,
    getMyPrivateSharesController,
    getComponentsSharedWithMeController,
    revokePrivateShareController,
    getShareStatisticsController,
    validatePrivateShareToken,
    getComponentWithPrivateToken,
    generateShareableLinkController,
    getMyShareableLinksController
} = require('../../controller/front/component_private_share.controller');

// Middleware
const { validateShareComponentPrivately, validateGetPrivateShares, validateAccessToken, validateGenerateShareableLink } = require('../../middlewares/validatePrivateShare');
const { validateComponentOwnership } = require('../../middlewares/validatePrivateAccess');

// Public routes (no authentication required)
// Validate private share token (for frontend)
router.get('/private-share/:token/validate', validateAccessToken, validatePrivateShareToken);

// Get component with private token (for frontend)
router.get('/private/:slug', getComponentWithPrivateToken);

// Authenticated routes
// Share component privately (by invite)
authRouter.post('/:id/share-privately', validateComponentOwnership, validateShareComponentPrivately, shareComponentPrivatelyController);

// Generate shareable link (by link)
authRouter.post('/:id/generate-link', validateComponentOwnership, validateGenerateShareableLink, generateShareableLinkController);

// Get my private shares
authRouter.get('/my-private-shares', validateGetPrivateShares, getMyPrivateSharesController);

// Get my shareable links
authRouter.get('/my-shareable-links', validateGetPrivateShares, getMyShareableLinksController);

// Get components shared with me
authRouter.get('/shared-with-me', validateGetPrivateShares, getComponentsSharedWithMeController);

// Revoke private share
authRouter.delete('/private-share/:shareId/revoke', revokePrivateShareController);

// Get share statistics
authRouter.get('/share-statistics', getShareStatisticsController);

module.exports = {
    router,
    authRouter
};