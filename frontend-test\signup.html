<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Private Share Test</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="form-header">
                <h1>📝 Sign Up</h1>
                <p>Create your account to access shared components</p>
            </div>

            <div class="card">
                <div id="signupStep">
                    <div class="input-group">
                        <label for="email">Email Address:</label>
                        <input type="email" id="email" placeholder="Enter your email" required>
                    </div>
                    <div class="input-group">
                        <label for="firstName">First Name:</label>
                        <input type="text" id="firstName" placeholder="Enter your first name" required>
                    </div>
                    <div class="input-group">
                        <label for="lastName">Last Name:</label>
                        <input type="text" id="lastName" placeholder="Enter your last name" required>
                    </div>
                    <div class="input-group">
                        <label for="username">Username:</label>
                        <input type="text" id="username" placeholder="Choose a username" required>
                    </div>
                    <div class="input-group">
                        <label>
                            <input type="checkbox" id="termsAccepted" required>
                            I agree to the Terms and Conditions
                        </label>
                    </div>
                    <button onclick="sendSignupOTP()" id="sendOtpBtn">📧 Send OTP</button>
                </div>

                <div id="otpStep" style="display: none;">
                    <div class="input-group">
                        <label for="otp">Enter OTP:</label>
                        <input type="text" id="otp" placeholder="Enter 6-digit OTP" maxlength="6" required>
                    </div>
                    <div class="button-group">
                        <button onclick="verifySignupOTP()" id="verifyBtn">✅ Verify & Create Account</button>
                        <button onclick="resendOTP()" id="resendBtn" class="secondary">🔄 Resend OTP</button>
                    </div>
                    <p class="info-text">Check your email for the OTP. It expires in 10 minutes.</p>
                </div>

                <div class="navigation-links" style="margin-top: 20px; text-align: center;">
                    <p style="margin-bottom: 10px;">Already have an account? <a href="login.html" style="color: #667eea; text-decoration: none;">Login here</a></p>
                    <p><a href="index.html" style="color: #667eea; text-decoration: none;">← Back to Home</a></p>
                </div>
            </div>

            <div class="logs">
                <h3>📋 Signup Logs</h3>
                <div id="logContainer"></div>
                <button onclick="clearLogs()">🗑️ Clear Logs</button>
            </div>
        </div>
    </div>

    <script src="config.js"></script>
    <script>
        let signupData = {};

        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            
            // Check if already logged in
            if (isAuthenticated()) {
                logMessage('Already logged in', 'info');
                const returnUrl = getUrlParams().returnUrl;
                if (returnUrl) {
                    window.location.href = returnUrl;
                } else {
                    window.location.href = 'index.html';
                }
            }
        });

        async function sendSignupOTP() {
            const email = document.getElementById('email').value.trim();
            const firstName = document.getElementById('firstName').value.trim();
            const lastName = document.getElementById('lastName').value.trim();
            const username = document.getElementById('username').value.trim();
            const termsAccepted = document.getElementById('termsAccepted').checked;
            
            // Validation
            if (!email || !firstName || !lastName || !username) {
                logMessage('Please fill in all required fields', 'error');
                return;
            }

            if (!isValidEmail(email)) {
                logMessage('Please enter a valid email address', 'error');
                return;
            }

            if (!termsAccepted) {
                logMessage('Please accept the Terms and Conditions', 'error');
                return;
            }

            // Store signup data for later use
            signupData = {
                email,
                first_name: firstName,
                last_name: lastName,
                username,
                terms_and_conditions: termsAccepted,
                guest_id: 'test-guest-' + Date.now() // Generate a test guest ID
            };

            const hideLoading = showLoading(document.getElementById('sendOtpBtn'));

            try {
                const { response, data } = await apiCall('/auth/signup-with-email', {
                    method: 'POST',
                    body: JSON.stringify(signupData)
                });

                hideLoading();

                if (data.status === 202) {
                    logMessage('OTP sent successfully!', 'success');
                    
                    // Show OTP step
                    document.getElementById('signupStep').style.display = 'none';
                    document.getElementById('otpStep').style.display = 'block';
                    
                    // Focus on OTP input
                    document.getElementById('otp').focus();
                } else if (data.status === 409) {
                    logMessage('Account already exists. Please login instead.', 'warning');
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 2000);
                } else {
                    logMessage(data.message || 'Failed to send OTP', 'error');
                }
            } catch (error) {
                hideLoading();
                handleError(error, 'sending signup OTP');
            }
        }

        async function verifySignupOTP() {
            const otp = document.getElementById('otp').value.trim();
            
            if (!otp) {
                logMessage('Please enter the OTP', 'error');
                return;
            }

            if (otp.length !== 6) {
                logMessage('OTP must be 6 digits', 'error');
                return;
            }

            const hideLoading = showLoading(document.getElementById('verifyBtn'));

            try {
                // Add OTP to signup data
                const signupWithOTP = { ...signupData, otp };

                const { response, data } = await apiCall('/auth/signup-with-email', {
                    method: 'POST',
                    body: JSON.stringify(signupWithOTP)
                });

                hideLoading();

                if (data.status === 200) {
                    const userInfo = data.data;
                    const token = userInfo.token;
                    
                    // Save user session
                    saveUserSession(token, userInfo);
                    
                    logMessage('Account created successfully!', 'success');
                    
                    // Check for return URL
                    const params = getUrlParams();
                    const returnUrl = params.returnUrl;
                    
                    if (returnUrl) {
                        logMessage(`Redirecting to: ${returnUrl}`, 'info');
                        setTimeout(() => {
                            window.location.href = returnUrl;
                        }, 1000);
                    } else {
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 1000);
                    }
                } else {
                    logMessage(data.message || 'Invalid OTP', 'error');
                }
            } catch (error) {
                hideLoading();
                handleError(error, 'verifying signup OTP');
            }
        }

        async function resendOTP() {
            if (!signupData.email) {
                logMessage('Session expired. Please start over.', 'error');
                window.location.reload();
                return;
            }

            const hideLoading = showLoading(document.getElementById('resendBtn'));

            try {
                const { response, data } = await apiCall('/auth/resend/otp', {
                    method: 'POST',
                    body: JSON.stringify({ email: signupData.email })
                });

                hideLoading();

                if (data.status === 202) {
                    logMessage('OTP resent successfully!', 'success');
                } else {
                    logMessage(data.message || 'Failed to resend OTP', 'error');
                }
            } catch (error) {
                hideLoading();
                handleError(error, 'resending OTP');
            }
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }

        // Auto-submit OTP when 6 digits are entered
        document.addEventListener('DOMContentLoaded', function() {
            const otpInput = document.getElementById('otp');
            if (otpInput) {
                otpInput.addEventListener('input', function() {
                    if (this.value.length === 6) {
                        setTimeout(() => {
                            verifySignupOTP();
                        }, 500);
                    }
                });
            }
        });
    </script>
</body>
</html>
