// Npm declaration
const mongoose = require('mongoose');
const moment = require('moment');
const filePath = require('path');

// Models declaration
const Components = require('../../models/component.model').Components;
const Category = require('../../models/category.model').Category;
const ComponentLikes = require('../../models/component_likes.model').ComponentLikes;
const ComponentViews = require('../../models/component_views.model').ComponentViews;
const ComponentBookmarks = require('../../models/component_bookmark.model').ComponentBookmarks;
const ComponentUnlockHistory = require('../../models/component_unlock_history.model').ComponentUnlockHistory;
const ComponentStatistics = require('../../models/component_statistics.model').ComponentStatistics;
const GitlabUsers = require('../../models/gitlab_users.model').GitlabUsers;
const GitlabRepository = require('../../models/gitlab_repository.model').GitlabRepository;
const DraftComponents = require('../../models/draft_components.model').DraftComponents;
const RepositoryStars = require('../../models/repository_star.model').RepositoryStars;
const PlatformLicense = require('../../models/platform_licenses.model').PlatformLicense;
const ComponentSales = require('../../models/component_sales.model').ComponentSales;
const PaymentTransactions = require('../../models/payment_transactions.model').PaymentTransactions;

// Service declaration
const constants = require('../../config/constants');
const { ReS, sendError, escapeRegex, generateSlug, s3UploadStaticContent } = require('../../services/general.helper');
const { storeAndGetFingerprintData } = require('../../services/fingerprint_helper.service');
const { unlockTypes, filterTypes, elementDefaultTags } = require('../../config/component.constant');
const { manageComponentPurchase, calculateComponentUnlock, initiateComponentFiatPurchase } = require('../../services/mpc_balance.service');
const logger = require('../../config/logger');
const { fetchFullComponentData } = require('../../services/component.service');
const { componentType, componentState, transactionStatus } = require('../../config/component.constant');
const { checkAndManageComponentState, prepareLicenseDataForPublished } = require('../../services/component.service');
const { fetchRepoContent, fetchLastCommitDateOnBranch, fetchProjectLanguagesMultiple } = require('../../services/gitlab.helper');
const { getParityConfig, getBuyerFeeConfig, getAuthorFeeConfig, getItemPricingTiers } = require('./../../services/settings.service');

// Validations
const { publishRepositoryComponent } = require('../../validations/cms/component/componentValidation');
const { gitlabProductionBranch, repositoryState } = require('../../config/gitlab.constant');
const { Users } = require('../../models/users.model');


async function getComponentDetail(req, res) {
    try {
        const componentSlug = req.params.slug;
        const userId = new mongoose.Types.ObjectId(req.session._id); // Ensure object ID is properly formatted

        // Find the basic component info
        const basicComponentInfo = await Components.findOne({
            slug: componentSlug,
            'component_state': {
                $in: [componentState.PUBLISHED, componentState.PRIVATE]
            }
        }, { _id: 1, is_paid: 1 }).lean();

        if (!basicComponentInfo) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        const { _id: componentId, is_paid: isPaid } = basicComponentInfo;

        // Determine if the component has been unlocked by the user
        const unlockHistory = await ComponentUnlockHistory.findOne({
            component_id: componentId,
            unlock_by: userId,
            is_active: true
        }).lean();

        // Fetch the full component data
        const componentData = await fetchFullComponentData(componentSlug, isPaid, unlockHistory, userId);
        // Return success response with component data
        return ReS(res, constants.success_code, 'Data Fetched', componentData);
    } catch (err) {
        console.error(`Error retrieving component details: ${err}`);
        // Return a server error response
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getComponentDetailPublic(req, res) {
    try {
        // Set Default conditions to find the component
        const conditions = {
            'slug': req.params.slug,
            'component_state': {
                $in: [componentState.PUBLISHED, componentState.PRIVATE]
            }
        };
        // Find the component data based on the conditions
        const componentData = await Components.findOne(conditions)
            .populate({
                'path': 'platform_data.platform_id',
                'select': 'title slug image_url'
            }).populate({
                'path': 'platform_data.repository_id',
                'select': 'project_name project_id component_id description stars forks detected_platforms',
                'populate': {
                    'path': 'detected_platforms',
                    'select': 'title slug image_url'
                }
            }).populate({
                'path': 'collection_id',
                'select': 'title slug'
            }).populate({
                'path': 'created_by_user',
                'select': 'first_name last_name username email avatar biography'
            }).populate({
                'path': 'languages',
                'select': 'title slug image_url'
            }).lean();

        // If component data is not found, return a 404 error
        if (!componentData) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        // Find component statistics
        const componentStatistics = await ComponentStatistics.findOne({
            component_id: componentData._id
        }).lean();

        // Construct component statistics object
        componentData['statistics'] = {
            likes: (componentStatistics && componentStatistics.likes) ? componentStatistics.likes.total_likes : 0,
            views: (componentStatistics && componentStatistics.views) ? componentStatistics.views.total_views : 0,
            bookmarks: (componentStatistics && componentStatistics.bookmarks) ? componentStatistics.bookmarks.total_bookmarks : 0
        };

        componentData['is_allowed_to_view'] = false;
        componentData['is_author'] = false;

        // Check for community_variations counts
        const communityVariations = await Components.countDocuments({
            component_type: componentType.ELEMENTS,
            component_state: componentState.PUBLISHED,
            variant_id: (componentData.variant_id) ? componentData.variant_id : componentData._id
        });

        componentData['community_variations'] = communityVariations;

        // Check if componentData exists and has platform_data with at least one entry
        if (componentData?.platform_data?.length > 0) {

            // Iterate over each platform object in platform_data
            for (const platform of componentData.platform_data) {

                // If the platform contains a repository_id, update its updated_at property
                if (platform.repository_id) {
                    try {
                        // Fetch the last commit date for the specified branch and assign it to updated_at
                        platform.repository_id.updated_at = await fetchLastCommitDateOnBranch(
                            platform.repository_id.project_id,
                            gitlabProductionBranch
                        );
                    } catch (error) {
                        logger.error(`Error from gitlab while fetching public project details ${error}`);
                    }
                }
            }
        }
        componentData['license_id'] = await prepareLicenseDataForPublished(componentData._id);
        // Return success response with component data
        return ReS(res, constants.success_code, 'Data Fetched', componentData);
    } catch (err) {
        // Log any errors that occur
        logger.error(`Error at Front Controller getComponentDetail${err}`);
        // Return a server error response
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getAllComponents(req, res) {
    try {
        const totalDocuments = await Components.countDocuments();
        // Set default conditions
        const conditions = {
            parent_id: { $exists: false },
            component_state: componentState.PUBLISHED,
            component_type: {
                $in: [componentType.REPOSITORY]
            },
            created_by_user: { $exists: true }
        };

        let categoryData = {};

        if (req.body.category_id) {
            conditions['category_id'] = new mongoose.Types.ObjectId(req.body.category_id);
        }

        if (req.body.category_slug) {
            categoryData = await Category.findOne({
                category_slug: req.body.category_slug
            }, 'category_name category_slug image_url short_description').lean();
            conditions['category_id'] = categoryData._id;
        }

        if (req.body.search_text) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.search_text);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        if (req.body.tag) {
            conditions['identification_tag'] = req.body.tag
        }

        if (req.body.is_paid) {
            conditions['is_paid'] = req.body.is_paid;
        }
        // Set default sort
        let sort = {
            'created_at': -1
        };
        // Check if sort_by property exists in the request body and set sort criteria accordingly
        if (req.body.sort_by) {
            if (req.body.sort_by === filterTypes.MOST_LIKED) {
                // Sort by likes in descending order
                sort = {
                    likes: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.MOST_POPULAR) {
                // Sort by views in descending order
                sort = {
                    views: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_ADDITIONS) {
                // Sort by created_at in descending order
                sort = {
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.FEATURED) {
                // Sort by created_at in descending order
                sort = {
                    is_featured: -1,
                    views: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.PREMIUM) {
                conditions['is_paid'] = true;
            }
        }
        const limit = (req.body.limit) ? req.body.limit : 12;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await Components.countDocuments(conditions);

        const query = [{
            $match: conditions
        }, {
            '$sort': sort
        }, {
            '$skip': skip
        }, {
            '$limit': limit
        }, {
            $lookup: {
                from: 'users', // Join with users collection
                let: { creatorId: '$created_by_user' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$creatorId'] }
                        }
                    },
                    {
                        $project: {
                            username: 1,
                            first_name: 1,
                            last_name: 1,
                            avatar: 1
                        }
                    }
                ],
                as: 'created_by_user'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                created_at: 1,
                is_paid: 1,
                orientation: 1,
                views: 1,
                likes: 1,
                bookmarks: 1,
                created_by_user: {
                    $arrayElemAt: ['$created_by_user', 0]
                },
                component_state: 1,
                component_type: 1,
                is_featured: 1
            }
        }];

        const componentList = await Components.aggregate(query);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList,
            category: categoryData
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at Front Controller getAllComponents${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getAllChildComponents(req, res) {
    try {
        const totalDocuments = await Components.countDocuments();

        const parentComponent = await Components.findOne({
            'slug': req.params.slug
        }).lean();

        if (!parentComponent) {
            return ReS(res, constants.resource_not_found, 'Oops! Components Not Found.');
        }
        // Set default conditions
        const conditions = {
            parent_id: parentComponent._id,
            component_state: componentState.PUBLISHED
        };
        // Set default sort
        const sort = {
            'created_at': -1
        };

        if (req.body.title) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.title);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        const limit = (req.body.limit) ? req.body.limit : 10;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await Components.countDocuments(conditions);

        const query = [{
            $match: conditions
        }, {
            $sort: sort
        }, {
            $skip: skip
        }, {
            $limit: limit
        }, {
            $lookup: {
                from: 'categories',
                let: {
                    category_id: '$category_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$category_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        category_name: 1,
                        category_slug: 1
                    }
                }],
                as: 'category'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                description: 1,
                long_description: 1,
                created_at: 1,
                version: 1,
                is_paid: 1,
                views: 1,
                likes: 1,
                bookmarks: 1,
                category: {
                    $arrayElemAt: ['$category', 0]
                },
                orientation: 1
            }
        }];

        const componentList = await Components.aggregate(query);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList
        };

        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at Front Controller getAllChildComponents${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getComponentRecommendation(req, res) {
    try {
        // Set Default conditions
        const conditions = {
            'slug': req.params.slug
        };

        const component = await Components.findOne(conditions, 'collection_id').lean();

        if (!component) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        if (!component.collection_id) {
            return ReS(res, constants.success_code, 'Data Fetched', []);
        }

        // Set default filters
        const filters = {
            collection_id: component.collection_id,
            component_state: componentState.PUBLISHED,
            created_by_user: {
                $exists: true
            },
            _id: {
                $ne: component._id
            }
        };

        // Set default sort
        const sort = {
            views: -1
        };

        const recommendationList = await Components.aggregate([{
            $match: filters
        }, {
            $lookup: {
                from: 'users',
                let: {
                    user_id: '$created_by_user'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$user_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'email': 1,
                        'username': 1
                    }
                }],
                as: 'created_by_user'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                created_at: 1,
                is_paid: 1,
                views: 1,
                likes: 1,
                bookmarks: 1,
                downloads: 1,
                orientation: 1,
                component_state: 1,
                component_type: 1,
                collection_id: 1,
                created_by_user: {
                    $arrayElemAt: ['$created_by_user', 0]
                },
                short_description: 1
            }
        }, {
            $sort: sort
        }]);
        return ReS(res, constants.success_code, 'Data Fetched', recommendationList);
    } catch (err) {
        logger.error(`Error at Front Controller getComponentRecommendation ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getElementsRecommendation(req, res) {
    try {
        // Set Default conditions
        const conditions = {
            slug: req.params.slug,
            component_type: componentType.ELEMENTS,
            component_state: {
                $in: [componentState.PUBLISHED, componentState.PRIVATE]
            }
        };

        const element = await Components.findOne(conditions, 'category_id').lean();

        if (!element) {
            return ReS(res, constants.resource_not_found, 'Oops! Element Not Found.');
        }

        if (!element.category_id) {
            return ReS(res, constants.success_code, 'Data Fetched', []);
        }

        // Set default filters
        const filters = {
            category_id: element.category_id,
            component_state: componentState.PUBLISHED,
            component_type: componentType.ELEMENTS,
            created_by_user: {
                $exists: true
            },
            _id: {
                $ne: element._id
            }
        };

        // Set default sort
        const sort = {
            views: -1
        };

        const recommendationList = await Components.aggregate([{
            $match: filters
        }, {
            $lookup: {
                from: 'users',
                let: {
                    user_id: '$created_by_user'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$user_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'email': 1,
                        'username': 1
                    }
                }],
                as: 'created_by_user'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                created_at: 1,
                is_paid: 1,
                views: 1,
                likes: 1,
                bookmarks: 1,
                downloads: 1,
                orientation: 1,
                component_state: 1,
                component_type: 1,
                category_id: 1,
                elements_data: 1,
                live_preview: 1,
                gif_status: 1,
                gif_url: 1,
                linked_output: 1,
                element_container_meta: 1,
                created_by_user: {
                    $arrayElemAt: ['$created_by_user', 0]
                },
                short_description: 1
            }
        }, {
            $sort: sort
        }]);
        return ReS(res, constants.success_code, 'Data Fetched', recommendationList);
    } catch (err) {
        logger.error(`Error at Front Controller getElementsRecommendation ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function increaseComponentViews(req, res) {
    try {
        // Set Default conditions to find the component
        const conditions = {
            'slug': req.params.slug,
            'component_state': {
                $in: [componentState.PUBLISHED, componentState.PRIVATE]
            }
        };

        // Find the component data
        const componentData = await Components.findOne(conditions).lean();

        // If component data not found, return error
        if (!componentData) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }
        // Initialize fingerprint data
        let fingerprintData = {}; const postData = {};
        const requestId = req.headers['x-request-id'];
        const visitorId = req.headers['x-visitor-id'];

        if (requestId && visitorId) {
            // Try to fetch fingerprint data
            try {
                fingerprintData = await storeAndGetFingerprintData(visitorId, requestId);
            } catch (err) {
                logger.error('Error from fetching fingerprint data from fingerprint service');
            }
            postData['visitor_id'] = visitorId;
            postData['request_id'] = requestId;
        }
        // Prepare data to be posted
        postData['component_id'] = componentData._id;
        postData['viewed_by'] = req.session._id;
        // Extract additional geolocation data if available
        if (fingerprintData && fingerprintData.ipInfo && fingerprintData.ipInfo.data && fingerprintData.ipInfo.data.v4 && fingerprintData.ipInfo.data.v4.geolocation) {
            const geoData = fingerprintData.ipInfo.data.v4.geolocation;
            postData['city'] = geoData.city || '';
            postData['country'] = geoData.country || '';
            postData['continent'] = geoData.continent || '';
            postData['subdivisions'] = geoData.subdivisions ? geoData.subdivisions[0] : '';
        }

        // Create a new component like record
        await ComponentViews.create(postData);

        // Fetch total views from the database
        const count = await ComponentViews.countDocuments({
            component_id: componentData._id
        });

        // Return success response
        return ReS(res, constants.success_code, 'Success', { count });
    } catch (err) {
        logger.error(`Error at Front Controller likeComponent${err}`);
        // Return error response in case of any exception
        return ReS(res, constants.success_code, 'Oops! Something went wrong.');
    }
}

async function getAllComponentsForHomePage(req, res) {
    try {
        const totalDocuments = await Components.countDocuments();
        // Set default conditions
        const conditions = {
            parent_id: { $exists: false },
            component_state: componentState.PUBLISHED,
            component_type: {
                $in: [componentType.REPOSITORY]
            },
            created_by_user: { $exists: true }
        };
        // Set default sort
        let sort = {
            'created_at': -1
        };

        if (req.body.title) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.title);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        if (req.body.tag) {
            conditions['identification_tag'] = req.body.tag
        }

        if (req.body.is_paid) {
            conditions['is_paid'] = req.body.is_paid;
        }

        // Check if sort_by property exists in the request body and set sort criteria accordingly
        if (req.body.sort_by) {
            if (req.body.sort_by === filterTypes.MOST_LIKED) {
                // Sort by likes in descending order
                sort = {
                    likes: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.MOST_POPULAR) {
                // Sort by views in descending order
                sort = {
                    views: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_ADDITIONS) {
                // Sort by created_at in descending order
                sort = {
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.FEATURED) {
                // Sort by created_at in descending order
                sort = {
                    is_featured: -1,
                    views: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.PREMIUM) {
                conditions['is_paid'] = true;
            }
        }

        const limit = (req.body.limit) ? req.body.limit : 12;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await Components.countDocuments(conditions);

        const query = [{
            $match: conditions
        }, {
            '$sort': sort
        }, {
            '$skip': skip
        }, {
            '$limit': limit
        }, {
            $lookup: {
                from: 'users', // Join with users collection
                let: { creatorId: '$created_by_user' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$creatorId'] }
                        }
                    },
                    {
                        $project: {
                            username: 1,
                            first_name: 1,
                            last_name: 1,
                            avatar: 1
                        }
                    }
                ],
                as: 'created_by_user'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                video_url: 1,
                created_at: 1,
                is_paid: 1,
                orientation: 1,
                views: 1,
                likes: 1,
                bookmarks: 1,
                downloads: 1,
                created_by_user: {
                    $arrayElemAt: ['$created_by_user', 0]
                },
                component_state: 1,
                component_type: 1,
                identification_tag: 1,
                short_description: 1,
                is_featured: 1
            }
        }];

        const componentList = await Components.aggregate(query);
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at Front Controller getAllComponentsForHomePage${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function likeComponent(req, res) {
    try {
        // Set Default conditions to find the component
        const conditions = {
            'slug': req.params.slug,
            'component_state': componentState.PUBLISHED
        };

        // Find the component data
        const componentData = await Components.findOne(conditions).lean();

        // If component data not found, return error
        if (!componentData) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        // Check if the component has already been liked
        const componentLike = await ComponentLikes.findOne({
            component_id: componentData._id,
            liked_by: req.session._id
        }).lean();

        // If component has already been liked, return error
        if (componentLike) {
            return ReS(res, constants.bad_request_code, 'Component has already been liked.');
        }

        // Initialize fingerprint data
        let fingerprintData = {}; const postData = {};
        const requestId = req.headers['x-request-id'];
        const visitorId = req.headers['x-visitor-id'];

        if (requestId && visitorId) {
            // Try to fetch fingerprint data
            try {
                fingerprintData = await storeAndGetFingerprintData(visitorId, requestId);
            } catch (err) {
                logger.error('Error from fetching fingerprint data from fingerprint service');
            }
            postData['visitor_id'] = visitorId;
            postData['request_id'] = requestId;
        }
        // Prepare data to be posted
        postData['component_id'] = componentData._id;
        postData['liked_by'] = req.session._id;
        postData['creator'] = componentData.created_by_user;

        // Extract additional geolocation data if available
        if (fingerprintData && fingerprintData.ipInfo && fingerprintData.ipInfo.data && fingerprintData.ipInfo.data.v4 && fingerprintData.ipInfo.data.v4.geolocation) {
            const geoData = fingerprintData.ipInfo.data.v4.geolocation;
            postData['city'] = geoData.city || '';
            postData['country'] = geoData.country || '';
            postData['continent'] = geoData.continent || '';
            postData['subdivisions'] = geoData.subdivisions ? geoData.subdivisions[0] : '';
        }

        // Create a new component like record
        await ComponentLikes.create(postData);
        // Fetch total likes from the database
        const count = await ComponentLikes.countDocuments({
            component_id: componentData._id
        });
        // Return success response
        return ReS(res, constants.success_code, 'Success', { count });
    } catch (err) {
        logger.error(`Error at Front Controller likeComponent${err}`);
        // Return error response in case of any exception
        return ReS(res, constants.success_code, 'Oops! Something went wrong.');
    }
}

async function bookMarkComponent(req, res) {
    try {
        // Set Default conditions
        const conditions = {
            'slug': req.params.slug,
            'component_state': componentState.PUBLISHED
        };

        const componentData = await Components.findOne(conditions).lean();

        if (!componentData) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        const componentBookmark = await ComponentBookmarks.findOne({
            component_id: componentData._id,
            bookmarked_by: req.session._id
        }).lean();

        if (componentBookmark) {
            return ReS(res, constants.bad_request_code, 'Component has already been bookmarked.');
        }

        await ComponentBookmarks.create({
            component_id: componentData._id,
            creator: componentData.created_by_user,
            bookmarked_by: req.session._id
        });

        // Fetch total bookmarks from the database
        const count = await ComponentBookmarks.countDocuments({
            component_id: componentData._id
        });
        return ReS(res, constants.success_code, 'Success', { count });
    } catch (err) {
        logger.error(`Error at Front Controller bookMarkComponent${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function removeBookMarkComponent(req, res) {
    try {
        // Set Default conditions
        const conditions = {
            'slug': req.params.slug,
            'component_state': {
                $in: [componentState.PUBLISHED, componentState.PRIVATE]
            }
        };

        const componentData = await Components.findOne(conditions).lean();

        if (!componentData) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        const componentBookmark = await ComponentBookmarks.findOne({
            component_id: componentData._id,
            bookmarked_by: req.session._id
        }).lean();

        if (!componentBookmark) {
            return ReS(res, constants.bad_request_code, 'Oops! Component Bookmark Not Found.');
        }

        await ComponentBookmarks.findOneAndDelete({
            component_id: componentData._id,
            bookmarked_by: req.session._id
        });
        // Fetch total bookmarks from the database
        const count = await ComponentBookmarks.countDocuments({
            component_id: componentData._id
        });

        return ReS(res, constants.success_code, 'Success', { count });
    } catch (err) {
        logger.error(`Error at Front Controller removeBookMarkComponent${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getAllBookMarkedComponents(req, res) {
    try {
        // Set default conditions for bookmark
        const conditions = {
            bookmarked_by: req.session._id,
            is_active: true
        };
        const componentIds = (await ComponentBookmarks.distinct('component_id', conditions)).map((id) => new mongoose.Types.ObjectId(id));
        // Set default conditions for components
        const filters = {
            '_id': {
                $in: componentIds
            },
            'component_state': {
                $in: [componentState.PUBLISHED]
            },
            'created_by_user': {
                $exists: true
            },
            'component_type': {
                $exists: true
            }
        };

        const totalDocuments = await Components.countDocuments(filters);

        // Set default sort
        const sort = {
            'created_at': -1
        };

        const limit = (req.body.limit) ? req.body.limit : 12;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await Components.countDocuments(filters);

        const query = [{
            $match: filters
        }, {
            $sort: sort
        }, {
            $skip: skip
        }, {
            $limit: limit
        }, {
            $lookup: {
                from: 'component_likes',
                let: {
                    liked_by: new mongoose.Types.ObjectId(req.session._id),
                    component_id: '$_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$component_id', '$$component_id']
                            },
                            {
                                $eq: ['$liked_by', '$$liked_by']
                            }]
                        }
                    }
                }, {
                    $project: {
                        liked_by: 1,
                        component_id: 1
                    }
                }],
                as: 'component_likes'
            }
        }, {
            $lookup: {
                from: 'component_bookmarks',
                let: {
                    bookmarked_by: new mongoose.Types.ObjectId(req.session._id),
                    component_id: '$_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$component_id', '$$component_id']
                            },
                            {
                                $eq: ['$bookmarked_by', '$$bookmarked_by']
                            }]
                        }
                    }
                }, {
                    $project: {
                        liked_by: 1,
                        component_id: 1
                    }
                }],
                as: 'component_bookmarks'
            }
        }, {
            $lookup: {
                from: 'users',
                let: {
                    user_id: '$created_by_user'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$user_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'email': 1,
                        'username': 1,
                        'first_name': 1,
                        'last_name': 1,
                        'avatar': 1
                    }
                }],
                as: 'created_by_user'
            }
        }, {
            $lookup: {
                from: 'categories', // Join with categories collection
                let: { categoryId: '$category_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$categoryId'] }
                        }
                    },
                    {
                        $project: {
                            category_name: 1,
                            category_slug: 1
                        }
                    }
                ],
                as: 'category_id'
            }
        }, {
            $lookup: {
                from: 'gitlab_repositories',
                let: {
                    public_repository_id: '$public_repository_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$public_repository_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        stars: 1,
                        forks: 1,
                        gitlab_languages: 1,
                        last_pushed_at: 1
                    }
                }],
                as: 'public_repository_id'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                short_description: 1,
                long_description: 1,
                created_at: 1,
                version: 1,
                is_paid: 1,
                orientation: 1,
                views: 1,
                likes: 1,
                downloads: 1,
                bookmarks: 1,
                elements_data: 1,
                live_preview: 1,
                gif_status: 1,
                linked_output: 1,
                gif_url: 1,
                element_container_meta: 1,
                is_liked: {
                    $cond: { if: { $isArray: '$component_likes' }, then: { $gte: [{ $size: '$component_likes' }, 1] }, else: false }
                },
                is_bookmarked: {
                    $cond: { if: { $isArray: '$component_bookmarks' }, then: { $gte: [{ $size: '$component_bookmarks' }, 1] }, else: false }
                },
                created_by_user: {
                    $arrayElemAt: ['$created_by_user', 0]
                },
                category_id: {
                    $arrayElemAt: ['$category_id', 0]
                },
                public_repository_id: {
                    $arrayElemAt: ['$public_repository_id', 0]
                },
                component_type: 1
            }
        }];

        const componentList = await Components.aggregate(query);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at Front Controller getAllBookMarkedComponents${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getComponentUserMeta(req, res) {
    try {
        // Set default conditions for bookmark
        const component = await Components.findOne({
            slug: req.params.slug,
            component_state: {
                $in: [componentState.PUBLISHED, componentState.PRIVATE]
            }
        }, '_id component_type').populate({
            'path': 'platform_data.repository_id',
            'select': 'project_name project_id component_id description stars forks'
        }).lean();

        if (!component) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        const bookmarkCondition = {
            component_id: component._id,
            bookmarked_by: req.session._id,
            is_active: true
        };
        const likeCondition = {
            component_id: component._id,
            liked_by: req.session._id,
            is_active: true
        };
        const viewsCondition = {
            component_id: component._id,
            viewed_by: req.session._id
        };

        // Reduce the platform_data to create an initial repository map with default values
        const repository = component?.platform_data?.reduce((acc, item) => {
            const id = item.repository_id?._id; // Extract the repository ID
            if (id) { // Ensure the ID exists before processing
                acc[id] = { starred: false, forked: false }; // Default values for starred and forked
            }
            return acc;
        }, {});

        // Fetch the GitLab user associated with the current session
        const gitlabUser = await GitlabUsers.findOne({
            user_id: req.session._id
        }, '_id').lean();

        // Check if the component is of type REPOSITORY
        if ([componentType.REPOSITORY, componentType.MOBILE].includes(component?.component_type) && component?.platform_data?.length) {
            // Iterate over each platform data entry
            for (const platform of component.platform_data) {
                const repositoryId = platform?.repository_id?._id; // Extract the repository ID

                if (!repositoryId) continue; // Skip if repository ID is missing

                // Check if the repository is starred by the user
                const isStarred = await RepositoryStars.exists({
                    user_id: req.session._id,
                    repository_id: repositoryId
                });

                if (isStarred) {
                    repository[repositoryId].starred = true;
                }

                // Check if the repository is forked by the user in GitLab
                if (gitlabUser) {
                    const isForked = await GitlabRepository.exists({
                        fork_id: repositoryId,
                        gitlab_user_id: gitlabUser._id,
                        is_deleted: false
                    });

                    if (isForked) {
                        repository[repositoryId].forked = true;
                    }
                }
            }
        }

        // Execute both queries concurrently
        const [componentBookMarked, componentLiked, componentViewed] = await Promise.all([
            ComponentBookmarks.findOne(bookmarkCondition).select('_id').lean(),
            ComponentLikes.findOne(likeCondition).select('_id').lean(),
            ComponentViews.findOne(viewsCondition).select('_id').lean()
        ]);
        // Determine statuses
        const isBookMarked = Boolean(componentBookMarked);
        const isLiked = Boolean(componentLiked);
        const isViewed = Boolean(componentViewed);
        // Respond with data
        return ReS(res, constants.success_code, 'Data Fetched', { isBookMarked, isLiked, isViewed, repository });
    } catch (err) {
        logger.error(`Error at Front Controller getComponentUserMeta${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function removeComponentLike(req, res) {
    try {
        // Set Default conditions
        const conditions = {
            'slug': req.params.slug,
            'component_state': {
                $in: [componentState.PUBLISHED, componentState.PRIVATE]
            }
        };

        const componentData = await Components.findOne(conditions).lean();

        if (!componentData) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        const componentLike = await ComponentLikes.findOne({
            component_id: componentData._id,
            liked_by: req.session._id
        }).lean();

        if (!componentLike) {
            return ReS(res, constants.bad_request_code, 'Oops! Component Like Not Found.');
        }

        await ComponentLikes.findOneAndDelete({
            component_id: componentData._id,
            liked_by: req.session._id
        });

        // Fetch total likes from the database
        const count = await ComponentLikes.countDocuments({
            component_id: componentData._id
        });
        return ReS(res, constants.success_code, 'Success', { count });
    } catch (err) {
        logger.error(`Error at Front Controller removeComponentLike${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function unlockComponent(req, res) {
    try {
        const { use_point_balance = true, use_fiat_balance = true } = req.body;
        // Set default conditions to find the component
        const conditions = {
            'slug': req.params.slug,
            component_type: {
                $in: [componentType.REPOSITORY, componentType.MOBILE]
            },
        };

        // Find the component data
        const component = await Components.findOne(conditions).lean();

        // If component data not found, return error
        if (!component) {
            return ReS(res, constants.resource_not_found, 'Oops! Project Not Found.');
        }

        if (component.component_state != componentState.PUBLISHED) {
            return ReS(res, constants.bad_request_code, 'Oops! Project is not published.');
        }

        // If the component is already free, return error
        if (!component.is_paid) {
            return ReS(res, constants.bad_request_code, 'This project is already free and does not require unlocking.');
        }

        if (component.created_by_user.toString() == req.session._id.toString()) {
            return ReS(res, constants.bad_request_code, 'Oops! As the author of this project, you\'re not allowed to unlock it.');
        }

        // Check if the component is already unlocked by the user
        const componentUnlock = await ComponentUnlockHistory.findOne({
            component_id: component._id,
            unlock_by: req.session._id,
            is_active: true
        });

        if (componentUnlock) {
            return ReS(res, constants.bad_request_code, 'You have already unlocked this project.');
        }

        const { deductFromUser, creditToAdmin, creditToCreator, fiat_applied, mpn_points_applied, net_payable_fiat } = await manageComponentPurchase(req.session._id, component._id, component, component.component_type, use_point_balance, use_fiat_balance);

        // Prepare unlock history
        const unlockHistory = {
            unlock_by: req.session._id,
            component_id: component._id,
            component_name: component.title,
            component_slug: component.slug,
            price: component.purchase_price,
            is_active: true,
            mpn_parity: component.mpn_parity,
            expense: deductFromUser,
        };

        // If the unlock type is limited, set the expiry date
        if (component.unlock_type === unlockTypes.LIMITED) {
            const currentDate = moment();
            const expiredOn = currentDate.add(component.unlock_duration, 'days');
            unlockHistory['expired_on'] = expiredOn;
        }

        // Create unlock history
        await ComponentUnlockHistory.create(unlockHistory);

        const componentSalesObj = {
            component_id: component?._id,
            creator_id: component?.created_by_user,
            purchaser_id: req.session._id,
            mpn_parity: component?.mpn_parity || 0,
            purchase_price: component?.purchase_price,
            item_price: component?.item_price,
            buyer_fee: component?.buyer_fee,
            distribution: {
                to_creator: creditToCreator,
                to_admin: creditToAdmin,
                total_credited: {
                    fiat: creditToCreator?.fiat + creditToAdmin?.fiat,
                    mpn_points: creditToCreator?.mpn_points + creditToAdmin?.mpn_points
                }
            },
            payment_breakdown: {
                used_mpn_points: mpn_points_applied,
                used_fiat_from_wallet: fiat_applied,
                paid_externally: net_payable_fiat
            }
        };
        // Create Component sales history
        await ComponentSales.create(componentSalesObj);

        // Return success response
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        // Log and return error response in case of any exception
        logger.error(`Error at Front Controller unlockComponent ${err}`);
        return sendError(res, err);
    }
}

async function getAllLikedComponents(req, res) {
    try {
        // Set default conditions for likes
        const conditions = {
            liked_by: req.session._id,
            is_active: true
        };

        const componentIds = (await ComponentLikes.distinct('component_id', conditions)).map((id) => new mongoose.Types.ObjectId(id));

        // Set default conditions for components
        const filters = {
            '_id': {
                $in: componentIds
            },
            'component_state': {
                $in: [componentState.PUBLISHED]
            },
            'created_by_user': {
                $exists: true
            },
            'component_type': {
                $exists: true
            }
        };

        const totalDocuments = await Components.countDocuments(filters);

        // Set default sort
        const sort = {
            'created_at': -1
        };

        const limit = (req.body.limit) ? req.body.limit : 12;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await Components.countDocuments(filters);

        const query = [{
            $match: filters
        }, {
            $sort: sort
        }, {
            $skip: skip
        }, {
            $limit: limit
        }, {
            $lookup: {
                from: 'component_bookmarks', // Join with component_bookmarks collection
                let: {
                    bookmarked_by: new mongoose.Types.ObjectId(req.session._id),
                    component_id: '$_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$component_id', '$$component_id']
                            },
                            {
                                $eq: ['$bookmarked_by', '$$bookmarked_by']
                            }]
                        }
                    }
                }, {
                    $project: {
                        liked_by: 1,
                        component_id: 1
                    }
                }],
                as: 'component_bookmarks'
            }
        }, {
            $lookup: {
                from: 'component_likes', // Join with component_likes collection
                let: {
                    liked_by: new mongoose.Types.ObjectId(req.session._id),
                    component_id: '$_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$component_id', '$$component_id']
                            },
                            {
                                $eq: ['$liked_by', '$$liked_by']
                            }]
                        }
                    }
                }, {
                    $project: {
                        liked_by: 1,
                        component_id: 1
                    }
                }],
                as: 'component_likes'
            }
        }, {
            $lookup: {
                from: 'users', // Join with users collection
                let: { creatorId: '$created_by_user' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$creatorId'] }
                        }
                    },
                    {
                        $project: {
                            username: 1,
                            first_name: 1,
                            last_name: 1,
                            avatar: 1
                        }
                    }
                ],
                as: 'created_by_user'
            }
        }, {
            $lookup: {
                from: 'categories', // Join with categories collection
                let: { categoryId: '$category_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$categoryId'] }
                        }
                    },
                    {
                        $project: {
                            category_name: 1,
                            category_slug: 1
                        }
                    }
                ],
                as: 'category_id'
            }
        }, {
            $lookup: {
                from: 'gitlab_repositories',
                let: {
                    public_repository_id: '$public_repository_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$public_repository_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        stars: 1,
                        forks: 1,
                        gitlab_languages: 1,
                        last_pushed_at: 1
                    }
                }],
                as: 'public_repository_id'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                short_description: 1,
                long_description: 1,
                created_at: 1,
                version: 1,
                is_paid: 1,
                orientation: 1,
                views: 1,
                likes: 1,
                downloads: 1,
                bookmarks: 1,
                elements_data: 1,
                live_preview: 1,
                gif_status: 1,
                linked_output: 1,
                gif_url: 1,
                element_container_meta: 1,
                created_by_user: {
                    $arrayElemAt: ['$created_by_user', 0]
                },
                category_id: {
                    $arrayElemAt: ['$category_id', 0]
                },
                public_repository_id: {
                    $arrayElemAt: ['$public_repository_id', 0]
                },
                is_liked: {
                    $cond: { if: { $isArray: '$component_likes' }, then: { $gte: [{ $size: '$component_likes' }, 1] }, else: false }
                },
                is_bookmarked: {
                    $cond: { if: { $isArray: '$component_bookmarks' }, then: { $gte: [{ $size: '$component_bookmarks' }, 1] }, else: false }
                },
                component_type: 1
            }
        }];

        const componentList = await Components.aggregate(query);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at Front Controller getAllLikedComponents${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getComponentPlatformDetail(req, res) {
    try {
        // Set Default conditions to find the component
        const conditions = {
            'slug': req.params.slug,
            'is_deleted': false
        };

        // Find the component platform data based on the conditions
        const componentData = await Components.findOne(conditions, {
            'platform_data.platform_id': 1
        }).populate({
            'path': 'platform_data.platform_id',
            'select': 'title slug image_url'
        }).lean();

        // If component data is not found, return a 404 error
        if (!componentData) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }
        // Return success response with component data
        return ReS(res, constants.success_code, 'Data Fetched', componentData);
    } catch (err) {
        // Log any errors that occur
        logger.error(`Error at Front Controller getComponentPlatformDetail${err}`);
        // Return a server error response
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

// Function to retrieve component search suggestions
async function getComponentSearchSuggestions(req, res) {
    try {
        const { component_type = componentType.REPOSITORY } = req.query;
        // Set Default conditions to find the component
        const conditions = {
            parent_id: { $exists: false },
            component_state: componentState.PUBLISHED,
            component_type: {
                $in: [component_type]
            },
            created_by_user: { $exists: true }
        };

        // If there's a search text provided, add it to the conditions
        if (req.query.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.query.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        // Find the distinct titles of components based on the conditions
        const suggestionList = await Components.distinct('title', conditions).lean();

        // Return success response with component titles
        return ReS(res, constants.success_code, 'Data Fetched', suggestionList);
    } catch (err) {
        // Log any errors that occur
        logger.error(`Error at Front Controller getComponentSearchSuggestions${err}`);
        // Return a server error response
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function detachRepositoryFromComponent(req, res) {
    try {
        const { id, repository_id } = req.params;
        // Fetch the component data based on the component ID from the request parameters
        const componentData = await DraftComponents.findOne(
            { _id: id, created_by_user: req.session._id },
            'component_type'
        ).lean();

        // Check if the component exists
        if (!componentData) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        const codeSpace = await GitlabRepository.findOne({ _id: repository_id }).lean();

        if (!codeSpace) {
            return ReS(res, constants.bad_request_code, 'Oops! Invalid code-space provided.');
        }

        // Update the component by removing the platform data
        await DraftComponents.updateOne(
            { _id: id },
            { $pull: { platform_data: { repository_id: repository_id } } }
        );

        // Update the GitlabRepository by unsetting the component_id
        await GitlabRepository.updateOne(
            { component_draft_id: id, _id: repository_id },
            { $unset: { component_draft_id: 1 } }
        );
        // Respond with success
        return ReS(res, constants.success_code, 'Platform Unlinked Successfully');
    } catch (err) {
        logger.error(`Error at Front Controller detachRepositoryFromComponent: ${err}`);
        return sendError(res, err);
    }
}

async function createComponentPlaceHolder(req, res) {
    try {
        const { title = 'Untitled', component_type = componentType.REPOSITORY, category_id } = req.body;

        // Fetch GitLab user information from the database based on the admin ID stored in the session
        const gitlabUser = await GitlabUsers.findOne({
            user_id: req.session._id
        }).lean();

        // Check if the GitLab user record was found
        if (!gitlabUser) {
            // If the GitLab user record does not exist, respond with an error message
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exists.');
        }

        const componentSlug = generateSlug(title);

        const component_id = new mongoose.Types.ObjectId();

        const newComponent = await DraftComponents.create({
            _id: component_id,
            title: title,
            slug: componentSlug,
            component_state: componentState.PLACEHOLDER,
            created_by_user: req.session._id,
            component_type: component_type,
            category_id: category_id
        });

        return ReS(res, constants.success_code, 'Success', {
            component_id: newComponent._id
        });
    } catch (err) {
        logger.error(`Error at Front Controller createComponentPlaceHolder${err}`);
        return sendError(res, err);
    }
}

async function updateRepositoryComponent(req, res) {
    try {
        const postData = req.body;
        // Fetch the component data based on the component ID from the request parameters
        const componentData = await DraftComponents.findOne({
            _id: req.params.id,
            created_by_user: new mongoose.Types.ObjectId(req.session._id),
            component_type: {
                $in: [componentType.REPOSITORY, componentType.MOBILE]
            },
        }, 'component_type component_state platform_data').lean();

        // Check if the component exists
        if (!componentData) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        // Fetch GitLab user information based on the admin ID from the session
        const gitlabUser = await GitlabUsers.findOne({ user_id: req.session._id }).lean();

        // Check if the GitLab user record exists
        if (!gitlabUser) {
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exist.');
        }

        // Generate a slug if the title is provided in postData
        if (postData.title) {
            postData.slug = generateSlug(postData.title);
        }

        // Check if the current component state is PLACEHOLDER

        if (postData?.platform_data?.length) {
            const repositoryIds = postData.platform_data
                .map((platform) => platform?.repository_id)
                .filter(Boolean); // Remove undefined or null IDs

            if (repositoryIds.length) {
                // Check if any repository has a "public" state
                const hasPublicRepo = await GitlabRepository.exists({
                    _id: { $in: repositoryIds },
                    state: repositoryState.PUBLIC
                });

                if (hasPublicRepo) {
                    return ReS(res, constants.bad_request_code, 'Oops! One or more repositories have a public state.');
                }
            }
        }


        // If the component has platform data, update its state to ACTIVE_DRAFT
        if (componentData?.platform_data?.length !== postData?.platform_data?.length) {
            postData.component_state = componentState.ACTIVE_DRAFT;
        } else {
            // No platform data is present, so check and potentially manage the component's state
            const isComponentStateChanged = await checkAndManageComponentState(req.params.id, postData);
            // If the component state was changed by the previous operation, update the state to ACTIVE_DRAFT
            if (isComponentStateChanged) {
                postData.component_state = componentState.ACTIVE_DRAFT;
            }
        }

        // Set the 'updated_by_user' field to the current session's admin ID
        postData.updated_by_user = req.session._id;

        const updateQuery = { '$set': postData };

        // Update the component with the new data
        await DraftComponents.updateOne({ _id: req.params.id }, updateQuery);

        // Respond with success
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at Front Controller updateRepositoryComponent: ${err}`);
        return sendError(res, err);
    }
}

async function linkCodeSpacesToComponent(req, res) {
    try {
        const { id } = req.params; // Extract the component ID from request parameters
        const { code_spaces } = req.body; // Extract the list of code space IDs from the request body

        // Fetch the component data based on the component ID and ensure it is of type REPOSITORY
        const componentData = await DraftComponents.findOne({
            _id: id,
            component_type: {
                $in: [componentType.REPOSITORY, componentType.MOBILE]
            },
        }, '_id').lean();

        // If the component doesn't exist or isn't of type REPOSITORY, return an error response
        if (!componentData) {
            return ReS(res, constants.bad_request_code, 'Oops! Invalid or Non-existent Component Provided.');
        }

        // Convert code space IDs from string to ObjectId
        const codeSpaceIds = code_spaces.map((id) => new mongoose.Types.ObjectId(id));

        if (codeSpaceIds.length) {
            // Check if any repository has a "public" state
            const hasPublicRepo = await GitlabRepository.exists({
                _id: {
                    $in: codeSpaceIds
                },
                state: repositoryState.PUBLIC
            });

            if (hasPublicRepo) {
                return ReS(res, constants.bad_request_code, 'Oops! One or more repositories have a public state.');
            }
        }
        // Fetch code spaces that match the provided IDs and aren't linked to any component
        const codeSpaceList = await GitlabRepository.find({
            _id: { $in: codeSpaceIds },
            component_id: { $exists: false } // Exclude code spaces that are already linked
        }, 'platform_id').lean();

        // If the number of fetched code spaces doesn't match the number provided, return an error response
        if (codeSpaceList.length !== code_spaces.length) {
            return ReS(res, constants.bad_request_code, 'Oops! One or more code-spaces are invalid or already linked.');
        }

        // Prepare an array of code spaces to be linked to the component, excluding the _id field
        const codeSpaceArray = codeSpaceList.map(({ _id, ...rest }) => ({
            ...rest,
            repository_id: _id
        }));

        // Update the component by adding the valid code spaces to the platform_data array
        await DraftComponents.updateOne(
            { _id: id },
            { $push: { platform_data: { $each: codeSpaceArray } } }
        );

        // Link the code spaces to the component by setting the component_id in the GitlabRepository documents
        await GitlabRepository.updateMany(
            { _id: { $in: codeSpaceIds } },
            { $set: { component_draft_id: id } }
        );

        // If all operations succeed, return a success response
        return ReS(res, constants.success_code, 'Code-space linked Successfully', codeSpaceArray);
    } catch (err) {
        // Log the error and return a server error response
        logger.error(`Error at Front Controller linkCodeSpacesToComponent: ${err}`);
        return sendError(res, err);
    }
}

async function getAllComponentList(req, res) {
    try {
        const totalDocuments = await Components.countDocuments();
        // Set default conditions
        const conditions = {
            created_by_user: new mongoose.Types.ObjectId(req.session._id),
            component_state: {
                $in: [componentState.ACTIVE_DRAFT, componentState.PUBLISHED]
            }
        };

        if (req.body.component_state) {
            conditions['component_state'] = req.body.component_state;
        }

        // Set default sort
        const sort = {
            'created_at': -1
        };

        if (req.body.category_id && req.body.category_id.length) {
            conditions['category_id'] = {
                $in: (req.body.category_id).map((id) => new mongoose.Types.ObjectId(id))
            };
        }

        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        const limit = (req.body.limit) ? req.body.limit : 10;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await Components.countDocuments(conditions);

        const query = [{
            $match: conditions
        }, {
            $lookup: {
                from: 'categories',
                let: {
                    category_id: '$category_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$category_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        category_name: 1,
                        category_slug: 1
                    }
                }],
                as: 'category'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                description: 1,
                long_description: 1,
                created_at: 1,
                version: 1,
                is_active: 1,
                category: {
                    $arrayElemAt: ['$category', 0]
                },
                orientation: 1,
                component_type: 1
            }
        }];
        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });
        const componentList = await Components.aggregate(query);
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at Front Controller getAllComponentList${err}`);
        return sendError(res, err);
    }
}

async function getRepoComponentDetails(req, res) {
    try {
        // Set default conditions
        const conditions = {
            '_id': req.params.id
        };
        const componentData = await Components.findOne(conditions)
            .populate({
                'path': 'platform_data.platform_id',
                'select': 'title slug image_url'
            }).populate({
                'path': 'platform_data.repository_id'
            }).populate({
                'path': 'collection_id',
                'select': 'title slug'
            }).lean();

        if (!componentData) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        if (componentData?.platform_data?.length) {
            for (const platform of componentData.platform_data) {
                if (platform?.repository_id?.project_id) {
                    const fileCheck = await fetchRepoContent(platform.repository_id.project_id);
                    platform['repository_id']['file_not_exists'] = (fileCheck && fileCheck.file_not_exists == true) ? true : false;
                }
            }
        }

        return ReS(res, constants.success_code, 'Data Fetched', componentData);
    } catch (err) {
        logger.error(`Error at Front Controller getRepoComponentDetails${err}`);
        return sendError(res, err);
    }
}

async function uploadComponentAssets(req, res) {
    try {
        // Check if the 'documents' field exists in the 'req.files' object and if it's not an array but an object
        if (req.files && req.files.documents && !Array.isArray(req.files.documents) && typeof req.files.documents == 'object') {
            // Convert the 'documents' object into an array containing a single element
            req.files.documents = [req.files.documents];
        }
        const assets = [];
        const maxSizeAllowed = 5 * 1024 * 1024; // 5 MB in bytes
        if (req && req.files && req.files.documents && req.files.documents.length) {

            const isFileSizeValid = req.files.documents.every((file) => file.size <= maxSizeAllowed);

            if (!isFileSizeValid) {
                return ReS(res, constants.bad_request_code, 'This file exceeds the maximum size. Max file size is 5mb.');
            }
            for (const document of req.files.documents) {
                const fileExtension = filePath.extname(document.name);
                const filename = `${new Date().getTime()}${fileExtension}`;
                const documentPath = `design-kit/${filename}`;
                const metaObj = JSON.parse(req.body[document.name]);
                const uploadRes = await s3UploadStaticContent(document, documentPath);
                if (uploadRes) {
                    assets.push({
                        'file_name': metaObj.name,
                        'file_original_name': document.name,
                        'file_url': documentPath,
                        'file_mime_type': document.mimetype,
                        'file_extension': fileExtension,
                        'file_notes': metaObj.notes
                    });
                } else {
                    return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
                }
            }
        }
        return ReS(res, constants.success_code, 'Data Fetched', assets);
    } catch (err) {
        logger.error(`Error at Front Controller uploadComponentAssets${err}`);
        return sendError(res, err);
    }
}

async function publishDraftComponent(req, res) {
    try {
        const componentId = req.params.id;

        const conditions = {
            _id: componentId,
            created_by_user: req.session._id
        };

        // Fetch the component by ID with selected fields
        const component = await DraftComponents.findOne(conditions).populate({
            'path': 'platform_data.repository_id',
            'select': 'project_name project_id detected_platforms'
        }).lean();
        // Return an error response if the component is not found
        if (!component) {
            return ReS(res, constants.resource_not_found, 'Oops! Project Not Found.');
        }

        // Validate component data before publishing
        const { error } = publishRepositoryComponent(component);
        if (error) {
            return ReS(res, constants.bad_request_code, error.details[0].message, 'ValidationError');
        }

        // Check for a unique slug across non-placeholder components
        const existingComponent = await Components.findOne({
            slug: component.slug,
            component_draft_id: { $ne: component._id }
        }).lean();

        // Return an error if a component with the same slug already exists
        if (existingComponent) {
            return ReS(res, constants.conflict_code, `Oops! Project with title ${component.title} already published.`);
        }

        // Ensure that at least one code-space is linked to the component
        if (!component.platform_data?.length) {
            return ReS(res, constants.bad_request_code, 'Oops! No Linked Code-space Found.');
        }

        // Extract repository_id into an array
        const repositoryIds = component?.platform_data.map((item) => item.repository_id);

        const existingPublishedComponent = await Components.findOne({ component_draft_id: componentId }).lean();

        // Check if component already published then detach existing code space 
        if (existingPublishedComponent) {
            // Extract repository_id into an array
            const repositoryIds = existingPublishedComponent?.platform_data.map((item) => item.repository_id);
            // Updated published component_id & removed draft id
            await GitlabRepository.updateMany({
                _id: repositoryIds
            }, {
                $unset: {
                    component_id: existingPublishedComponent._id
                }
            });
        }

        // Prepare technologies array from all linked code-spaces 
        const technologies = [
            ...new Set(
                component.platform_data.flatMap((platform) =>
                    Array.isArray(platform.platform_id) ? platform.platform_id : []
                )
            )
        ];

        // Prepare detected technologies array from all linked code-spaces 
        const detectedTechnologies = component.platform_data.flatMap((item) => item.repository_id?.detected_platforms || []);
        // Prepare component data object that would be transferred from daft to published
        const componentObj = {
            title: component?.title,
            slug: component?.slug,
            short_description: component?.short_description,
            long_description: component?.long_description,
            image_url: component?.image_url,
            thumbnail_url: component?.thumbnail_url,
            video_url: component?.video_url,
            platform_data: component?.platform_data,
            created_by_user: component?.created_by_user,
            updated_by_user: component?.updated_by_user,
            collection_id: component?.collection_id,
            component_type: component?.component_type,
            orientation: component?.orientation,
            difficulty_level: component?.difficulty_level,
            identification_tag: component?.identification_tag,
            is_paid: component?.is_paid,
            component_state: componentState.PUBLISHED,
            technologies: technologies,
            license_id: component?.license_id,
            mpn_parity: component?.mpn_parity,
            purchase_price: component?.purchase_price,
            item_price: component?.item_price,
            buyer_fee: component?.buyer_fee,
            last_published_at: new Date(),
            detected_technologies: detectedTechnologies,
            category_id: component?.category_id
        };

        // Update the component's state to published
        const publishedComponent = await Components.findOneAndUpdate({
            component_draft_id: component._id
        }, {
            $set: componentObj,
            $setOnInsert: {
                createdOn: new Date()
            },
            $inc: {
                published_count: 1
            }
        }, {
            upsert: true,
            new: true
        }).lean();

        // Updated draft component status to published
        await DraftComponents.updateOne({
            _id: component._id
        }, {
            $set: {
                component_state: componentState.PUBLISHED
            }
        });

        // Updated published component_id & removed draft id
        await GitlabRepository.updateMany({
            _id: repositoryIds
        }, {
            $set: {
                component_id: publishedComponent._id,
                component_draft_id: component._id
            }
        });
        // Return a success response
        return ReS(res, constants.success_code, 'Project published successfully', { _id: publishedComponent._id });

    } catch (err) {
        // Log and handle any errors
        logger.error(`Error at Front Controller publishDraftComponent: ${err}`);
        return sendError(res, err);
    }
}

async function getDraftComponentDetails(req, res) {
    try {
        // Set default conditions
        const conditions = {
            '_id': req.params.id
        };

        const componentData = await DraftComponents.findOne(conditions)
            .populate({
                'path': 'platform_data.platform_id',
                'select': 'title slug image_url'
            }).populate({
                'path': 'platform_data.repository_id'
            }).populate({
                'path': 'collection_id',
                'select': 'title slug'
            }).populate({
                'path': 'created_by_user',
                'select': 'first_name last_name username email avatar'
            }).lean();

        if (!componentData) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        const publishedComponent = await Components.findOne({
            component_draft_id: componentData._id,
            component_state: componentState.PUBLISHED
        }, '_id title slug').populate({
            'path': 'created_by_user',
            'select': 'first_name last_name username email avatar'
        }).lean();

        // If any version is live of this component attach published component details
        if (publishedComponent) {
            componentData['published_component'] = publishedComponent;
        }

        // Check if any version is live of this component or not
        componentData['is_published'] = (publishedComponent) ? true : false;

        if (componentData?.platform_data?.length) {
            for (const platform of componentData.platform_data) {
                if (platform?.repository_id?.project_id) {
                    const fileCheck = await fetchRepoContent(platform.repository_id.project_id);
                    platform['repository_id']['file_not_exists'] = (fileCheck && fileCheck.file_not_exists == true) ? true : false;
                }
            }
        }
        return ReS(res, constants.success_code, 'Data Fetched', componentData);
    } catch (err) {
        logger.error(`Error at Front Controller getDraftComponentDetails ${err}`);
        return sendError(res, err);
    }
}

async function makePrivatePublishedComponent(req, res) {
    try {
        const componentId = req.params.id;

        const conditions = {
            _id: componentId,
            created_by_user: req.session._id,
            component_type: {
                $in: [componentType.REPOSITORY, componentType.MOBILE]
            },
        };

        // Fetch the component by ID with selected fields
        const draftComponent = await DraftComponents.findOne(conditions).lean();

        // Return an error response if the component is not found
        if (!draftComponent) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        // Fetch the component by ID with selected fields
        const publishedComponent = await Components.findOne({
            component_draft_id: draftComponent._id,
            component_state: componentState.PUBLISHED
        }).lean();

        if (!publishedComponent) {
            return ReS(res, constants.bad_request_code, 'Oops! There is no published version to take down.');
        }

        // Changed component state from publish to private
        await Components.updateOne({
            _id: publishedComponent._id
        }, {
            $set: {
                component_state: componentState.PRIVATE
            }
        });
        // Return a success response
        return ReS(res, constants.success_code, 'Component unpublished successfully', { _id: publishedComponent._id });

    } catch (err) {
        // Log and handle any errors
        logger.error(`Error at Front Controller makePrivatePublishedComponent: ${err}`);
        return sendError(res, err);
    }
}

async function softDeleteDraftComponent(req, res) {
    try {
        const componentId = req.params.id;

        const conditions = {
            _id: componentId,
            created_by_user: req.session._id,
            component_state: componentState.ACTIVE_DRAFT
        };

        // Fetch the component by ID with selected fields
        const draftComponent = await DraftComponents.findOne(conditions).lean();

        // Return an error response if the component is not found
        if (!draftComponent) {
            return ReS(res, constants.resource_not_found, 'Oops! Component Draft Not Found.');
        }

        const activeComponent = await Components.findOne({
            component_draft_id: draftComponent._id,
            component_state: {
                $in: [componentState.PUBLISHED, componentState.PRIVATE]
            }
        }).lean();

        if (activeComponent) {
            return ReS(res, constants.bad_request_code, 'Oops! This draft cannot be deleted because it is linked to an active project or element.');
        }
        // Changed component state from publish to private
        await DraftComponents.updateOne({
            _id: draftComponent._id
        }, {
            $set: {
                component_state: componentState.SOFT_DELETED
            }
        });

        if (draftComponent?.component_type === componentType.REPOSITORY && draftComponent?.platform_data?.length) {
            const repositoryIds = draftComponent.platform_data
                .map((platform) => platform?.repository_id)
                .filter(Boolean); // Remove undefined or null IDs

            if (repositoryIds.length) {
                // Update the GitlabRepository by unsetting the component_draft_id
                await GitlabRepository.updateMany(
                    {
                        component_draft_id: draftComponent._id,
                        _id: { $in: repositoryIds }
                    },
                    {
                        $unset: { component_draft_id: 1 }
                    }
                );
            }
        }
        const message = (draftComponent.component_type == componentType.ELEMENTS) ? 'Element draft archived successfully' : 'Project draft archived successfully';
        // Return a success response
        return ReS(res, constants.success_code, message, { _id: draftComponent._id });

    } catch (err) {
        // Log and handle any errors
        logger.error(`Error at Front Controller softDeleteDraftComponent: ${err}`);
        return sendError(res, err);
    }
}

async function fetchComponentTagSuggestions(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id } = req.params;

        const draftProject = await DraftComponents.findOne({
            _id: id,
            created_by_user: req.session._id,
            component_type: {
                $in: [componentType.REPOSITORY, componentType.MOBILE, componentType.ELEMENTS]
            },
        }).lean();

        if (!draftProject) {
            return ReS(res, constants.resource_not_found, 'Oops! Component Draft Not Found.');
        }

        // Check if component type is element then send default tags in suggestions
        if (draftProject && draftProject.component_type == componentType.ELEMENTS) {
            return ReS(res, constants.success_code, 'Component tags fetched successfully', { tags: Object.values(elementDefaultTags) });
        }

        // / Extract repository_id into an array
        const repositoryIds = draftProject?.platform_data.map((item) => item.repository_id);

        // Find the GitLab repository by ID
        const repository = await GitlabRepository.find(
            {
                _id: {
                    $in: repositoryIds
                }
            }, // Query condition
            'project_id' // Fields to return
        );

        // / Extract repository_id into an array
        const projectIds = repository.map((item) => item.project_id);

        // // Fetch commit information from GitLab
        const repositoryData = await fetchProjectLanguagesMultiple(projectIds);

        // Extract unique language keys
        const uniqueLanguages = Array.from(new Set(
            Object.values(repositoryData)
                .flatMap((languages) => Object.keys(languages))
        ));

        // Send the commit information as a JSON response
        return ReS(res, constants.success_code, 'Component tags fetched successfully', { tags: uniqueLanguages });
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in fetchComponentTagSuggestions: ${err}`);
        return sendError(res, err);
    }
}

async function getProjectPriceEstimation(req, res) {
    try {
        const { component_type = componentType.REPOSITORY, fiat_amount } = req.query;
        // Fetch user's price tier from the database
        const priceTier = await getItemPricingTiers(req.session._id, component_type)
        // Fetch configuration values for parity and buyer fees
        const mpnParity = await getParityConfig();
        const buyerFeeConfig = await getBuyerFeeConfig(req.session._id, component_type);

        // Parse fiat amount from request query, default to 0 if invalid
        const fiatAmount = parseFloat(fiat_amount) || 0;

        // Calculate MPN points based on fiat amount and parity
        const mpnPoints = fiatAmount * mpnParity;

        // Calculate buyer fee as a percentage of fiat amount
        const calculatedBuyerFee = (fiatAmount * buyerFeeConfig.percentage) / 100;

        // Ensure buyer fee meets the minimum fixed amount requirement
        const buyerFeeAmount = Math.max(calculatedBuyerFee, buyerFeeConfig.fixed_min);

        // Calculate the maximum allowed amount
        const maxAmountLimit = priceTier.max;

        // Validate if fiat amount exceeds user's price tier limit
        if (fiatAmount > maxAmountLimit) {
            return ReS(res, constants.bad_request_code, 'Provided fiat amount exceeds your max limit');
        }

        // Construct response object
        const responseObj = {
            min: priceTier.min,
            max: priceTier.max,
            user_limit: maxAmountLimit,
            mpn_parity: mpnParity,
            mpn_points: mpnPoints,
            fiat_amount: fiatAmount,
            buyer_fee_percentage: buyerFeeConfig.percentage,
            buyer_fee_amount: buyerFeeAmount
        };

        // Return success response with calculated price estimation
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        // Log and return error response
        logger.error(`Error in getProjectPriceEstimation: ${err}`);
        return sendError(res, err);
    }
}

async function fetchProjectUnlockRates(req, res) {
    try {
        // Set default conditions to find the component
        const conditions = {
            'slug': req.params.slug,
            component_type: {
                $in: [componentType.REPOSITORY, componentType.MOBILE]
            },
            'created_by_user': {
                $exists: true
            }
        };

        const { use_point_balance = false, use_fiat_balance = false } = req.query;

        // Find the component data
        const component = await Components.findOne(conditions).lean();

        // If component data not found, return error
        if (!component) {
            return ReS(res, constants.resource_not_found, 'Oops! Project Not Found.');
        }

        if (component.component_state != componentState.PUBLISHED) {
            return ReS(res, constants.bad_request_code, 'Oops! Project is not published.');
        }

        // If the component is already free, return error
        if (!component.is_paid) {
            return ReS(res, constants.bad_request_code, 'This project is already free and does not require unlocking.');
        }

        if (component.created_by_user.toString() == req.session._id.toString()) {
            return ReS(res, constants.bad_request_code, 'Oops! As the author of this project, you\'re not allowed to unlock it.');
        }

        // Check if the component is already unlocked by the user
        const componentUnlock = await ComponentUnlockHistory.findOne({
            component_id: component._id,
            unlock_by: req.session._id,
            is_active: true
        });

        if (componentUnlock) {
            return ReS(res, constants.bad_request_code, 'You have already unlocked this project.');
        }

        const response = await calculateComponentUnlock(req.session._id, component, use_point_balance, use_fiat_balance);

        // Return success response
        return ReS(res, constants.success_code, 'Success', response);
    } catch (err) {
        // Log and return error response in case of any exception
        logger.error(`Error at Front Controller fetchProjectUnlockRates ${err}`);
        return sendError(res, err);
    }
}

async function fetchLicenseContent(req, res) {
    try {
        const { license_id } = req.params;
        const userId = req.session._id;

        // Fetch user and license concurrently
        const [user, license] = await Promise.all([
            Users.findOne({ _id: userId }, 'first_name last_name').lean(),
            PlatformLicense.findOne({ _id: license_id }, 'title slug category description dynamic_values is_active').lean()
        ]);

        if (!license) {
            return ReS(res, constants.resource_not_found, 'Oops! License Not Found.');
        }

        // Generate dynamic values
        const currentYear = new Date().getFullYear();
        const copyrightHolder = [user?.first_name, user?.last_name].filter(Boolean).join(' ') || 'Unknown';

        // Function to replace placeholders
        const replacePlaceholders = (content = '') =>
            content.replace(/{{year}}/g, currentYear).replace(/{{copyright_holder}}/g, copyrightHolder);

        // Update description
        license.description = replacePlaceholders(license.description);

        return ReS(res, constants.success_code, 'Success', license);
    } catch (err) {
        logger.error(`Error in fetchLicenseContent: ${err.message}`, err);
        return sendError(res, err);
    }
}

async function calculatesMonthlySalesProjections(req, res) {
    try {
        const { target, price, component_type = componentType.REPOSITORY } = req.body;

        const feeRate = await getAuthorFeeConfig(req.session._id, component_type);

        // Convert percent to decimal (e.g., 5 → 0.05)
        const feeDecimal = feeRate / 100;

        const data = []; let totalEarnings = 0;
        const totalSales = Math.ceil(target * price * 12);
        for (let month = 1; month <= 12; month++) {
            const estimatedSales = Math.ceil(target * price * month);
            const mpnFees = Math.ceil(estimatedSales * feeDecimal);
            const earnings = Math.ceil(estimatedSales - mpnFees);
            totalEarnings += earnings; // accumulate monthly earnings
            data.push({
                month,
                estimatedSales,
                mpnFees,
                earnings
            });
        }
        return ReS(res, constants.success_code, 'Success', { target, price, feeRate: feeRate, totalEarnings, totalSales, months: data });
    } catch (err) {
        console.error(`Error in calculatesMonthlySalesProjections: ${err.message}`, err);
        return sendError(res, err);
    }
}

async function checkComponentNameAvailability(req, res) {
    const { title } = req.query;
    const { id } = req.params;

    const slug = generateSlug(title);

    const existingComponent = await Components.findOne({
        slug: slug,
        component_draft_id: {
            $ne: id
        }
    }, "_id").lean();

    // Check if the component exists
    const isAvailable = !existingComponent;

    // Return response
    return ReS(
        res,
        constants.success_code,
        isAvailable ? 'Title is available to take' : 'Title is already taken.',
        { isAvailable }
    );
}

async function initiateUnlockComponentWithFiat(req, res) {
    try {
        const { use_point_balance = true, use_fiat_balance = true } = req.body;
        // Set default conditions to find the component
        const conditions = {
            'slug': req.params.slug,
            component_type: {
                $in: [componentType.REPOSITORY, componentType.MOBILE]
            },
        };

        // Find the component data
        const component = await Components.findOne(conditions).lean();

        // If component data not found, return error
        if (!component) {
            return ReS(res, constants.resource_not_found, 'Oops! Project Not Found.');
        }

        if (component.component_state != componentState.PUBLISHED) {
            return ReS(res, constants.bad_request_code, 'Oops! Project is not published.');
        }

        // If the component is already free, return error
        if (!component.is_paid) {
            return ReS(res, constants.bad_request_code, 'This project is already free and does not require unlocking.');
        }

        if (component.created_by_user.toString() == req.session._id.toString()) {
            return ReS(res, constants.bad_request_code, 'Oops! As the author of this project, you\'re not allowed to unlock it.');
        }

        // Check if the component is already unlocked by the user
        const componentUnlock = await ComponentUnlockHistory.findOne({
            component_id: component._id,
            unlock_by: req.session._id,
            is_active: true
        });

        if (componentUnlock) {
            return ReS(res, constants.bad_request_code, 'You have already unlocked this project.');
        }

        // If a pending transaction exists, prevent creating another one
        const pendingOrder = await PaymentTransactions.findOne({
            component_id: component._id,
            status: transactionStatus.PENDING
        });

        if (pendingOrder) {
            return ReS(res, constants.success_code, 'A payment is already in progress for this item. Please wait until it is completed or resolved.', { order_id: pendingOrder.order_id });
        }

        const { mpn_points_applied, fiat_applied, net_payable_fiat } = await calculateComponentUnlock(req.session._id, component, use_point_balance, use_fiat_balance);

        if (net_payable_fiat == 0) {
            return ReS(res, constants.bad_request_code, 'Payment initiation failed: No fiat amount to deduct.');
        }

        const result = await initiateComponentFiatPurchase(req.session._id, mpn_points_applied, fiat_applied, net_payable_fiat, component);
        // Return success response
        return ReS(res, constants.success_code, 'Success', result);
    } catch (err) {
        // Log and return error response in case of any exception
        logger.error(`Error at Front Controller initiateUnlockComponentWithFiat ${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    getComponentDetail,
    getAllComponents,
    getAllChildComponents,
    getComponentRecommendation,
    increaseComponentViews,
    getAllComponentsForHomePage,
    likeComponent,
    bookMarkComponent,
    getAllBookMarkedComponents,
    removeBookMarkComponent,
    getComponentUserMeta,
    removeComponentLike,
    unlockComponent,
    getAllLikedComponents,
    getComponentPlatformDetail,
    getComponentDetailPublic,
    getComponentSearchSuggestions,
    detachRepositoryFromComponent,
    createComponentPlaceHolder,
    updateRepositoryComponent,
    linkCodeSpacesToComponent,
    getAllComponentList,
    getRepoComponentDetails,
    uploadComponentAssets,
    publishDraftComponent,
    getDraftComponentDetails,
    makePrivatePublishedComponent,
    getElementsRecommendation,
    softDeleteDraftComponent,
    fetchComponentTagSuggestions,
    getProjectPriceEstimation,
    fetchProjectUnlockRates,
    fetchLicenseContent,
    calculatesMonthlySalesProjections,
    checkComponentNameAvailability,
    initiateUnlockComponentWithFiat
};