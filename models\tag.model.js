const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const TagsSchema = new Schema({
    title: {
        type: String,
        required: true
    },
    slug: {
        type: String,
        required: true,
        unique: true
    },
    description: {
        type: String
    },
    project_popularity: {
        type: Number,
        default: 0
    },
    elements_popularity: {
        type: Number,
        default: 0
    },
    codespace_popularity: {
        type: Number,
        default: 0
    },
    mobile_popularity: {
        type: Number,
        default: 0
    },
    is_active: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

TagsSchema.set('autoIndex', true);

const Tags = mongoose.model('tags', TagsSchema);

module.exports = {
    Tags
};