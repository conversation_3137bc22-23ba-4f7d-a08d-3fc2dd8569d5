const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { componentType } = require('../config/component.constant');

const GlobalItemSettingsSchema = new Schema({
    item_type: {
        type: String,
        required: true,
        unique: true,
        enum: [componentType.ELEMENTS, componentType.REPOSITORY, componentType.MOBILE],
        trim: true,
    },
    version: {
        type: Number,
        required: true,
    },
    price: {
        min: {
            type: Number,
            required: true,
            min: 0,
        },
        max: {
            type: Number,
            required: true,
            min: 0,
        }
    },
    fees: {
        author: {
            percentage: {
                type: Number,
                required: true,
                min: 0,
                max: 100
            }
        },
        buyer: {
            percentage: {
                type: Number,
                required: true,
                min: 0,
                max: 100
            },
            fixed_min: {
                type: Number,
                required: true,
                min: 0
            }
        }
    },
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

GlobalItemSettingsSchema.index({ item_type: 1 }, { unique: true });
GlobalItemSettingsSchema.set('autoIndex', true);

const GlobalItemSettings = mongoose.model('global_item_settings', GlobalItemSettingsSchema);

module.exports = {
    GlobalItemSettings
};