// Private Share Controller Functions
const mongoose = require('mongoose');
require('dotenv').config();

// Models
const { Components } = require('../../models/component.model');

// Services
const constants = require('../../config/constants');
const { ReS } = require('../../services/general.helper');
const logger = require('../../config/logger');
const { fetchFullComponentData } = require('../../services/component.service');
const { 
    shareComponentPrivately, 
    acceptPrivateShare, 
    checkPrivateAccess, 
    getMyPrivateShares, 
    getComponentsSharedWithMe, 
    revokePrivateShare,
    getShareStatistics,
    generatePublicShareableLink,
    getMyShareableLinks
} = require('../../services/private_share.service');

async function shareComponentPrivatelyController(req, res) {
    try {
        const componentId = req.params.id;
        const { emails, personal_message, access_duration = 'undefined', duration_days, access_controls = [] } = req.body;
        const userId = req.session._id;

        const result = await shareComponentPrivately(
            componentId,
            userId,
            emails,
            personal_message,
            access_duration,
            duration_days,
            access_controls
        );

        return ReS(res, constants.success_code, 'Component shared successfully', result);
    } catch (error) {
        logger.error(`Error in shareComponentPrivatelyController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function acceptPrivateShareController(req, res) {
    try {
        const { token } = req.params;
        const userId = req.session?._id;
        const userEmail = req.session?.email;

        const result = await acceptPrivateShare(token, userId, userEmail);

        if (result.requiresSignup) {
            return ReS(res, constants.success_code, 'Please sign up to access this component', {
                component: result.component,
                sharedBy: result.sharedBy,
                message: result.message,
                requiresSignup: true,
                signupUrl: `${process.env.FRONTEND_URL || process.env.SITE_URL}/auth/signup?returnUrl=${encodeURIComponent(`private-share/${token}`)}` 
            });
        }

        return ReS(res, constants.success_code, 'Access granted successfully', result);
    } catch (error) {
        logger.error(`Error in acceptPrivateShareController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getMyPrivateSharesController(req, res) {
    try {
        const userId = req.session._id;
        const limit = parseInt(req.query.limit) || 12;
        const skip = parseInt(req.query.skip) || 0;
        const options = {
            limit: limit,
            skip: skip,
            status: req.query.status
        };

        const result = await getMyPrivateShares(userId, options);
        return ReS(res, constants.success_code, 'Data fetched successfully', result);
    } catch (error) {
        logger.error(`Error in getMyPrivateSharesController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getComponentsSharedWithMeController(req, res) {
    try {
        const userId = req.session._id;
        const userEmail = req.session.email;
        const limit = parseInt(req.query.limit) || 12;
        const skip = parseInt(req.query.skip) || 0;
        const options = {
            limit: limit,
            skip: skip,
            status: req.query.status
        };

        const result = await getComponentsSharedWithMe(userId, userEmail, options);
        return ReS(res, constants.success_code, 'Data fetched successfully', result);
    } catch (error) {
        logger.error(`Error in getComponentsSharedWithMeController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function revokePrivateShareController(req, res) {
    try {
        const { shareId } = req.params;
        const userId = req.session._id;

        const result = await revokePrivateShare(shareId, userId);
        return ReS(res, constants.success_code, result.message);
    } catch (error) {
        logger.error(`Error in revokePrivateShareController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getShareStatisticsController(req, res) {
    try {
        const userId = req.session._id;
        const stats = await getShareStatistics(userId);
        return ReS(res, constants.success_code, 'Statistics fetched successfully', stats);
    } catch (error) {
        logger.error(`Error in getShareStatisticsController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getComponentWithPrivateToken(req, res) {
    try {
        const { token } = req.query;
        const userId = req.session?._id;
        const userEmail = req.session?.email;
        const componentSlug = req.params.slug;

        if (!token) {
            return ReS(res, constants.bad_request_code, 'Access token is required');
        }

        // Validate token and get access
        const tokenResult = await acceptPrivateShare(token, userId, userEmail);
        
        if (tokenResult.requiresSignup) {
            return ReS(res, constants.unauthorized_code, 'Authentication required');
        }

        // Find the component
        const component = await Components.findOne({
            slug: componentSlug
        }).lean();

        if (!component) {
            return ReS(res, constants.resource_not_found, 'Component not found');
        }

        // Verify token is for this component
        if (component._id.toString() !== tokenResult.component._id.toString()) {
            return ReS(res, constants.forbidden_code, 'Token is not valid for this component');
        }

        // Fetch full component data
        const componentData = await fetchFullComponentData(componentSlug, component.is_paid, null, userId);
        
        // Add access type to response
        componentData.access_type = 'private_share';
        componentData.shared_by = tokenResult.sharedBy;
        componentData.personal_message = tokenResult.message;
        componentData.access_controls = tokenResult.accessControls;

        return ReS(res, constants.success_code, 'Data fetched successfully', componentData);
    } catch (error) {
        logger.error(`Error in getComponentWithPrivateToken: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function validatePrivateShareToken(req, res) {
    try {
        const { token } = req.params;
        const userId = req.session?._id;
        const userEmail = req.session?.email;

        const result = await acceptPrivateShare(token, userId, userEmail);

        if (result.requiresSignup) {
            return ReS(res, constants.success_code, 'Please sign up to access this component', {
                component: result.component,
                sharedBy: result.sharedBy,
                message: result.message,
                requiresSignup: true
            });
        }

        // Get component creator username for frontend URL
        const component = await Components.findById(result.component._id)
            .populate('created_by_user', 'username')
            .lean();

        return ReS(res, constants.success_code, 'Access granted successfully', {
            component: {
                ...result.component,
                username: component.created_by_user.username
            },
            sharedBy: result.sharedBy,
            message: result.message,
            accessControls: result.accessControls,
            accessType: result.accessType
        });
    } catch (error) {
        logger.error(`Error in validatePrivateShareToken: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function generateShareableLinkController(req, res) {
    try {
        const componentId = req.params.id;
        const { link_name, access_duration = 'undefined', duration_days, access_controls = [] } = req.body;
        const userId = req.session._id;

        const result = await generatePublicShareableLink(
            componentId,
            userId,
            link_name,
            access_duration,
            duration_days,
            access_controls
        );

        return ReS(res, constants.success_code, 'Shareable link generated successfully', result);
    } catch (error) {
        logger.error(`Error in generateShareableLinkController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getMyShareableLinksController(req, res) {
    try {
        const userId = req.session._id;
        const limit = parseInt(req.query.limit) || 12;
        const skip = parseInt(req.query.skip) || 0;
        const options = {
            limit: limit,
            skip: skip
        };

        const result = await getMyShareableLinks(userId, options);
        return ReS(res, constants.success_code, 'Data fetched successfully', result);
    } catch (error) {
        logger.error(`Error in getMyShareableLinksController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

module.exports = {
    shareComponentPrivatelyController,
    acceptPrivateShareController,
    getMyPrivateSharesController,
    getComponentsSharedWithMeController,
    revokePrivateShareController,
    getShareStatisticsController,
    validatePrivateShareToken,
    getComponentWithPrivateToken,
    generateShareableLinkController,
    getMyShareableLinksController
};