// Private Share Controller Functions
require('dotenv').config();

// Models
const { Components } = require('../../models/component.model');
const { ComponentUnlockHistory } = require('../../models/component_unlock_history.model');

// Services
const constants = require('../../config/constants');
const { ReS, escapeRegex } = require('../../services/general.helper');
const logger = require('../../config/logger');
const { fetchFullComponentData } = require('../../services/component.service');
const { paymentFilterTypes, filterTypes } = require('../../config/component.constant');
const {
    shareComponentPrivately,
    acceptPrivateShare,
    getMyPrivateShares,
    getComponentsSharedWithMe,
    revokePrivateShare,
    getShareStatistics,
    generatePublicShareableLink,
    getMyShareableLinks,
    refreshShareableLink,
    bulkRevokeShares,
    getShareAnalytics,
    getSharedWithMeStatistics,
    deleteShareLink
} = require('../../services/private_share.service');

async function shareComponentPrivatelyController(req, res) {
    try {
        const componentId = req.params.id;
        const { emails, personal_message, access_duration = 'undefined', duration_days, access_controls = [] } = req.body;
        const userId = req.session._id;

        const result = await shareComponentPrivately(
            componentId,
            userId,
            emails,
            personal_message,
            access_duration,
            duration_days,
            access_controls
        );

        return ReS(res, constants.success_code, 'Component shared successfully', result);
    } catch (error) {
        logger.error(`Error in shareComponentPrivatelyController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}



async function getMyPrivateSharesController(req, res) {
    try {
        const userId = req.session._id;
        const limit = parseInt(req.body.limit) || 12;
        const skip = parseInt(req.body.skip) || 0;

        const options = {
            limit: limit,
            skip: skip,
            status: req.body.status,
            searchText: req.body.searchText,
            sort_by: req.body.sort_by
        };

        const result = await getMyPrivateShares(userId, options);

        return ReS(res, constants.success_code, 'Data fetched successfully', result);
    } catch (error) {
        logger.error(`Error in getMyPrivateSharesController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getComponentsSharedWithMeController(req, res) {
    try {
        const userId = req.session._id;
        const userEmail = req.session.email;

        // Debug logging
        logger.info(`getComponentsSharedWithMe DEBUG:`, {
            userId: userId,
            userEmail: userEmail,
            sessionData: {
                hasSession: !!req.session,
                sessionKeys: req.session ? Object.keys(req.session) : [],
                sessionId: req.session?._id,
                sessionEmail: req.session?.email
            },
            requestBody: req.body
        });

        if (!userId || !userEmail) {
            logger.error(`Missing user authentication data:`, { userId, userEmail });
            return ReS(res, constants.unauthorized_code, 'User authentication required');
        }

        const limit = parseInt(req.body.limit) || 12;
        const skip = parseInt(req.body.skip) || 0;

        const options = {
            limit: limit,
            skip: skip,
            status: req.body.status,
            searchText: req.body.searchText,
            sort_by: req.body.sort_by // For shared-with-me, this should be payment filter types for sorting
        };

        // Validate sort_by against payment filter types (for shared-with-me sorting)
        if (options.sort_by && !Object.values(paymentFilterTypes).includes(options.sort_by)) {
            return ReS(res, constants.bad_request_code, `Invalid sort_by. Must be one of: ${Object.values(paymentFilterTypes).join(', ')}`);
        }

        logger.info(`Calling getComponentsSharedWithMe service with:`, {
            userId: userId.toString(),
            userEmail,
            options
        });

        const result = await getComponentsSharedWithMe(userId, userEmail, options);

        logger.info(`getComponentsSharedWithMe result:`, {
            recordsTotal: result.recordsTotal,
            recordsFiltered: result.recordsFiltered,
            listLength: result.list ? result.list.length : 0,
            firstShareId: result.list && result.list.length > 0 ? result.list[0]._id : 'none'
        });

        // Return standard codebase response structure: {recordsTotal, recordsFiltered, list}
        return ReS(res, constants.success_code, 'Data fetched successfully', result);
    } catch (error) {
        logger.error(`Error in getComponentsSharedWithMeController: ${error.message}`, {
            error: error.stack,
            userId: req.session._id,
            userEmail: req.session.email,
            body: req.body
        });
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function revokePrivateShareController(req, res) {
    try {
        const { shareId } = req.params;
        const userId = req.session._id;

        const result = await revokePrivateShare(shareId, userId);
        return ReS(res, constants.success_code, result.message);
    } catch (error) {
        logger.error(`Error in revokePrivateShareController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getShareStatisticsController(req, res) {
    try {
        const userId = req.session._id;
        const stats = await getShareStatistics(userId);
        return ReS(res, constants.success_code, 'Statistics fetched successfully', stats);
    } catch (error) {
        logger.error(`Error in getShareStatisticsController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getComponentWithPrivateToken(req, res) {
    try {
        const { token } = req.query;
        const userId = req.session?._id;
        const userEmail = req.session?.email;
        const componentSlug = req.params.slug;

        if (!token) {
            return ReS(res, constants.bad_request_code, 'Access token is required');
        }

        // Validate token and get access
        const tokenResult = await acceptPrivateShare(token, userId, userEmail);

        // Handle authentication requirements
        if (tokenResult.requiresSignup) {
            return ReS(res, constants.unauthorized_code, 'Please sign up to access this component', {
                authRequired: true,
                requiresSignup: true,
                requiresLogin: false,
                accessType: tokenResult.accessType,
                redirectUrl: `/signup?returnUrl=${encodeURIComponent(`component/private/${componentSlug}?token=${token}`)}`
            });
        }

        if (tokenResult.requiresLogin) {
            return ReS(res, constants.unauthorized_code, 'Please login to access this component', {
                authRequired: true,
                requiresSignup: false,
                requiresLogin: true,
                accessType: tokenResult.accessType,
                redirectUrl: `/login?returnUrl=${encodeURIComponent(`component/private/${componentSlug}?token=${token}`)}`
            });
        }

        // Find the component
        const component = await Components.findOne({
            slug: componentSlug
        }).lean();

        if (!component) {
            return ReS(res, constants.resource_not_found, 'Component not found');
        }

        // Verify token is for this component
        if (component._id.toString() !== tokenResult.component._id.toString()) {
            return ReS(res, constants.forbidden_code, 'Token is not valid for this component');
        }

        // Check if component is paid and if user has unlocked it
        let unlockHistory = null;
        let requiresPayment = false;

        if (component.is_paid && userId) {
            unlockHistory = await ComponentUnlockHistory.findOne({
                component_id: component._id,
                unlock_by: userId,
                is_active: true
            }).lean();

            requiresPayment = !unlockHistory;
        } else if (component.is_paid && !userId) {
            // For paid components, require payment if user is not logged in
            requiresPayment = true;
        }

        // Fetch full component data
        const componentData = await fetchFullComponentData(componentSlug, component.is_paid, unlockHistory, userId);

        // Determine access type based on share type and payment status
        let accessType = 'private_share';
        if (tokenResult.accessType === 'public_link') {
            accessType = requiresPayment ? 'public_link_requires_payment' : 'public_link';
        } else if (tokenResult.accessType === 'private_invite') {
            accessType = requiresPayment ? 'private_invite_requires_payment' : 'private_invite';
        }

        // Add access type and payment info to response
        componentData.access_type = accessType;
        componentData.shared_by = tokenResult.sharedBy;
        componentData.personal_message = tokenResult.message;
        componentData.access_controls = tokenResult.accessControls;
        componentData.requires_payment = requiresPayment;
        componentData.share_type = tokenResult.accessType; // 'public_link' or 'private_invite'

        if (requiresPayment) {
            componentData.payment_info = {
                purchase_price: component.purchase_price,
                item_price: component.item_price,
                buyer_fee: component.buyer_fee
            };
        }

        return ReS(res, constants.success_code, 'Data fetched successfully', componentData);
    } catch (error) {
        logger.error(`Error in getComponentWithPrivateToken: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function validatePrivateShareToken(req, res) {
    try {
        const { token } = req.params;

        if (!token) {
            return ReS(res, constants.bad_request_code, 'Access token is required');
        }

        // Initialize session if it doesn't exist (for incognito/new users)
        if (!req.session) {
            req.session = {};
        }

        const userId = req.session._id || null;
        const userEmail = req.session.email || null;

        logger.info(`ValidatePrivateShareToken called:`, {
            token: token.substring(0, 8) + '...',
            hasUserId: !!userId,
            hasUserEmail: !!userEmail,
            hasSession: !!req.session
        });

        const result = await acceptPrivateShare(token, userId, userEmail);

        // Handle authentication requirements
        if (result.requiresSignup) {
            // Store access token in session for link tracking after signup
            if (result.accessType === 'public_link') {
                // Ensure session exists before setting properties
                if (!req.session) {
                    req.session = {};
                }
                req.session.pendingShareToken = token;
            }

            return ReS(res, constants.success_code, 'Please sign up to access this component', {
                component: result.component,
                sharedBy: result.sharedBy,
                message: result.message,
                accessType: result.accessType,
                requiresSignup: true,
                requiresLogin: false,
                authRequired: true,
                redirectUrl: `/signup?returnUrl=${encodeURIComponent(`private-share/${token}`)}`
            });
        }

        if (result.requiresLogin) {
            // Store access token in session for link tracking after login
            if (result.accessType === 'public_link') {
                // Ensure session exists before setting properties
                if (!req.session) {
                    req.session = {};
                }
                req.session.pendingShareToken = token;
            }

            return ReS(res, constants.success_code, 'Please login to access this component', {
                component: result.component,
                sharedBy: result.sharedBy,
                message: result.message,
                accessType: result.accessType,
                requiresSignup: false,
                requiresLogin: true,
                authRequired: true,
                redirectUrl: `/login?returnUrl=${encodeURIComponent(`private-share/${token}`)}`
            });
        }

        // Get component creator username for frontend URL
        const component = await Components.findById(result.component._id)
            .populate('created_by_user', 'username')
            .lean();

        logger.info(`Access granted for token ${token.substring(0, 8)}... to user ${userId || 'anonymous'}`);

        return ReS(res, constants.success_code, 'Access granted successfully', {
            component: {
                ...result.component,
                username: component?.created_by_user?.username || 'unknown'
            },
            sharedBy: result.sharedBy,
            message: result.message,
            accessControls: result.accessControls,
            accessType: result.accessType,
            accessToken: result.accessToken, // Include for frontend tracking
            requiresSignup: false,
            requiresLogin: false,
            authRequired: false
        });
    } catch (error) {
        logger.error(`Error in validatePrivateShareToken: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function generateShareableLinkController(req, res) {
    try {
        const componentId = req.params.id;
        const { link_name, access_duration = 'undefined', duration_days, access_controls = [] } = req.body;
        const userId = req.session._id;

        const result = await generatePublicShareableLink(
            componentId,
            userId,
            link_name,
            access_duration,
            duration_days,
            access_controls
        );

        return ReS(res, constants.success_code, 'Shareable link generated successfully', result);
    } catch (error) {
        logger.error(`Error in generateShareableLinkController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getMyShareableLinksController(req, res) {
    try {
        const userId = req.session._id;
        const limit = parseInt(req.body.limit) || 12;
        const skip = parseInt(req.body.skip) || 0;

        const options = {
            limit: limit,
            skip: skip,
            searchText: req.body.searchText,
            sort_by: req.body.sort_by
        };

        const result = await getMyShareableLinks(userId, options);

        return ReS(res, constants.success_code, 'Data fetched successfully', result);
    } catch (error) {
        logger.error(`Error in getMyShareableLinksController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}



async function bulkRevokeSharesController(req, res) {
    try {
        const { shareIds } = req.body;
        const userId = req.session._id;

        const result = await bulkRevokeShares(shareIds, userId);
        return ReS(res, constants.success_code, result.message, result);
    } catch (error) {
        logger.error(`Error in bulkRevokeSharesController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getShareAnalyticsController(req, res) {
    try {
        const { shareId } = req.params;
        const userId = req.session._id;

        const result = await getShareAnalytics(shareId, userId);
        return ReS(res, constants.success_code, 'Analytics fetched successfully', result);
    } catch (error) {
        logger.error(`Error in getShareAnalyticsController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getSharedWithMeStatisticsController(req, res) {
    try {
        const userId = req.session._id;
        const userEmail = req.session.email;

        // Debug logging
        logger.info(`getSharedWithMeStatistics DEBUG:`, {
            userId: userId,
            userEmail: userEmail,
            sessionData: {
                hasSession: !!req.session,
                sessionKeys: req.session ? Object.keys(req.session) : [],
                sessionId: req.session?._id,
                sessionEmail: req.session?.email
            }
        });

        if (!userId || !userEmail) {
            logger.error(`Missing user authentication data in statistics:`, { userId, userEmail });
            return ReS(res, constants.unauthorized_code, 'User authentication required');
        }

        const result = await getSharedWithMeStatistics(userId, userEmail);

        logger.info(`getSharedWithMeStatistics result:`, result);

        return ReS(res, constants.success_code, 'Statistics fetched successfully', result);
    } catch (error) {
        logger.error(`Error in getSharedWithMeStatisticsController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

/**
 * Get full component details with private token validation
 * This is equivalent to /component/get/:slug but for private shares
 */
async function getPrivateComponentDetailsController(req, res) {
    try {
        const { token } = req.query;
        const userId = req.session?._id;
        const userEmail = req.session?.email;
        const componentSlug = req.params.slug;

        if (!token) {
            return ReS(res, constants.bad_request_code, 'Access token is required');
        }

        logger.info(`getPrivateComponentDetails called:`, {
            componentSlug,
            token: token.substring(0, 8) + '...',
            userId,
            userEmail
        });

        // Validate the token first
        const tokenResult = await checkPrivateAccess(null, userId, userEmail, token);

        if (!tokenResult.hasAccess) {
            return ReS(res, constants.forbidden_code, 'Invalid or expired access token');
        }

        if (tokenResult.requiresSignup) {
            return ReS(res, constants.unauthorized_code, 'Please sign up to access this component', {
                authRequired: true,
                requiresSignup: true,
                requiresLogin: false,
                accessType: tokenResult.accessType,
                redirectUrl: `/signup?returnUrl=${encodeURIComponent(`component/private/${componentSlug}?token=${token}`)}`
            });
        }

        if (tokenResult.requiresLogin) {
            return ReS(res, constants.unauthorized_code, 'Please login to access this component', {
                authRequired: true,
                requiresSignup: false,
                requiresLogin: true,
                accessType: tokenResult.accessType,
                redirectUrl: `/login?returnUrl=${encodeURIComponent(`component/private/${componentSlug}?token=${token}`)}`
            });
        }

        // Find the component
        const component = await Components.findOne({
            slug: componentSlug
        }).lean();

        if (!component) {
            return ReS(res, constants.resource_not_found, 'Component not found');
        }

        // Verify token is for this component
        if (component._id.toString() !== tokenResult.component._id.toString()) {
            return ReS(res, constants.forbidden_code, 'Token is not valid for this component');
        }

        // Check if user has unlocked this component (for paid components)
        const unlockHistory = userId ? await ComponentUnlockHistory.findOne({
            component_id: component._id,
            unlock_by: userId,
            is_active: true
        }).lean() : null;

        // Determine if payment is required
        const requiresPayment = component.is_paid && !unlockHistory &&
                               component.created_by_user?.toString() !== userId?.toString();

        // Fetch full component data using the same service as regular component access
        const componentData = await fetchFullComponentData(componentSlug, component.is_paid, unlockHistory, userId);

        // Add private share specific metadata
        componentData.access_type = tokenResult.accessType;
        componentData.shared_by = tokenResult.sharedBy;
        componentData.access_controls = tokenResult.accessControls || [];
        componentData.requires_payment = requiresPayment;
        componentData.is_private_share = true;

        logger.info(`Private component details fetched successfully for ${componentSlug}`);

        return ReS(res, constants.success_code, 'Component details fetched successfully', componentData);

    } catch (error) {
        logger.error(`❌ Error in getPrivateComponentDetailsController: ${error.message}`, {
            error: error.stack,
            componentSlug: req.params.slug,
            token: req.query.token?.substring(0, 8) + '...',
            userId: req.session?._id
        });
        return ReS(res, constants.bad_request_code, error.message);
    }
}

/**
 * Delete a shareable link permanently
 */
async function deleteShareLinkController(req, res) {
    try {
        const { shareId } = req.params;
        const userId = req.session._id;

        logger.info(`🗑️ deleteShareLink called:`, { shareId, userId });

        const result = await deleteShareLink(shareId, userId);

        return ReS(res, constants.success_code, 'Shareable link deleted successfully', result);
    } catch (error) {
        logger.error(`Error in deleteShareLinkController: ${error.message}`, {
            error: error.stack,
            shareId: req.params.shareId,
            userId: req.session._id
        });
        return ReS(res, constants.bad_request_code, error.message);
    }
}

/**
 * Refresh a shareable link (generate new token)
 */
async function refreshShareableLinkController(req, res) {
    try {
        const { shareId } = req.params;
        const userId = req.session._id;

        logger.info(`refreshShareableLink called:`, { shareId, userId });

        const result = await refreshShareableLink(shareId, userId);

        return ReS(res, constants.success_code, 'Shareable link refreshed successfully', result);
    } catch (error) {
        logger.error(`Error in refreshShareableLinkController: ${error.message}`, {
            error: error.stack,
            shareId: req.params.shareId,
            userId: req.session._id
        });
        return ReS(res, constants.bad_request_code, error.message);
    }
}

module.exports = {
    shareComponentPrivatelyController,
    getMyPrivateSharesController,
    getComponentsSharedWithMeController,
    revokePrivateShareController,
    getShareStatisticsController,
    validatePrivateShareToken,
    getComponentWithPrivateToken,
    generateShareableLinkController,
    getMyShareableLinksController,
    refreshShareableLinkController,
    bulkRevokeSharesController,
    getShareAnalyticsController,
    getSharedWithMeStatisticsController,
    deleteShareLinkController,
    getPrivateComponentDetailsController
};