<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shared with Me - Private Share Test</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>📥 Shared with Me</h1>
            <p>Components that have been shared with you</p>
        </header>

        <div class="auth-status" id="authStatus">
            <span id="userInfo">Not logged in</span>
            <button id="logoutBtn" style="display: none;" onclick="logout()">Logout</button>
        </div>

        <div class="card">
            <div class="filters">
                <h3>🔍 Filters</h3>
                <div class="input-row">
                    <div class="input-group">
                        <label for="statusFilter">Status:</label>
                        <select id="statusFilter" onchange="loadSharedComponents()">
                            <option value="">All</option>
                            <option value="accepted">Accepted</option>
                            <option value="pending">Pending</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="searchText">Search:</label>
                        <input type="text" id="searchText" placeholder="Search components..." onkeyup="debounceSearch()">
                    </div>
                    <div class="input-group">
                        <button onclick="loadSharedComponents()" class="secondary">🔄 Refresh</button>
                    </div>
                </div>
            </div>

            <div class="statistics" id="statisticsContainer">
                <h3>📊 Statistics</h3>
                <div id="statsContent">Loading...</div>
            </div>

            <div class="components-section">
                <h3>📋 Shared Components</h3>
                <div id="loadingIndicator" style="text-align: center; padding: 20px;">
                    <div class="loading"></div>
                    <p>Loading shared components...</p>
                </div>
                <div id="componentsContainer" style="display: none;">
                    <div class="component-grid" id="componentGrid"></div>
                    <div class="pagination" id="paginationContainer"></div>
                </div>
                <div id="emptyState" style="display: none; text-align: center; padding: 40px;">
                    <p>No shared components found.</p>
                    <button onclick="window.location.href='index.html'" class="secondary">🏠 Go Home</button>
                </div>
            </div>
        </div>

        <div class="navigation">
            <div class="button-group">
                <button onclick="window.location.href='index.html'">🏠 Home</button>
                <button onclick="window.location.href='manage-shares.html'">📤 Manage My Shares</button>
            </div>
        </div>

        <div class="logs">
            <h3>📋 Logs</h3>
            <div id="logContainer"></div>
            <button onclick="clearLogs()">🗑️ Clear Logs</button>
        </div>
    </div>

    <script src="config.js"></script>
    <script>
        let currentPage = 1;
        let totalPages = 1;
        let searchTimeout = null;

        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            checkAuthStatus();
            
            if (!isAuthenticated()) {
                showLoginRequired();
                return;
            }
            
            loadStatistics();
            loadSharedComponents();
        });

        function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');
            
            if (token && userInfo) {
                const user = JSON.parse(userInfo);
                document.getElementById('userInfo').textContent = `Logged in as: ${user.first_name} ${user.last_name}`;
                document.getElementById('logoutBtn').style.display = 'inline-block';
            } else {
                document.getElementById('userInfo').textContent = 'Not logged in';
                document.getElementById('logoutBtn').style.display = 'none';
            }
        }

        function logout() {
            clearUserSession();
            checkAuthStatus();
            showLoginRequired();
        }

        function showLoginRequired() {
            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('componentsContainer').style.display = 'none';
            document.getElementById('emptyState').innerHTML = `
                <h3>🔑 Login Required</h3>
                <p>Please login to view components shared with you.</p>
                <div class="button-group">
                    <button onclick="window.location.href='login.html'" class="success">🔑 Login</button>
                    <button onclick="window.location.href='signup.html'" class="secondary">📝 Sign Up</button>
                </div>
            `;
            document.getElementById('emptyState').style.display = 'block';
        }

        async function loadStatistics() {
            if (!isAuthenticated()) return;

            try {
                const { response, data } = await apiCall('/v1/component/shared-with-me/statistics', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                    }
                });

                if (data.status === 200) {
                    const stats = data.data;
                    document.getElementById('statsContent').innerHTML = `
                        <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                            <div class="stat-item" style="text-align: center; padding: 15px; background: #f7fafc; border-radius: 8px;">
                                <div style="font-size: 2rem; font-weight: bold; color: #667eea;">${stats.total}</div>
                                <div style="color: #718096;">Total Shared</div>
                            </div>
                            <div class="stat-item" style="text-align: center; padding: 15px; background: #f0fff4; border-radius: 8px;">
                                <div style="font-size: 2rem; font-weight: bold; color: #48bb78;">${stats.free}</div>
                                <div style="color: #718096;">Free Access</div>
                            </div>
                            <div class="stat-item" style="text-align: center; padding: 15px; background: #fef5e7; border-radius: 8px;">
                                <div style="font-size: 2rem; font-weight: bold; color: #ed8936;">${stats.unlocked}</div>
                                <div style="color: #718096;">Unlocked</div>
                            </div>
                            <div class="stat-item" style="text-align: center; padding: 15px; background: #fed7d7; border-radius: 8px;">
                                <div style="font-size: 2rem; font-weight: bold; color: #f56565;">${stats.locked}</div>
                                <div style="color: #718096;">Requires Payment</div>
                            </div>
                        </div>
                    `;
                } else {
                    document.getElementById('statsContent').innerHTML = '<p>Failed to load statistics</p>';
                }
            } catch (error) {
                handleError(error, 'loading statistics');
                document.getElementById('statsContent').innerHTML = '<p>Error loading statistics</p>';
            }
        }

        async function loadSharedComponents(page = 1) {
            if (!isAuthenticated()) {
                showLoginRequired();
                return;
            }

            const status = document.getElementById('statusFilter').value;
            const searchText = document.getElementById('searchText').value;
            
            // Show loading
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('componentsContainer').style.display = 'none';
            document.getElementById('emptyState').style.display = 'none';

            try {
                let endpoint = `/v1/component/shared-with-me?page=${page}&limit=12`;
                
                if (status) {
                    endpoint += `&status=${status}`;
                }
                
                if (searchText) {
                    endpoint += `&search_text=${encodeURIComponent(searchText)}`;
                }

                logMessage(`Loading shared components: ${endpoint}`, 'info');

                const { response, data } = await apiCall(endpoint, {
                    method: 'GET'
                });

                document.getElementById('loadingIndicator').style.display = 'none';

                if (data.status === 200) {
                    const components = data.data.shares || [];
                    currentPage = page;
                    totalPages = Math.ceil((data.data.recordsFiltered || 0) / 12);

                    if (components.length === 0) {
                        document.getElementById('emptyState').style.display = 'block';
                        return;
                    }

                    renderComponents(components);
                    renderPagination();
                    document.getElementById('componentsContainer').style.display = 'block';
                } else {
                    logMessage(data.message || 'Failed to load shared components', 'error');
                    document.getElementById('emptyState').style.display = 'block';
                }
            } catch (error) {
                document.getElementById('loadingIndicator').style.display = 'none';
                handleError(error, 'loading shared components');
                document.getElementById('emptyState').style.display = 'block';
            }
        }

        function renderComponents(components) {
            const grid = document.getElementById('componentGrid');
            grid.innerHTML = '';

            components.forEach(share => {
                const component = share.component;
                if (!component) return;

                const card = document.createElement('div');
                card.className = 'component-card';
                
                // Determine access type and status
                let accessTypeHtml = '';
                if (share.access_type === 'by_link' && !share.shared_with_user && !share.shared_with_email) {
                    accessTypeHtml = '<div class="access-type public-link">🔗 Public Link</div>';
                } else {
                    accessTypeHtml = '<div class="access-type private-invite">📧 Private Invite</div>';
                }

                let statusBadge = '';
                if (share.status === 'accepted') {
                    statusBadge = '<span class="status-badge accepted">Accepted</span>';
                } else if (share.status === 'pending') {
                    statusBadge = '<span class="status-badge pending">Pending</span>';
                }

                card.innerHTML = `
                    ${accessTypeHtml}
                    <h3>${component.title}</h3>
                    <p>${component.description || 'No description available'}</p>
                    <div class="component-meta">
                        <span>Shared: ${formatDate(share.created_at)}</span>
                        ${statusBadge}
                    </div>
                    ${share.shared_by ? `<p><strong>Shared by:</strong> ${share.shared_by.first_name} ${share.shared_by.last_name}</p>` : ''}
                    ${share.personal_message ? `<p><em>"${share.personal_message}"</em></p>` : ''}
                    <div class="button-group">
                        <button onclick="accessComponent('${component.slug}', '${share.access_token}')" class="success">👁️ View</button>
                        ${component.is_paid ? '<button class="secondary">💳 Paid</button>' : '<button class="secondary">🆓 Free</button>'}
                    </div>
                `;

                grid.appendChild(card);
            });
        }

        function renderPagination() {
            const container = document.getElementById('paginationContainer');
            if (totalPages <= 1) {
                container.innerHTML = '';
                return;
            }

            let paginationHtml = '<div class="pagination-controls" style="text-align: center; margin-top: 20px;">';
            
            // Previous button
            if (currentPage > 1) {
                paginationHtml += `<button onclick="loadSharedComponents(${currentPage - 1})" class="secondary">← Previous</button>`;
            }
            
            // Page info
            paginationHtml += `<span style="margin: 0 15px;">Page ${currentPage} of ${totalPages}</span>`;
            
            // Next button
            if (currentPage < totalPages) {
                paginationHtml += `<button onclick="loadSharedComponents(${currentPage + 1})" class="secondary">Next →</button>`;
            }
            
            paginationHtml += '</div>';
            container.innerHTML = paginationHtml;
        }

        function accessComponent(slug, token) {
            window.location.href = `component-view.html?slug=${slug}&token=${token}`;
        }

        function debounceSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                loadSharedComponents(1);
            }, 500);
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }
    </script>
</body>
</html>
