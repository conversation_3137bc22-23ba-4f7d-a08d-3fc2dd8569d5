// Simple HTTP server for testing the frontend
const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 8080;

// MIME types for different file extensions
const mimeTypes = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2',
    '.ttf': 'font/ttf',
    '.eot': 'application/vnd.ms-fontobject'
};

const server = http.createServer((req, res) => {
    // Parse the URL
    const parsedUrl = url.parse(req.url, true);
    let pathname = parsedUrl.pathname;

    console.log(`\n🌐 Request: ${req.method} ${pathname}`);
    console.log(`📁 Original URL: ${req.url}`);

    // Handle root path
    if (pathname === '/') {
        pathname = '/index.html';
    }

    // Handle common browser requests that we don't need to serve
    const ignorePaths = [
        '/_static/',
        '/.well-known/',
        '/favicon.ico',
        '/robots.txt',
        '/sitemap.xml'
    ];

    if (ignorePaths.some(ignorePath => pathname.startsWith(ignorePath))) {
        res.writeHead(204); // No Content
        res.end();
        return;
    }

    // Handle private-share URLs (simulate frontend routing)
    if (pathname.startsWith('/private-share/')) {
        // Check if this is a request for a static file within private-share path
        const fileName = pathname.split('/').pop();

        // If it's a static file (has extension), serve it directly
        if (fileName.includes('.')) {
            // Remove the /private-share/ prefix and serve the actual file
            pathname = '/' + fileName;
        } else {
            // It's a private share token URL, serve the HTML page
            const simpleFile = path.join(__dirname, 'private-share-simple.html');
            if (fs.existsSync(simpleFile)) {
                pathname = '/private-share-simple.html';
            } else {
                pathname = '/private-share.html';
            }
        }
    }
    // Construct file path
    const filePath = path.join(__dirname, pathname);

    console.log(`📂 Mapped to pathname: ${pathname}`);
    console.log(`📄 Serving file: ${filePath}`);
    
    // Get file extension
    const ext = path.extname(filePath).toLowerCase();
    const contentType = mimeTypes[ext] || 'application/octet-stream';
    
    // Check if file exists
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            console.log(`File not found: ${filePath}`);
            // File not found
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>404 - Not Found</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                        h1 { color: #e74c3c; }
                        a { color: #3498db; text-decoration: none; }
                    </style>
                </head>
                <body>
                    <h1>404 - Page Not Found</h1>
                    <p>The requested page <code>${pathname}</code> was not found.</p>
                    <p>File path: <code>${filePath}</code></p>
                    <p><a href="/">← Go back to home</a></p>
                </body>
                </html>
            `);
            return;
        }
        
        // Read and serve the file
        fs.readFile(filePath, (err, data) => {
            if (err) {
                res.writeHead(500, { 'Content-Type': 'text/html' });
                res.end(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>500 - Server Error</title>
                        <style>
                            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                            h1 { color: #e74c3c; }
                            a { color: #3498db; text-decoration: none; }
                        </style>
                    </head>
                    <body>
                        <h1>500 - Internal Server Error</h1>
                        <p>Sorry, there was an error reading the file.</p>
                        <p><a href="/">← Go back to home</a></p>
                    </body>
                    </html>
                `);
                return;
            }
            
            // Set CORS headers for API calls
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
            
            // Serve the file
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(data);
        });
    });
});

server.listen(PORT, () => {
    console.log(`🚀 Frontend test server running at http://localhost:${PORT}`);
    console.log(`📁 Serving files from: ${__dirname}`);
    console.log(`🔗 Open http://localhost:${PORT} in your browser to start testing`);
    console.log(`\n📋 Available test pages:`);
    console.log(`   • http://localhost:${PORT}/index.html - Main dashboard`);
    console.log(`   • http://localhost:${PORT}/login.html - Login page`);
    console.log(`   • http://localhost:${PORT}/signup.html - Signup page`);
    console.log(`   • http://localhost:${PORT}/shared-with-me.html - Shared components`);
    console.log(`   • http://localhost:${PORT}/share-component.html - Share components`);
    console.log(`   • http://localhost:${PORT}/private-share/[token] - Access shared component`);
    console.log(`\n⚙️  Don't forget to update the API URL in the frontend to point to your backend!`);
});

// Handle server shutdown gracefully
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down server...');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});
