const express = require('express');
const router = express.Router();

const { createPlatformValidation, updatePlatformValidation, updatePlatformStatusValidation } = require('../../middlewares/validations/cms/supported_platforms/platformValidation');
const { createPlatform, getAllPlatformSortList, getAllPlatforms, getPlatformDetails, updatePlatform, updatePlatformStatus } = require('../../controller/cms/supportedPlatforms.controller');


router.post('/create', createPlatformValidation, createPlatform);
router.put('/update/:id', updatePlatformValidation, updatePlatform);
router.put('/update/status/:id', updatePlatformStatusValidation, updatePlatformStatus);
router.get('/sort-list', getAllPlatformSortList);
router.post('/list', getAllPlatforms);
router.get('/details/:id', getPlatformDetails);

module.exports = router;