const unlockTypes = {
    FOREVER: 'forever',
    LIMITED: 'limited'
};

const coinTypes = {
    NORMAL: 'normal',
    BONUS: 'bonus'
};

const fiatActivity = {
    EXPENSE: 'expense',
    EARNING: 'earning',
    WITHDRAWAL: 'withdrawal'
};

const pointActivity = {
    EXPENSE: 'expense',
    EARNING: 'earning',
    EXPIRED: 'expired'
};

const mpcBonusRequestStatus = {
    PENDING: 'pending',
    ACCEPTED: 'accepted',
    REJECTED: 'rejected'
};

const userActions = {
    SIGNUP: 'signup',
    LOGIN: 'login',
    UPDATE_EMAIL: 'update_email'
};

const filterTypes = {
    MOST_POPULAR: 'most-popular',
    MOST_LIKED: 'most-liked',
    MOST_VIEWED: 'most-viewed',
    RECENT_ADDITIONS: 'recent-additions',
    PREMIUM: 'premium',
    FEATURED: 'featured',
    RECENT_UPDATES: 'recent-updates'
};

const componentOrientation = {
    DEFAULT: 'default',
    LANDSCAPE: 'landscape',
    PORTRAIT: 'portrait'
};

const componentUserAction = {
    ADD_COMPONENT: 'add_component',
    CHANGE_STATUS: 'change_status',
    EDIT_COMPONENT: 'edit_component',
    PUBLISH_COMPONENT: 'publish_component'
};

const componentType = {
    MANUAL: 'manual',
    REPOSITORY: 'repository',
    ELEMENTS: 'elements',
    CODESPACE: 'codespace',
    MOBILE: 'mobile',
};

const componentState = {
    PLACEHOLDER: 'placeholder',
    ACTIVE_DRAFT: 'active_draft',
    PUBLISHED: 'published',
    SOFT_DELETED: 'soft_deleted',
    PRIVATE: 'private'
};

const componentCurrency = {
    USD: 'USD',
    EUR: 'EUR'
};

const notificationTypes = {
    ELEMENT_PUBLISH: 'element_publish',
    NEW_FOLLOWER: 'new_follower',
    PROJECT_PUBLISH: 'project_publish',
};

const reportTypes = {
    MISLEADING_OR_INAPPROPRIATE_CONTENT: 'misleading_or_inappropriate_content',
    COPYRIGHT_VIOLATION: 'copyright_violation',
    SPAM_OR_MALICIOUS_CONTENT: 'spam_or_malicious_content',
    OTHER: 'other'
};

const reportStatus = {
    PENDING: 'pending',
    REVIEWED: 'reviewed',
    RESOLVED: 'resolved'
};

const elementFileNames = {
    CSS: 'style.css',
    JAVASCRIPT: 'custom.js',
    HTML: 'index.html'
};

const publishReference = {
    ORIGINAL_CREATOR: 'original_creator',
    SHARED: 'shared',
    MODIFIED: 'modified'
};

const licenseType = {
    PAID: 'paid',
    FREE: 'free'
};

const pointActivityDescription = {
    SIGNUP_BONUS: 'Signup bonus credited',
    UNLOCK_EXPENSE: 'Points deducted for unlocking the {{component_type}}.',
    UNLOCK_EARNING: 'Points added for unlocking the {{component_type}}.',
    BONUS_EXPIRED: 'Bonus points expired'
};

const collectionState = {
    ACTIVE_DRAFT: 'active_draft',
    PUBLISHED: 'published'
};

const licenseDynamicValues = {
    COPYRIGHT_HOLDER: 'copyright_holder',
    YEAR: 'year'
};

const slugReplacements = {
    '+': 'plus',
    '#': 'sharp',
    '.NET': 'dot-net'
};

const thumbnailStatus = {
    PENDING: 'pending',
    PROCESSING: 'processing',
    DONE: 'done',
    FAILED: 'failed'
};

const salesFilterTypes = {
    LAST_7_DAYS: 'last_7_days',
    THIS_MONTH: 'this_month',
    LAST_MONTH: 'last_month',
    LAST_30_DAYS: 'last_30_days',
    THIS_YEAR: 'this_year',
    ALL: 'all'
};

const paymentFilterTypes = {
    ALL: 'all',
    FREE: 'free',
    UNLOCKED: 'unlocked',
    LOCKED: 'locked'
};

const shareFilterTypes = {
    MOST_POPULAR: 'most-popular',
    MOST_LIKED: 'most-liked',
    RECENT_ADDITIONS: 'recent-additions',
    RECOMMENDED_FOR_YOU: 'recommended-for-you'
};

const thumbnailSource = {
    AUTO: 'auto',
    CUSTOM: 'custom'
};

const categoryTypes = {
    WEB_ELEMENTS: 'web_elements',
    MOBILE_ELEMENTS: 'mobile_elements'
};

const settingsSource = {
    OVERRIDE: 'override',
    GLOBAL: 'global'
}

const elementDefaultTags = {
    HTML: 'HTML',
    CSS: 'CSS',
    JavaScript: 'JavaScript'
}

const componentTitle = {
    [componentType.REPOSITORY]: 'project',
    [componentType.ELEMENTS]: 'element',
    [componentType.CODESPACE]: 'codespace',
    [componentType.MOBILE]: 'mobile component',
};

const balanceLockStatus = {
    LOCKED: 'locked',
    USED: 'used',
    EXPIRED: 'expired'
}

const balanceLockType = {
    FIAT: 'fiat',
    POINTS: 'points'
}

const transactionStatus = {
    PENDING: 'pending',
    PAID: 'paid',
    FAILED: 'failed',
    CANCELLED: 'cancelled',
}

const privateShareStatus = {
    PENDING: 'pending',
    ACCEPTED: 'accepted',
    EXPIRED: 'expired',
    REVOKED: 'revoked'
}

const accessType = {
    OWNER: 'owner',
    PUBLIC: 'public',
    PRIVATE_SHARE: 'private_share',
    UNLOCKED: 'unlocked'
}

module.exports = {
    unlockTypes,
    coinTypes,
    mpcBonusRequestStatus,
    userActions,
    filterTypes,
    componentOrientation,
    componentUserAction,
    componentType,
    componentState,
    componentCurrency,
    notificationTypes,
    reportTypes,
    reportStatus,
    elementFileNames,
    publishReference,
    fiatActivity,
    pointActivity,
    licenseType,
    pointActivityDescription,
    collectionState,
    licenseDynamicValues,
    slugReplacements,
    thumbnailStatus,
    salesFilterTypes,
    paymentFilterTypes,
    shareFilterTypes,
    thumbnailSource,
    categoryTypes,
    settingsSource,
    elementDefaultTags,
    componentTitle,
    balanceLockStatus,
    balanceLockType,
    transactionStatus,
    privateShareStatus,
    accessType
};