const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);
const { getGitlabMasterAccessToken } = require('./settings.service');
const path = require('path');
const fs = require('fs-extra');
const logger = require('../config/logger');

/**
 * Pushes extracted files to GitLab using Git CLI commands
 * @param {string} extractDir - Directory containing extracted files
 * @param {string} repoUrl - GitLab repository URL
 * @param {string} branch - Target branch name
 * @param {string} commitMessage - Commit message
 * @param {Object} gitlabUser - GitLab user information
 * @returns {Promise<Object>} - Result with commit ID and file count
 */
async function pushExtractedFilesToGitlab(
    extractDir,
    repoUrl,
    branch,
    commitMessage,
    gitlabUser
) {
    // Ensure extractDir is absolute
    const absoluteExtractDir = path.resolve(extractDir);
    const repoDir = path.join(absoluteExtractDir, 'repo');

    const accessToken = await getGitlabMasterAccessToken();

    let fileCount = 0;
    await countFilesInDir(absoluteExtractDir);

    // Construct the repository URL with credentials
    const credentialUrl = repoUrl.replace(
        'https://',
        `https://oauth2:${accessToken}@`
    );

    try {
        // Try to clone the existing branch first
        let cloneSuccess = false;
        
        try {
            logger.info(`Attempting to clone branch ${branch}...`);
            await execPromise(`git clone --quiet --branch=${branch} ${credentialUrl} ${repoDir}`);
            cloneSuccess = true;
            logger.info(`Successfully cloned existing branch ${branch}`);
        } catch (cloneErr) {
            logger.info(`Branch ${branch} doesn't exist or clone failed. Will create new branch.`);
            
            // Clone default branch instead
            try {
                await execPromise(`git clone --quiet ${credentialUrl} ${repoDir}`);
                cloneSuccess = true;
                logger.info(`Cloned repository with default branch`);
            } catch (defaultCloneErr) {
                logger.error(`Failed to clone repository: ${defaultCloneErr.message}`);
                throw new Error(`Unable to clone repository: ${defaultCloneErr.message}`);
            }
        }

        // Set Git configuration
        await execPromise(`git config user.name "${gitlabUser.username}"`, { cwd: repoDir });
        await execPromise(`git config user.email "${gitlabUser.email}"`, { cwd: repoDir });

        // Create/switch to target branch if needed
        try {
            await execPromise(`git checkout ${branch}`, { cwd: repoDir });
            logger.info(`Switched to existing branch ${branch}`);
        } catch (checkoutErr) {
            logger.info(`Creating new branch ${branch}`);
            await execPromise(`git checkout -b ${branch}`, { cwd: repoDir });
        }

        // Remove all tracked & untracked files (except .git and .gitignore)
        const files = await fs.readdir(repoDir);
        for (const file of files) {
            if (file !== '.git' && file !== '.gitignore') {
                await fs.remove(path.join(repoDir, file));
            }
        }

        // Copy new extracted contents into the repo (excluding repo folder itself)
        const extractedFiles = await fs.readdir(absoluteExtractDir);
        let copiedFiles = 0;
        
        for (const file of extractedFiles) {
            if (file !== 'repo') {
                const src = path.join(absoluteExtractDir, file);
                const dest = path.join(repoDir, file);
                
                try {
                    await fs.copy(src, dest, { overwrite: true });
                    copiedFiles++;
                    logger.debug(`Copied: ${file}`);
                } catch (copyErr) {
                    logger.warn(`Failed to copy ${file}: ${copyErr.message}`);
                }
            }
        }

        logger.info(`Copied ${copiedFiles} items to repository`);

        // Add all files (respects .gitignore if present)
        await execPromise('git add .', { cwd: repoDir });

        // Check if there are changes to commit
        try {
            await execPromise('git diff --cached --exit-code', { cwd: repoDir });
            logger.info('No changes to commit.');
            return { commitId: null, fileCount };
        } catch (error) {
            // There are changes to commit, proceed
            logger.info('Changes detected, proceeding with commit');
        }

        // Create commit
        await execPromise(`git commit --quiet -m "${commitMessage}"`, { cwd: repoDir });
        logger.info(`Created commit with message: ${commitMessage}`);

        // Push changes
        try {
            await execPromise(`git push --quiet origin ${branch}`, { cwd: repoDir });
            logger.info(`Successfully pushed to branch ${branch}`);
        } catch (pushErr) {
            // If push fails, try with upstream
            logger.info(`Setting upstream and pushing to ${branch}`);
            await execPromise(`git push --quiet -u origin ${branch}`, { cwd: repoDir });
        }

        // Get the commit ID
        const { stdout: commitId } = await execPromise('git rev-parse HEAD', { cwd: repoDir });
        
        logger.info(`Push completed successfully. Commit ID: ${commitId.trim()}`);
        
        return {
            commitId: commitId.trim(),
            fileCount
        };

    } catch (err) {
        logger.error(`Git operation failed: ${err.message}`);
        throw err;
    } finally {
        // Clean up the cloned repository
        try {
            if (await fs.pathExists(repoDir)) {
                await fs.remove(repoDir);
                logger.debug(`Cleaned up repository directory: ${repoDir}`);
            }
        } catch (cleanupErr) {
            logger.warn(`Failed to cleanup repository directory: ${cleanupErr.message}`);
        }
    }

    // Helper function to count files (excluding repo directory)
    async function countFilesInDir(dir) {
        const entries = await fs.readdir(dir, { withFileTypes: true });

        for (const entry of entries) {
            const fullPath = path.join(dir, entry.name);

            if (entry.isDirectory()) {
                // Skip .git and repo directories
                if (entry.name !== '.git' && entry.name !== 'repo') {
                    await countFilesInDir(fullPath);
                }
            } else {
                fileCount++;
            }
        }
    }
}

module.exports = {
    pushExtractedFilesToGitlab
};