const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);
const { getGitlabMasterAccessToken } = require('./settings.service');
const path = require('path');
const fs = require('fs-extra');
const logger = require('../config/logger');

/**
 * Pushes extracted files to GitLab using Git CLI commands
 * @param {string} extractDir - Directory containing extracted files
 * @param {string} repoUrl - GitLab repository URL
 * @param {string} branch - Target branch name
 * @param {string} commitMessage - Commit message
 * @param {Object} gitlabUser - GitLab user information
 * @returns {Promise<Object>} - Result with commit ID and file count
 */

async function pushExtractedFilesToGitlab(
    extractDir,
    repoUrl,
    branch,
    commitMessage,
    gitlabUser
) {
    // Ensure extractDir is absolute
    const absoluteExtractDir = path.resolve(extractDir);

    const accessToken = await getGitlabMasterAccessToken();

    let fileCount = 0;
    await countFilesInDir(absoluteExtractDir);

    // Construct the repository URL with credentials
    const credentialUrl = repoUrl.replace(
        'https://',
        `https://oauth2:${accessToken}@`
    );

    try {
        // Initialize Git repository
        await execPromise(`git init --initial-branch=${branch} --quiet`, { cwd: absoluteExtractDir });

        // Set Git configuration
        await execPromise(`git config user.name "${gitlabUser.username}"`, { cwd: absoluteExtractDir });
        await execPromise(`git config user.email "${gitlabUser.email}"`, { cwd: absoluteExtractDir });

        // Add all files (respects .gitignore)
        await execPromise('git add .', { cwd: absoluteExtractDir });

        // Check if there are changes to commit
        try {
            await execPromise('git diff --cached --exit-code', { cwd: absoluteExtractDir });
            logger.info('No changes to commit.');
            return { commitId: null, fileCount };
        } catch (error) {
            // There are changes to commit, proceed
            logger.info('Changes detected. Proceeding with commit.');
        }

        // Create commit
        await execPromise(`git commit --quiet -m "${commitMessage}"`, { cwd: absoluteExtractDir });

        // Check if branch exists remotely
        const { stdout: remoteBranches } = await execPromise(`git ls-remote --heads ${credentialUrl}`, { cwd: absoluteExtractDir });
        const branchExists = remoteBranches.includes(`refs/heads/${branch}`);

        if (branchExists) {
            // Force push
            logger.info(`Branch ${branch} exists remotely. Using force push.`);
            await execPromise(`git push --quiet -f ${credentialUrl} HEAD:${branch}`, { cwd: absoluteExtractDir });

        } else {
            // Branch doesn't exist, create it
            logger.info(`Branch ${branch} doesn't exist remotely. Creating new branch.`);
            await execPromise(`git checkout -b ${branch}`, { cwd: absoluteExtractDir });
            await execPromise(`git push --quiet -u ${credentialUrl} ${branch}`, { cwd: absoluteExtractDir });
        }

        // Get the commit ID
        const { stdout: commitId } = await execPromise('git rev-parse HEAD', { cwd: absoluteExtractDir });

        return {
            commitId: commitId.trim(),
            fileCount
        };

    } catch (err) {
        logger.error(`Git operation failed: ${err.message}`);
        throw err;
    }

    // Helper function to count files
    async function countFilesInDir(dir) {
        const entries = await fs.promises.readdir(dir, { withFileTypes: true });

        for (const entry of entries) {
            const fullPath = path.join(dir, entry.name);

            if (entry.isDirectory()) {
                if (entry.name !== '.git') {
                    await countFilesInDir(fullPath);
                }
            } else {
                fileCount++;
            }
        }
    }
}

/**
 * Clones a GitLab repository to a local directory
 * @param {string} repoUrl - GitLab repository URL
 * @param {string} targetDir - Directory to clone to
 * @param {string} accessToken - GitLab access token
 * @param {string} branch - Branch to clone
 * @returns {Promise<void>}
 */
// async function cloneRepository(repoUrl, targetDir, accessToken, branch = 'development') {
//     const credentialUrl = repoUrl.replace(
//         'https://',
//         `https://oauth2:${accessToken}@`
//     );

//     await execPromise(`git clone --branch ${branch} ${credentialUrl} ${targetDir}`);
// }

module.exports = {
    pushExtractedFilesToGitlab
    // cloneRepository
};