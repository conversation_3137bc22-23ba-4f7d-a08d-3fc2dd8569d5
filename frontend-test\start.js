#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Checking frontend test environment...\n');

// Check if all required files exist
const requiredFiles = [
    'index.html',
    'login.html', 
    'signup.html',
    'private-share.html',
    'shared-with-me.html',
    'component-view.html',
    'share-component.html',
    'styles.css',
    'config.js',
    'server.js'
];

let allFilesExist = true;

requiredFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - MISSING`);
        allFilesExist = false;
    }
});

if (!allFilesExist) {
    console.log('\n❌ Some required files are missing. Please ensure all files are present.');
    process.exit(1);
}

console.log('\n✅ All required files are present.');

// Check config.js content
const configPath = path.join(__dirname, 'config.js');
const configContent = fs.readFileSync(configPath, 'utf8');

if (configContent.includes('window.API_BASE_URL')) {
    console.log('✅ config.js appears to be valid JavaScript');
} else {
    console.log('❌ config.js may be corrupted');
}

console.log('\n🚀 Starting server...\n');

// Start the server
require('./server.js');
