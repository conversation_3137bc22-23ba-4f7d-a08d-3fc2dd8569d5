const Joi = require('joi');
const { componentType } = require('../../../config/component.constant');

class ComponentValidation {
    linkCodeSpacesToComponent(params) {
        const schema = Joi.object({
            code_spaces: Joi.array().items(Joi.string().trim()).min(1).unique().required()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    updateComponentRepository(params) {
        const schema = Joi.object({
            title: Joi.string().required(),
            collection_id: Joi.string().optional(),
            short_description: Joi.string().optional().allow(null, ''),
            long_description: Joi.string().optional().allow(null, ''),
            difficulty_level: Joi.string().optional().allow(null, ''),
            identification_tag: Joi.array().items(Joi.string().trim()).unique().optional(),
            component_type: Joi.string().optional(),
            is_paid: Joi.boolean().optional(),
            mpn_parity: Joi.number().default(0),
            purchase_price: Joi.object({
                fiat: Joi.number().default(0),
                mpn_points: Joi.number().default(0)
            }),
            item_price: Joi.object({
                fiat: Joi.number().default(0),
                mpn_points: Joi.number().default(0)
            }),
            buyer_fee: Joi.object({
                fiat: Joi.number().default(0),
                mpn_points: Joi.number().default(0)
            }),
            platform_data: Joi.array().optional(),
            assets: Joi.array().items(Joi.object({
                file_url: Joi.string().required(),
                file_extension: Joi.string().required(),
                file_name: Joi.string().optional().allow(null, ''),
                file_original_name: Joi.string().required(),
                file_mime_type: Joi.string().required(),
                file_notes: Joi.string().optional().allow(null, '') // optional field
            })).optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    calculateSalesProjection(params) {
        const schema = Joi.object({
            target: Joi.number().min(0).required(),
            price: Joi.number().min(0).required(),
            component_type: Joi.string().valid(...Object.values(componentType)).required(),
        });
        return schema.validate(params);
    }

    createComponentPlaceholder(params) {
        const schema = Joi.object({
            title: Joi.string().optional(),
            component_type: Joi.string().valid(componentType.REPOSITORY, componentType.MOBILE).required(),
            category_id: Joi.string().optional(),
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }
}

module.exports = new ComponentValidation();