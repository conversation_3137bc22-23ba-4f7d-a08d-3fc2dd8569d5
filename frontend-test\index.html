<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Private Share Test - Home</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🔗 Private Share Test Environment</h1>
            <p>Test the complete private share functionality</p>
        </header>

        <div class="auth-status" id="authStatus">
            <span id="userInfo">Not logged in</span>
            <button id="logoutBtn" style="display: none;" onclick="logout()">Logout</button>
        </div>

        <div class="navigation">
            <h2>🧪 Test Scenarios</h2>
            
            <div class="test-section">
                <h3>1. Authentication Flow</h3>
                <div class="button-group">
                    <button onclick="window.location.href='login.html'">🔑 Login</button>
                    <button onclick="window.location.href='signup.html'">📝 Sign Up</button>
                </div>
            </div>

            <div class="test-section">
                <h3>2. Private Share Access</h3>
                <div class="input-group">
                    <label for="accessToken">Access Token:</label>
                    <input type="text" id="accessToken" placeholder="Enter access token from email">
                    <button onclick="testTokenAccess()">🔍 Test Token Access</button>
                </div>
                <div class="input-group">
                    <label for="componentSlug">Component Slug:</label>
                    <input type="text" id="componentSlug" placeholder="Enter component slug">
                    <button onclick="accessComponent()">🎯 Access Component</button>
                </div>
            </div>

            <div class="test-section">
                <h3>3. Share Management</h3>
                <div class="button-group">
                    <button onclick="window.location.href='shared-with-me.html'">📥 Shared with Me</button>
                    <button onclick="window.location.href='manage-shares.html'">📤 Manage My Shares</button>
                </div>
            </div>

            <div class="test-section">
                <h3>4. Component Creation & Sharing</h3>
                <div class="button-group">
                    <button onclick="window.location.href='create-component.html'">➕ Create Component</button>
                    <button onclick="window.location.href='share-component.html'">🔗 Share Component</button>
                </div>
            </div>
        </div>

        <div class="api-config">
            <h3>⚙️ API Configuration</h3>
            <div class="input-group">
                <label for="apiBaseUrl">Backend URL:</label>
                <input type="text" id="apiBaseUrl" value="http://localhost:7898" placeholder="http://localhost:7898">
                <button onclick="saveConfig()">💾 Save</button>
            </div>
        </div>

        <div class="logs">
            <h3>📋 Test Logs</h3>
            <div id="logContainer"></div>
            <button onclick="clearLogs()">🗑️ Clear Logs</button>
        </div>
    </div>

    <script src="config.js"></script>
    <script>
        // Check authentication status on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();
            loadConfig();
        });

        function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');
            
            if (token && userInfo) {
                const user = JSON.parse(userInfo);
                document.getElementById('userInfo').textContent = `Logged in as: ${user.first_name} ${user.last_name} (${user.email})`;
                document.getElementById('logoutBtn').style.display = 'inline-block';
            } else {
                document.getElementById('userInfo').textContent = 'Not logged in';
                document.getElementById('logoutBtn').style.display = 'none';
            }
        }

        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('userInfo');
            checkAuthStatus();
            logMessage('Logged out successfully', 'success');
        }

        function loadConfig() {
            const savedUrl = localStorage.getItem('apiBaseUrl');
            if (savedUrl) {
                document.getElementById('apiBaseUrl').value = savedUrl;
                window.API_BASE_URL = savedUrl;
            }
        }

        function saveConfig() {
            const url = document.getElementById('apiBaseUrl').value;
            localStorage.setItem('apiBaseUrl', url);
            window.API_BASE_URL = url;
            logMessage(`API URL saved: ${url}`, 'success');
        }

        async function testTokenAccess() {
            const token = document.getElementById('accessToken').value;
            if (!token) {
                logMessage('Please enter an access token', 'error');
                return;
            }

            try {
                logMessage(`Testing token: ${token}`, 'info');
                
                const response = await fetch(`${API_BASE_URL}/component/private-share/${token}/validate`, {
                    method: 'GET',
                    headers: {
                    'Content-Type': 'application/json',
                    'token': localStorage.getItem('authToken'),
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                },
                });

                const data = await response.json();
                logMessage(`Token validation response: ${JSON.stringify(data, null, 2)}`, 'success');

                if (data.status === 200) {
                    if (data.data.requiresSignup) {
                        logMessage('Token valid but requires signup', 'warning');
                        window.location.href = `signup.html?returnUrl=private-share/${token}`;
                    } else {
                        logMessage('Token valid - access granted', 'success');
                    }
                }
            } catch (error) {
                logMessage(`Error testing token: ${error.message}`, 'error');
            }
        }

        async function accessComponent() {
            const token = document.getElementById('accessToken').value;
            const slug = document.getElementById('componentSlug').value;
            
            if (!token || !slug) {
                logMessage('Please enter both access token and component slug', 'error');
                return;
            }

            try {
                logMessage(`Accessing component: ${slug} with token: ${token}`, 'info');
                
                const response = await fetch(`${API_BASE_URL}/component/private/${slug}?token=${token}`, {
                    method: 'GET',
                    headers: {
                    'Content-Type': 'application/json',
                    'token': localStorage.getItem('authToken'),
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                },
                });

                const data = await response.json();
                logMessage(`Component access response: ${JSON.stringify(data, null, 2)}`, 'success');

                if (data.status === 200) {
                    if (data.data.requires_payment) {
                        logMessage('Component requires payment', 'warning');
                        window.location.href = `payment.html?component=${slug}&token=${token}`;
                    } else {
                        logMessage('Component access granted', 'success');
                        window.location.href = `component-view.html?slug=${slug}&token=${token}`;
                    }
                }
            } catch (error) {
                logMessage(`Error accessing component: ${error.message}`, 'error');
            }
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }
    </script>
</body>
</html>
