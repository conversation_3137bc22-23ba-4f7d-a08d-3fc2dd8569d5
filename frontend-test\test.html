<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Test</title>
</head>
<body>
    <h1>Server Test Page</h1>
    <p>If you can see this, the server is working correctly.</p>
    
    <h2>Testing config.js loading:</h2>
    <div id="configTest">Loading...</div>
    
    <script src="config.js"></script>
    <script>
        document.getElementById('configTest').innerHTML = 
            typeof window.API_BASE_URL !== 'undefined' ? 
            `✅ Config.js loaded successfully. API_BASE_URL: ${window.API_BASE_URL}` : 
            '❌ Config.js failed to load';
    </script>
</body>
</html>
