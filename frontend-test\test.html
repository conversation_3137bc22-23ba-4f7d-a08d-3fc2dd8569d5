<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Test</title>
</head>
<body>
    <h1>Server Test Page</h1>
    <p>If you can see this, the server is working correctly.</p>
    
    <h2>Testing config.js loading:</h2>
    <div id="configTest">Loading...</div>

    <h2>Testing session data:</h2>
    <div id="sessionTest">Loading...</div>

    <h2>Testing authentication:</h2>
    <div id="authTest">
        <button onclick="testAuth()">Test Authentication Status</button>
        <div id="authResult"></div>
    </div>

    <script src="config.js"></script>
    <script>
        // Test config loading
        document.getElementById('configTest').innerHTML =
            typeof window.API_BASE_URL !== 'undefined' ?
            `✅ Config.js loaded successfully. API_BASE_URL: ${window.API_BASE_URL}` :
            '❌ Config.js failed to load';

        // Test session data
        function testSessionData() {
            const token = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');
            const userId = localStorage.getItem('userId');
            const userEmail = localStorage.getItem('userEmail');

            let sessionHtml = '<h3>Session Data:</h3>';
            sessionHtml += `<p><strong>Token:</strong> ${token ? '✅ Present' : '❌ Missing'}</p>`;
            sessionHtml += `<p><strong>User Info:</strong> ${userInfo ? '✅ Present' : '❌ Missing'}</p>`;
            sessionHtml += `<p><strong>User ID:</strong> ${userId ? '✅ Present' : '❌ Missing'}</p>`;
            sessionHtml += `<p><strong>User Email:</strong> ${userEmail ? '✅ Present' : '❌ Missing'}</p>`;

            if (userInfo) {
                try {
                    const user = JSON.parse(userInfo);
                    sessionHtml += `<h4>User Details:</h4>`;
                    sessionHtml += `<p>Name: ${user.first_name} ${user.last_name}</p>`;
                    sessionHtml += `<p>Email: ${user.email}</p>`;
                    sessionHtml += `<p>ID: ${user._id}</p>`;
                } catch (e) {
                    sessionHtml += `<p>❌ Error parsing user info: ${e.message}</p>`;
                }
            }

            document.getElementById('sessionTest').innerHTML = sessionHtml;
        }

        async function testAuth() {
            const resultDiv = document.getElementById('authResult');
            resultDiv.innerHTML = 'Testing authentication...';

            try {
                const { response, data } = await apiCall('/auth/check-session', {
                    method: 'GET'
                });

                resultDiv.innerHTML = `
                    <h4>Authentication Test Result:</h4>
                    <p><strong>Status:</strong> ${data.status}</p>
                    <p><strong>Message:</strong> ${data.message}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h4>Authentication Test Error:</h4>
                    <p>${error.message}</p>
                `;
            }
        }

        // Run session test on load
        testSessionData();

        // Refresh session test every 5 seconds
        setInterval(testSessionData, 5000);
    </script>
</body>
</html>
