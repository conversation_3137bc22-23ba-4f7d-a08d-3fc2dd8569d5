<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connection Test</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="form-header">
                <h1>🔌 API Connection Test</h1>
                <p>Testing backend connectivity</p>
            </div>

            <div class="card">
                <h3>Backend Configuration</h3>
                <div id="configInfo"></div>
                
                <h3>Connection Tests</h3>
                <div class="button-group">
                    <button onclick="testHealthEndpoint()" class="success">Test Health Endpoint</button>
                    <button onclick="testPrivateShareEndpoint()" class="primary">Test Private Share Endpoint</button>
                </div>
                
                <div class="button-group">
                    <button onclick="testWithAuth()" class="secondary">Test With Auth</button>
                    <button onclick="testCORS()" class="warning">Test CORS</button>
                </div>
                
                <h3>Test Results</h3>
                <div id="testResults"></div>
            </div>

            <div class="logs">
                <h3>📋 Test Logs</h3>
                <div id="logContainer"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:7898/api/front';
        const TEST_TOKEN = '336569f3fa873ab0d831c8a3d45c937cf4786e27a4d328ffc3d7a8fd7deecaf8';
        
        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type}] ${message}`);
        }
        
        function showResult(test, success, message, details = '') {
            const resultsDiv = document.getElementById('testResults');
            const resultClass = success ? 'success' : 'error';
            const icon = success ? '✅' : '❌';
            
            const resultHtml = `
                <div class="test-result ${resultClass}" style="margin: 10px 0; padding: 10px; border-radius: 5px;">
                    <strong>${icon} ${test}</strong><br>
                    ${message}
                    ${details ? `<br><small>${details}</small>` : ''}
                </div>
            `;
            
            resultsDiv.innerHTML += resultHtml;
        }
        
        async function testHealthEndpoint() {
            logMessage('Testing health endpoint...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const status = response.status;
                const text = await response.text();
                
                logMessage(`Health endpoint response: ${status}`, 'info');
                logMessage(`Response body: ${text}`, 'info');
                
                if (response.ok) {
                    showResult('Health Endpoint', true, `Status: ${status}`, text);
                } else {
                    showResult('Health Endpoint', false, `Status: ${status}`, text);
                }
            } catch (error) {
                logMessage(`Health endpoint error: ${error.message}`, 'error');
                showResult('Health Endpoint', false, `Error: ${error.message}`, 'Check if backend server is running');
            }
        }
        
        async function testPrivateShareEndpoint() {
            logMessage('Testing private share endpoint...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/component/private-share/${TEST_TOKEN}/validate`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });
                
                const status = response.status;
                const contentType = response.headers.get('content-type');
                
                logMessage(`Private share endpoint response: ${status}`, 'info');
                logMessage(`Content-Type: ${contentType}`, 'info');
                
                let data;
                if (contentType && contentType.includes('application/json')) {
                    data = await response.json();
                    logMessage(`Response data: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    data = await response.text();
                    logMessage(`Response text: ${data}`, 'info');
                }
                
                if (response.ok) {
                    showResult('Private Share Endpoint', true, `Status: ${status}`, JSON.stringify(data, null, 2));
                } else {
                    showResult('Private Share Endpoint', false, `Status: ${status}`, JSON.stringify(data, null, 2));
                }
            } catch (error) {
                logMessage(`Private share endpoint error: ${error.message}`, 'error');
                showResult('Private Share Endpoint', false, `Error: ${error.message}`, 'Check network connectivity and CORS settings');
            }
        }
        
        async function testWithAuth() {
            logMessage('Testing with authentication...', 'info');
            
            const authToken = localStorage.getItem('authToken');
            if (!authToken) {
                showResult('Auth Test', false, 'No auth token found', 'Please login first');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/component/private-share/${TEST_TOKEN}/validate`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    credentials: 'include'
                });
                
                const status = response.status;
                const data = await response.json();
                
                logMessage(`Auth test response: ${status}`, 'info');
                logMessage(`Auth test data: ${JSON.stringify(data, null, 2)}`, 'info');
                
                showResult('Auth Test', response.ok, `Status: ${status}`, JSON.stringify(data, null, 2));
            } catch (error) {
                logMessage(`Auth test error: ${error.message}`, 'error');
                showResult('Auth Test', false, `Error: ${error.message}`, '');
            }
        }
        
        async function testCORS() {
            logMessage('Testing CORS...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/component/private-share/${TEST_TOKEN}/validate`, {
                    method: 'OPTIONS'
                });
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };
                
                logMessage(`CORS headers: ${JSON.stringify(corsHeaders, null, 2)}`, 'info');
                
                const hasCORS = corsHeaders['Access-Control-Allow-Origin'] !== null;
                showResult('CORS Test', hasCORS, hasCORS ? 'CORS enabled' : 'CORS not configured', JSON.stringify(corsHeaders, null, 2));
            } catch (error) {
                logMessage(`CORS test error: ${error.message}`, 'error');
                showResult('CORS Test', false, `Error: ${error.message}`, 'CORS might be blocking the request');
            }
        }
        
        function showConfig() {
            const configDiv = document.getElementById('configInfo');
            const authToken = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');
            
            configDiv.innerHTML = `
                <p><strong>API Base URL:</strong> ${API_BASE_URL}</p>
                <p><strong>Test Token:</strong> ${TEST_TOKEN.substring(0, 20)}...</p>
                <p><strong>Auth Token:</strong> ${authToken ? '✅ Present' : '❌ Missing'}</p>
                <p><strong>User Info:</strong> ${userInfo ? '✅ Present' : '❌ Missing'}</p>
            `;
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            logMessage('API test page loaded', 'info');
            showConfig();
        });
    </script>
</body>
</html>
