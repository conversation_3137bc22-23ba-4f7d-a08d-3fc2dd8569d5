const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { privateShareStatus, accessControls, accessDuration, shareType } = require('../../config/component.constant');
const logger = require('../config/logger');

const componentPrivateShareSchema = new Schema({
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components',
        required: true
    },
    shared_by: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        required: true
    },
    access_type: {
        type: String,
        enum: Object.values(shareType),
        required: true
    },
    shared_with_email: {
        type: String,
        lowercase: true,
        trim: true
    },
    shared_with_user: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        default: null
    },
    access_token: {
        type: String,
        required: true,
        unique: true
    },
    status: {
        type: String,
        enum: Object.values(privateShareStatus),
        default: privateShareStatus.PENDING
    },
    expires_at: {
        type: Date
    },
    access_duration: {
        type: String,
        enum: Object.values(accessDuration),
        default: accessDuration.UNDEFINED
    },
    duration_days: {
        type: Number
    },
    access_controls: [{
        type: String,
        enum: Object.values(accessControls)
    }],
    accessed_at: {
        type: Date
    },
    access_count: {
        type: Number,
        default: 0
    },
    personal_message: {
        type: String,
        maxlength: 500
    },
    link_name: {
        type: String,
        maxlength: 100
    },
    is_active: {
        type: Boolean,
        default: true
    },
    revoked_at: {
        type: Date
    },
    revoked_by: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    revoked_reason: {
        type: String,
        maxlength: 200
    },
    accepted_at: {
        type: Date
    },
    deleted_at: {
        type: Date
    },
    deleted_by: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    deleted_reason: {
        type: String,
        maxlength: 200,
        default: 'Deleted by user'
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

// Compound indexes for performance
componentPrivateShareSchema.index({ component_id: 1, shared_with_email: 1 });
componentPrivateShareSchema.index({ shared_with_user: 1, status: 1, expires_at: 1 });
componentPrivateShareSchema.index({ access_token: 1 });
componentPrivateShareSchema.index({ shared_by: 1, status: 1 });
componentPrivateShareSchema.index({ expires_at: 1 }); // For cleanup jobs
componentPrivateShareSchema.index({ shared_by: 1, access_type: 1, is_active: 1, status: 1 }); // For getMyShareableLinks optimization
componentPrivateShareSchema.index({ shared_with_user: 1, shared_with_email: 1, status: 1, is_active: 1 }); // For getComponentsSharedWithMe optimization
componentPrivateShareSchema.index({ status: 1, is_active: 1 }); // For filtering out deleted shares

// Pre-save middleware to set shared_with_user if email matches existing user
// BUT ONLY for invite-based shares, NOT for link-based shares
componentPrivateShareSchema.pre('save', async function(next) {

    // Only apply this logic for invite-based shares with an email
    if (this.isNew &&
        !this.shared_with_user &&
        this.access_type === 'by_invite' &&
        this.shared_with_email) {

        logger.info(`Pre-save middleware: Processing invite-based share for email: ${this.shared_with_email}`);

        try {
            const Users = require('./users.model').Users;
            const existingUser = await Users.findOne({
                email: this.shared_with_email,
                is_active: true
            }).select('_id').lean();

            if (existingUser) {
                logger.info(`Pre-save middleware: Found existing user ${existingUser._id} for email ${this.shared_with_email}`);
                this.shared_with_user = existingUser._id;
                this.status = 'accepted'; // Auto-accept for existing users
            } else {
                logger.info(`Pre-save middleware: No existing user found for email ${this.shared_with_email}`);
            }
        } catch (error) {
            logger.error(`Pre-save middleware error: ${error.message}`);
            // Continue without setting user if lookup fails
        }
    } else {
        logger.info(`Pre-save middleware: Skipping (not applicable for this share type)`);
    }
    next();
});

// Pre-init hook to store original values for validation
componentPrivateShareSchema.pre('init', function() {
    this._original = this.toObject();
});

// Pre-save validation for status transitions and data consistency
componentPrivateShareSchema.pre('save', function(next) {
    // Validate status transitions
    if (this.isModified('status')) {
        const validTransitions = {
            'pending': ['accepted', 'expired', 'revoked', 'deleted'],
            'accepted': ['expired', 'revoked', 'deleted'],
            'expired': ['deleted'], // Can only delete expired shares
            'revoked': ['deleted'], // Can only delete revoked shares
            'deleted': [] // Deleted shares cannot transition to other states
        };

        if (this.isNew) {
            // New documents can start with any status except deleted
            if (this.status === 'deleted') {
                return next(new Error('New shares cannot be created with deleted status'));
            }
        } else {
            const originalStatus = this._original?.status || 'pending';
            const newStatus = this.status;

            if (originalStatus !== newStatus) {
                const allowedTransitions = validTransitions[originalStatus] || [];
                if (!allowedTransitions.includes(newStatus)) {
                    return next(new Error(`Invalid status transition from '${originalStatus}' to '${newStatus}'`));
                }
            }
        }
    }

    // Set deletion timestamp when status changes to deleted
    if (this.isModified('status') && this.status === 'deleted' && !this.deleted_at) {
        this.deleted_at = new Date();
    }

    // Set acceptance timestamp when status changes to accepted
    if (this.isModified('status') && this.status === 'accepted' && !this.accepted_at) {
        this.accepted_at = new Date();
    }

    // Validate that deleted shares are inactive
    if (this.status === 'deleted' && this.is_active !== false) {
        this.is_active = false;
    }

    // Validate access token uniqueness for active shares only
    if (this.isNew || this.isModified('access_token')) {
        // This will be handled by the unique index, but we can add custom logic here if needed
    }

    next();
});

// Static method to check if user has access to component
componentPrivateShareSchema.statics.hasAccess = async function(componentId, userId, userEmail) {
    const baseQuery = {
        component_id: componentId,
        status: { $in: [privateShareStatus.PENDING, privateShareStatus.ACCEPTED, privateShareStatus.DELETED] },
        is_active: true,
        $or: [
            { expires_at: null }, // No expiration
            { expires_at: { $gt: new Date() } } // Not yet expired
        ]
    };

    // Check for public shareable links first (by_link with no specific user/email)
    const publicLinkQuery = {
        ...baseQuery,
        access_type: shareType.BY_LINK,
        shared_with_user: null,
        shared_with_email: null
    };

    const publicLink = await this.findOne(publicLinkQuery).lean();
    if (publicLink) {
        return true; // Public link exists, anyone can access
    }

    // If no public link, check for user-specific invites (by_invite)
    if (!userId && !userEmail) {
        return false; // No public link and no user info provided
    }

    const inviteQuery = {
        ...baseQuery,
        access_type: shareType.BY_INVITE
    };

    if (userId) {
        inviteQuery.$or = [
            { shared_with_user: userId },
            { shared_with_email: userEmail, shared_with_user: null }
        ];
    } else if (userEmail) {
        inviteQuery.shared_with_email = userEmail;
        inviteQuery.shared_with_user = null;
    }

    const invite = await this.findOne(inviteQuery).lean();
    return !!invite;
};

// Static method to validate access by token (for both public links and private invites)
componentPrivateShareSchema.statics.validateTokenAccess = async function(accessToken, userId = null, userEmail = null) {
    const baseQuery = {
        access_token: accessToken,
        status: { $in: [privateShareStatus.PENDING, privateShareStatus.ACCEPTED, privateShareStatus.DELETED] }, // Exclude deleted, expired, revoked
        is_active: true,
        // Improved expiration check: either no expiration or not yet expired
        $or: [
            { expires_at: null }, // No expiration set
            { expires_at: { $gt: new Date() } } // Not yet expired
        ]
    };

    const share = await this.findOne(baseQuery)
        .populate('component_id', 'title slug image_url created_by_user is_paid component_state')
        .populate('shared_by', 'first_name last_name username')
        .lean();

    if (!share) {
        return { isValid: false, error: 'Invalid or expired access token' };
    }

    // For public shareable links (by_link)
    if (share.access_type === 'by_link') {
        // If share is already assigned to a user, check if it's the current user
        if (share.shared_with_user) {
            if (!userId || share.shared_with_user.toString() !== userId.toString()) {
                return { isValid: false, error: 'This link has already been claimed by another user' };
            }
            // User is accessing their own claimed link
            return {
                isValid: true,
                share,
                accessType: 'public_link',
                requiresSignup: false,
                requiresLogin: false
            };
        }

        // Link is not yet claimed - check authentication
        if (!userId && !userEmail) {
            return {
                isValid: true,
                share,
                accessType: 'public_link',
                requiresSignup: true,  // User needs to signup/login to claim
                requiresLogin: false
            };
        }

        // User is authenticated and can claim the link
        return {
            isValid: true,
            share,
            accessType: 'public_link',
            requiresSignup: false,
            requiresLogin: false
        };
    }

    // For private invites (by_invite), check user-specific access
    if (share.access_type === privateShareStatus.BY_INVITE) {

        // Check if the invite is for this specific user
        const isAuthorizedByUserId = share.shared_with_user && share.shared_with_user.toString() === userId;
        const isAuthorizedByEmail = share.shared_with_email && share.shared_with_email === userEmail;

        if (!isAuthorizedByUserId && !isAuthorizedByEmail) {
            // Check if this is for a different user entirely
            if (share.shared_with_email && userEmail && share.shared_with_email !== userEmail) {
                return { isValid: false, error: 'This invitation is for a different email address' };
            }

            if (share.shared_with_user && userId && share.shared_with_user.toString() !== userId) {
                return { isValid: false, error: 'This invitation is for a different user' };
            }

            // If user is not logged in but the invite is for their email, check if they exist
            if (!userId && share.shared_with_email) {
                const Users = require('./users.model').Users;
                const existingUser = await Users.findOne({
                    email: share.shared_with_email,
                    is_verified: true,
                    is_active: true
                }).lean();

                if (existingUser) {
                    return {
                        isValid: true,
                        share,
                        accessType: 'private_invite',
                        requiresSignup: false,
                        requiresLogin: true  // User exists but needs to login
                    };
                } else {
                    return {
                        isValid: true,
                        share,
                        accessType: 'private_invite',
                        requiresSignup: true,
                        requiresLogin: false  // User doesn't exist, needs to signup
                    };
                }
            }

            return { isValid: false, error: 'This invitation is not for you' };
        }

        return {
            isValid: true,
            share,
            accessType: 'private_invite',
            requiresSignup: false,
            requiresLogin: false
        };
    }

    return { isValid: false, error: 'Invalid share type' };
};

// Static method to link pending shares when user signs up
componentPrivateShareSchema.statics.linkPendingShares = async function(userId, email) {
    const now = new Date();
    const result = await this.updateMany(
        {
            shared_with_email: email,
            shared_with_user: null,
            status: 'pending', // Only link pending shares, not deleted ones
            is_active: true,
            $or: [
                { expires_at: null }, // No expiration
                { expires_at: { $gt: now } } // Not yet expired
            ]
        },
        {
            $set: {
                shared_with_user: userId,
                status: 'accepted',
                accepted_at: now,
                updated_at: now
            }
        }
    );
    return result.modifiedCount;
};

// Static method to cleanup expired shares
componentPrivateShareSchema.statics.cleanupExpiredShares = async function() {
    const result = await this.updateMany(
        {
            expires_at: { $lt: new Date() },
            status: { $in: [privateShareStatus.PENDING, privateShareStatus.ACCEPTED] },
            is_active: true
        },
        {
            status: privateShareStatus.EXPIRED,
            updated_at: new Date()
        }
    );
    return result.modifiedCount;
};

// Static method to get active shares query (excludes deleted shares)
componentPrivateShareSchema.statics.getActiveSharesQuery = function(additionalFilters = {}) {
    return {
        ...additionalFilters,
        is_active: true
    };
};

// Static method to soft delete a share
componentPrivateShareSchema.statics.softDelete = async function(shareId, deletedBy, reason = 'Deleted by user') {
    const result = await this.findByIdAndUpdate(
        shareId,
        {
            status: privateShareStatus.DELETED,
            deleted_at: new Date(),
            deleted_by: deletedBy,
            deleted_reason: reason,
            updated_at: new Date()
        },
        { new: true }
    );
    return result;
};

// Static method to check if a share can be safely hard deleted
componentPrivateShareSchema.statics.canHardDelete = function(share) {
    // Safe to hard delete if:
    // 1. Status is pending (never accessed)
    // 2. Status is revoked or expired (already inactive)
    // 3. Access count is 0 (never accessed)
    const safeStatuses = [privateShareStatus.PENDING, privateShareStatus.REVOKED, privateShareStatus.EXPIRED];
    return safeStatuses.includes(share.status) || share.access_count === 0;
};

const ComponentPrivateShares = mongoose.model('component_private_shares', componentPrivateShareSchema);

module.exports = {
    ComponentPrivateShares
};