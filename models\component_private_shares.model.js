const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const componentPrivateShareSchema = new Schema({
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components',
        required: true
    },
    shared_by: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        required: true
    },
    access_type: {
        type: String,
        enum: ['by_link', 'by_invite'],
        required: true
    },
    shared_with_email: {
        type: String,
        lowercase: true,
        trim: true
    },
    shared_with_user: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        default: null
    },
    access_token: {
        type: String,
        required: true,
        unique: true
    },
    status: {
        type: String,
        enum: ['pending', 'accepted', 'expired', 'revoked'],
        default: 'pending'
    },
    expires_at: {
        type: Date
    },
    access_duration: {
        type: String,
        enum: ['undefined', 'days'],
        default: 'undefined'
    },
    duration_days: {
        type: Number
    },
    access_controls: [{
        type: String,
        enum: ['fork', 'download', 'copy']
    }],
    accessed_at: {
        type: Date
    },
    access_count: {
        type: Number,
        default: 0
    },
    personal_message: {
        type: String,
        maxlength: 500
    },
    link_name: {
        type: String,
        maxlength: 100
    },
    is_active: {
        type: Boolean,
        default: true
    },
    revoked_at: {
        type: Date
    },
    revoked_by: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    accepted_at: {
        type: Date
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

// Compound indexes for performance
componentPrivateShareSchema.index({ component_id: 1, shared_with_email: 1 });
componentPrivateShareSchema.index({ shared_with_user: 1, status: 1, expires_at: 1 });
componentPrivateShareSchema.index({ access_token: 1 });
componentPrivateShareSchema.index({ shared_by: 1, status: 1 });
componentPrivateShareSchema.index({ expires_at: 1 }); // For cleanup jobs

// Pre-save middleware to set shared_with_user if email matches existing user
componentPrivateShareSchema.pre('save', async function(next) {
    if (this.isNew && !this.shared_with_user) {
        try {
            const Users = require('./users.model').Users;
            const existingUser = await Users.findOne({ 
                email: this.shared_with_email,
                is_active: true 
            }).select('_id').lean();
            
            if (existingUser) {
                this.shared_with_user = existingUser._id;
                this.status = 'accepted'; // Auto-accept for existing users
            }
        } catch (error) {
            // Continue without setting user if lookup fails
        }
    }
    next();
});

// Static method to check if user has access to component
componentPrivateShareSchema.statics.hasAccess = async function(componentId, userId, userEmail) {
    const baseQuery = {
        component_id: componentId,
        status: { $in: ['pending', 'accepted'] },
        expires_at: { $gt: new Date() },
        is_active: true
    };

    // Check for public shareable links first (by_link with no specific user/email)
    const publicLinkQuery = {
        ...baseQuery,
        access_type: 'by_link',
        shared_with_user: null,
        shared_with_email: null
    };

    const publicLink = await this.findOne(publicLinkQuery).lean();
    if (publicLink) {
        return true; // Public link exists, anyone can access
    }

    // If no public link, check for user-specific invites (by_invite)
    if (!userId && !userEmail) {
        return false; // No public link and no user info provided
    }

    const inviteQuery = {
        ...baseQuery,
        access_type: 'by_invite'
    };

    if (userId) {
        inviteQuery.$or = [
            { shared_with_user: userId },
            { shared_with_email: userEmail, shared_with_user: null }
        ];
    } else if (userEmail) {
        inviteQuery.shared_with_email = userEmail;
        inviteQuery.shared_with_user = null;
    }

    const invite = await this.findOne(inviteQuery).lean();
    return !!invite;
};

// Static method to validate access by token (for both public links and private invites)
componentPrivateShareSchema.statics.validateTokenAccess = async function(accessToken, userId = null, userEmail = null) {
    const baseQuery = {
        access_token: accessToken,
        status: { $in: ['pending', 'accepted'] },
        expires_at: { $gt: new Date() },
        is_active: true
    };

    const share = await this.findOne(baseQuery)
        .populate('component_id', 'title slug image_url created_by_user is_paid component_state')
        .populate('shared_by', 'first_name last_name username')
        .lean();

    if (!share) {
        return { isValid: false, error: 'Invalid or expired access token' };
    }

    // For public shareable links (by_link), anyone can access
    if (share.access_type === 'by_link' && !share.shared_with_user && !share.shared_with_email) {
        return {
            isValid: true,
            share,
            accessType: 'public_link',
            requiresSignup: false
        };
    }

    // For private invites (by_invite), check user-specific access
    if (share.access_type === 'by_invite') {
        // If user is not logged in, require signup
        if (!userId && !userEmail) {
            return {
                isValid: true,
                share,
                accessType: 'private_invite',
                requiresSignup: true
            };
        }

        // Check if the invite is for this specific user
        const isAuthorized = (
            (share.shared_with_user && share.shared_with_user.toString() === userId) ||
            (share.shared_with_email && share.shared_with_email === userEmail)
        );

        if (!isAuthorized) {
            return { isValid: false, error: 'This invitation is not for you' };
        }

        return {
            isValid: true,
            share,
            accessType: 'private_invite',
            requiresSignup: false
        };
    }

    return { isValid: false, error: 'Invalid share type' };
};

// Static method to link pending shares when user signs up
componentPrivateShareSchema.statics.linkPendingShares = async function(userId, email) {
    const now = new Date();
    const result = await this.updateMany(
        {
            shared_with_email: email,
            shared_with_user: null,
            status: 'pending',
            expires_at: { $gt: now },
            is_active: true
        },
        {
            $set: {
                shared_with_user: userId,
                status: 'accepted',
                accepted_at: now,
                updated_at: now
            }
        }
    );
    return result.modifiedCount;
};

// Static method to cleanup expired shares
componentPrivateShareSchema.statics.cleanupExpiredShares = async function() {
    const result = await this.updateMany(
        {
            expires_at: { $lt: new Date() },
            status: { $ne: 'expired' }
        },
        {
            status: 'expired'
        }
    );
    return result.modifiedCount;
};

const ComponentPrivateShares = mongoose.model('component_private_shares', componentPrivateShareSchema);

module.exports = {
    ComponentPrivateShares
};