// Simple test to verify user isolation in shared-with-me
require('dotenv').config();
const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

const { ComponentPrivateShares } = require('./models/component_private_shares.model');
const { Users } = require('./models/users.model');

async function testUserIsolation() {
    try {
        console.log('🔍 Testing User Isolation in Shared-With-Me...\n');
        
        // Get two different users
        const users = await Users.find({ is_verified: true }).limit(2).lean();
        if (users.length < 2) {
            console.log('❌ Need at least 2 verified users to test isolation');
            return;
        }
        
        const userA = users[0];
        const userB = users[1];
        
        console.log(`👤 User A: ${userA.first_name} ${userA.last_name} (${userA.email})`);
        console.log(`   ID: ${userA._id}`);
        console.log(`👤 User B: ${userB.first_name} ${userB.last_name} (${userB.email})`);
        console.log(`   ID: ${userB._id}`);
        console.log('');
        
        // Test the EXACT conditions used in the service
        console.log('📋 Testing User A with EXACT service conditions:');
        
        const conditionsA = {
            $and: [
                {
                    $or: [
                        { shared_with_user: new mongoose.Types.ObjectId(userA._id) },
                        { 
                            shared_with_email: userA.email,
                            shared_with_user: null
                        }
                    ]
                },
                {
                    is_active: true,
                    status: { $in: ['pending', 'accepted'] },
                    $or: [
                        { expires_at: { $gt: new Date() } },
                        { expires_at: null }
                    ]
                }
            ]
        };
        
        console.log('🔍 User A Query:');
        console.log(JSON.stringify(conditionsA, null, 2));
        
        const userAShares = await ComponentPrivateShares.find(conditionsA).lean();
        console.log(`📊 User A found: ${userAShares.length} shares`);
        
        if (userAShares.length > 0) {
            console.log('   First few shares:');
            userAShares.slice(0, 3).forEach((share, index) => {
                console.log(`   ${index + 1}. ID: ${share._id}`);
                console.log(`      Type: ${share.access_type}, Status: ${share.status}`);
                console.log(`      Shared with user: ${share.shared_with_user || 'NULL'}`);
                console.log(`      Shared with email: ${share.shared_with_email || 'NULL'}`);
            });
        }
        
        console.log('\n📋 Testing User B with EXACT service conditions:');
        
        const conditionsB = {
            $and: [
                {
                    $or: [
                        { shared_with_user: new mongoose.Types.ObjectId(userB._id) },
                        { 
                            shared_with_email: userB.email,
                            shared_with_user: null
                        }
                    ]
                },
                {
                    is_active: true,
                    status: { $in: ['pending', 'accepted'] },
                    $or: [
                        { expires_at: { $gt: new Date() } },
                        { expires_at: null }
                    ]
                }
            ]
        };
        
        console.log('🔍 User B Query:');
        console.log(JSON.stringify(conditionsB, null, 2));
        
        const userBShares = await ComponentPrivateShares.find(conditionsB).lean();
        console.log(`📊 User B found: ${userBShares.length} shares`);
        
        if (userBShares.length > 0) {
            console.log('   First few shares:');
            userBShares.slice(0, 3).forEach((share, index) => {
                console.log(`   ${index + 1}. ID: ${share._id}`);
                console.log(`      Type: ${share.access_type}, Status: ${share.status}`);
                console.log(`      Shared with user: ${share.shared_with_user || 'NULL'}`);
                console.log(`      Shared with email: ${share.shared_with_email || 'NULL'}`);
            });
        }
        
        // Check for overlap (CRITICAL SECURITY TEST)
        console.log('\n🔒 SECURITY CHECK - Overlap Analysis:');
        const userAShareIds = userAShares.map(s => s._id.toString());
        const userBShareIds = userBShares.map(s => s._id.toString());
        const overlap = userAShareIds.filter(id => userBShareIds.includes(id));
        
        console.log(`   User A share IDs: [${userAShareIds.slice(0, 3).join(', ')}${userAShareIds.length > 3 ? '...' : ''}]`);
        console.log(`   User B share IDs: [${userBShareIds.slice(0, 3).join(', ')}${userBShareIds.length > 3 ? '...' : ''}]`);
        console.log(`   Overlapping shares: ${overlap.length}`);
        
        if (overlap.length === 0) {
            console.log('   ✅ SECURE: No overlap between users - isolation working correctly');
        } else {
            console.log('   ❌ CRITICAL SECURITY ISSUE: Users can see each other\'s shares!');
            console.log(`   Overlapping share IDs: ${overlap}`);
            
            // Analyze the overlapping shares
            console.log('\n🔍 Analyzing overlapping shares:');
            for (const shareId of overlap.slice(0, 3)) {
                const share = await ComponentPrivateShares.findById(shareId).lean();
                console.log(`   Share ${shareId}:`);
                console.log(`     Type: ${share.access_type}`);
                console.log(`     Status: ${share.status}`);
                console.log(`     Shared with user: ${share.shared_with_user}`);
                console.log(`     Shared with email: ${share.shared_with_email}`);
                console.log(`     Created: ${share.created_at}`);
                console.log(`     Active: ${share.is_active}`);
                console.log('');
            }
        }
        
        // Additional check: Look for problematic shares
        console.log('\n🔍 Checking for problematic shares (no user/email assignment):');
        const problematicShares = await ComponentPrivateShares.find({
            shared_with_user: null,
            shared_with_email: null,
            is_active: true,
            status: { $in: ['pending', 'accepted'] }
        }).lean();
        
        console.log(`   Found ${problematicShares.length} shares with no user/email assignment`);
        
        if (problematicShares.length > 0) {
            console.log('   ⚠️  These shares could be causing security issues:');
            problematicShares.slice(0, 5).forEach((share, index) => {
                console.log(`   ${index + 1}. ID: ${share._id}, Type: ${share.access_type}, Status: ${share.status}`);
            });
        }
        
        console.log('\n🎉 User isolation test completed!');
        
    } catch (error) {
        console.error('❌ Error during test:', error);
    } finally {
        mongoose.connection.close();
        console.log('\n✅ Database connection closed');
    }
}

testUserIsolation();
