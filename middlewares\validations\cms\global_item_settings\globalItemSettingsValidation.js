const { updateGlobalItemSettings } = require('../../../../validations/cms/global_item_settings/globalItemSettingsValidation');

class UpdateGlobalItemSettingsMiddleware {
    updateGlobalItemSettingsValidation(req, res, next) {
        const { value, error } = updateGlobalItemSettings(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

}
module.exports = new UpdateGlobalItemSettingsMiddleware();