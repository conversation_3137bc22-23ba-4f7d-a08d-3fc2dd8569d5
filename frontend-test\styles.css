/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Auth Status */
.auth-status {
    background: white;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Navigation */
.navigation {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.test-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.test-section:last-child {
    border-bottom: none;
}

.test-section h3 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

/* Buttons */
.button-group {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

button:active {
    transform: translateY(0);
}

button.secondary {
    background: #718096;
    box-shadow: 0 4px 15px rgba(113, 128, 150, 0.3);
}

button.danger {
    background: linear-gradient(135deg, #fc8181 0%, #f56565 100%);
    box-shadow: 0 4px 15px rgba(245, 101, 101, 0.3);
}

button.success {
    background: linear-gradient(135deg, #68d391 0%, #48bb78 100%);
    box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

/* Input Groups */
.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #4a5568;
}

.input-group input,
.input-group textarea,
.input-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus,
.input-group textarea:focus,
.input-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-row {
    display: flex;
    gap: 15px;
    align-items: end;
}

.input-row .input-group {
    flex: 1;
    margin-bottom: 0;
}

/* API Config */
.api-config {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.api-config h3 {
    color: #4a5568;
    margin-bottom: 15px;
}

/* Logs */
.logs {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.logs h3 {
    color: #4a5568;
    margin-bottom: 15px;
}

#logContainer {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.log-entry {
    margin-bottom: 8px;
    padding: 5px;
    border-radius: 4px;
}

.log-entry.success {
    background: #f0fff4;
    color: #22543d;
    border-left: 4px solid #48bb78;
}

.log-entry.error {
    background: #fed7d7;
    color: #742a2a;
    border-left: 4px solid #f56565;
}

.log-entry.warning {
    background: #fefcbf;
    color: #744210;
    border-left: 4px solid #ed8936;
}

.log-entry.info {
    background: #ebf8ff;
    color: #2a4365;
    border-left: 4px solid #4299e1;
}

/* Cards */
.card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

/* Form Styles */
.form-container {
    max-width: 500px;
    margin: 0 auto;
}

.form-header {
    text-align: center;
    margin-bottom: 30px;
}

.form-header h1 {
    color: white;
    font-size: 2rem;
    margin-bottom: 10px;
}

.form-header p {
    color: rgba(255,255,255,0.9);
}

/* Component Grid */
.component-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.component-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.component-card:hover {
    transform: translateY(-5px);
}

.component-card h3 {
    color: #4a5568;
    margin-bottom: 10px;
}

.component-card p {
    color: #718096;
    margin-bottom: 15px;
}

.component-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #718096;
}

/* Status Badges */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.accepted {
    background: #c6f6d5;
    color: #22543d;
}

.status-badge.pending {
    background: #fefcbf;
    color: #744210;
}

.status-badge.expired {
    background: #fed7d7;
    color: #742a2a;
}

/* Access Type Indicators */
.access-type {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 10px;
    display: inline-block;
}

.access-type.public-link {
    background: #e6fffa;
    color: #234e52;
    border: 1px solid #81e6d9;
}

.access-type.private-invite {
    background: #fef5e7;
    color: #744210;
    border: 1px solid #f6e05e;
}

.access-type.requires-payment {
    background: #fed7d7;
    color: #742a2a;
    border: 1px solid #fc8181;
}

/* Loading States */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .button-group {
        flex-direction: column;
    }

    .input-row {
        flex-direction: column;
    }

    .component-grid {
        grid-template-columns: 1fr;
    }
}
