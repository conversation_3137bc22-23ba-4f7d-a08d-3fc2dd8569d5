// Test script to verify private share security fixes
require('dotenv').config();
const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

const { ComponentPrivateShares } = require('./models/component_private_shares.model');
const { Users } = require('./models/users.model');
const { Components } = require('./models/component.model');

async function testPrivateShareSecurity() {
    try {
        console.log('🔍 Testing Private Share Security Fixes...\n');
        
        // Test 1: Verify user isolation in shared-with-me
        console.log('📋 Test 1: User Isolation in Shared Components');
        
        // Find two different users
        const users = await Users.find({ is_verified: true }).limit(2).lean();
        if (users.length < 2) {
            console.log('❌ Need at least 2 verified users to test isolation');
            return;
        }
        
        const userA = users[0];
        const userB = users[1];
        
        console.log(`👤 User A: ${userA.first_name} ${userA.last_name} (${userA.email})`);
        console.log(`👤 User B: ${userB.first_name} ${userB.last_name} (${userB.email})`);
        
        // Check shares for User A
        const userAShares = await ComponentPrivateShares.find({
            $or: [
                { shared_with_user: userA._id },
                { shared_with_email: userA.email }
            ],
            is_active: true,
            status: { $in: ['pending', 'accepted'] }
        }).lean();
        
        // Check shares for User B
        const userBShares = await ComponentPrivateShares.find({
            $or: [
                { shared_with_user: userB._id },
                { shared_with_email: userB.email }
            ],
            is_active: true,
            status: { $in: ['pending', 'accepted'] }
        }).lean();
        
        console.log(`📊 User A has ${userAShares.length} shares`);
        console.log(`📊 User B has ${userBShares.length} shares`);
        
        // Verify no overlap (security check)
        const userAShareIds = userAShares.map(s => s._id.toString());
        const userBShareIds = userBShares.map(s => s._id.toString());
        const overlap = userAShareIds.filter(id => userBShareIds.includes(id));
        
        if (overlap.length === 0) {
            console.log('✅ User isolation working correctly - no shared access');
        } else {
            console.log(`❌ Security issue: ${overlap.length} shares accessible by both users`);
        }
        
        console.log('\n' + '='.repeat(60) + '\n');
        
        // Test 2: Verify link generation doesn't assign users
        console.log('📋 Test 2: Link Generation User Assignment');
        
        const linkShares = await ComponentPrivateShares.find({
            access_type: 'by_link',
            is_active: true
        }).limit(5).lean();
        
        console.log(`🔗 Found ${linkShares.length} link-based shares`);
        
        let correctLinks = 0;
        let incorrectLinks = 0;
        
        linkShares.forEach((share, index) => {
            const hasUser = share.shared_with_user !== null;
            const hasEmail = share.shared_with_email !== null && share.shared_with_email !== '';
            
            console.log(`Link ${index + 1}:`);
            console.log(`  - Has User: ${hasUser ? '❌ YES (should be null)' : '✅ NO'}`);
            console.log(`  - Has Email: ${hasEmail ? '❌ YES (should be null)' : '✅ NO'}`);
            console.log(`  - Status: ${share.status}`);
            console.log(`  - Token: ${share.access_token.substring(0, 8)}...`);
            
            if (!hasUser && !hasEmail) {
                correctLinks++;
            } else {
                incorrectLinks++;
            }
        });
        
        console.log(`\n📊 Summary: ${correctLinks} correct, ${incorrectLinks} incorrect link assignments`);
        
        if (incorrectLinks === 0) {
            console.log('✅ Link generation working correctly');
        } else {
            console.log('❌ Link generation has issues - some links have user/email assigned');
        }
        
        console.log('\n' + '='.repeat(60) + '\n');
        
        // Test 3: Check for orphaned shares (shares without proper user/email assignment)
        console.log('📋 Test 3: Orphaned Shares Detection');
        
        const orphanedShares = await ComponentPrivateShares.find({
            access_type: 'by_invite',
            shared_with_user: null,
            shared_with_email: null,
            is_active: true
        }).lean();
        
        console.log(`🔍 Found ${orphanedShares.length} orphaned invite shares`);
        
        if (orphanedShares.length === 0) {
            console.log('✅ No orphaned invite shares found');
        } else {
            console.log('❌ Found orphaned invite shares - these should have user or email');
            orphanedShares.forEach((share, index) => {
                console.log(`  ${index + 1}. Share ID: ${share._id}, Status: ${share.status}`);
            });
        }
        
        console.log('\n' + '='.repeat(60) + '\n');
        
        // Test 4: Verify proper share status distribution
        console.log('📋 Test 4: Share Status Distribution');
        
        const statusCounts = await ComponentPrivateShares.aggregate([
            { $match: { is_active: true } },
            { $group: { _id: '$status', count: { $sum: 1 } } },
            { $sort: { count: -1 } }
        ]);
        
        console.log('📊 Share Status Distribution:');
        statusCounts.forEach(status => {
            console.log(`  ${status._id}: ${status.count} shares`);
        });
        
        const accessTypeCounts = await ComponentPrivateShares.aggregate([
            { $match: { is_active: true } },
            { $group: { _id: '$access_type', count: { $sum: 1 } } },
            { $sort: { count: -1 } }
        ]);
        
        console.log('\n📊 Access Type Distribution:');
        accessTypeCounts.forEach(type => {
            console.log(`  ${type._id}: ${type.count} shares`);
        });
        
        console.log('\n' + '='.repeat(60) + '\n');
        
        // Test 5: Sample aggregation query test (like the API uses)
        console.log('📋 Test 5: Sample API Query Test');
        
        if (userA) {
            const sampleQuery = [
                {
                    $match: {
                        $or: [
                            { shared_with_user: userA._id },
                            { shared_with_email: userA.email }
                        ],
                        is_active: true,
                        status: { $in: ['pending', 'accepted'] },
                        $or: [
                            { expires_at: { $gt: new Date() } },
                            { expires_at: null }
                        ]
                    }
                },
                { $count: "total" }
            ];
            
            const result = await ComponentPrivateShares.aggregate(sampleQuery);
            const count = result[0]?.total || 0;
            
            console.log(`🔍 Sample query for User A returned ${count} shares`);
            console.log('✅ Query structure matches fixed API implementation');
        }
        
        console.log('\n🎉 Security test completed!');
        
    } catch (error) {
        console.error('❌ Error during security test:', error);
    } finally {
        mongoose.connection.close();
        console.log('\n✅ Database connection closed');
    }
}

testPrivateShareSecurity();
