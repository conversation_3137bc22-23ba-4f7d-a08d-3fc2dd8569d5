const logger = require('../config/logger');

const Settings = require('../models/settings.model').Settings;
const GitlabUsers = require('../models/gitlab_users.model').GitlabUsers;
const GitlabUserTokens = require('../models/gitlab_user_tokens.model').GitlabUserTokens;
const Users = require('../models/users.model').Users;
const GlobalItemSettings = require('../models/global_item_settings.model').GlobalItemSettings;
const UserItemSettings = require('../models/user_item_settings.model').UserItemSettings;

const { decryptDataWithAES } = require('./general.helper');

/**
 * Retrieves the configuration for welcome bonus coins.
 * @returns {Promise<number>} The number of welcome bonus coins configured.
 */
const getWelcomeBonusCoinsConfig = async () => {
    try {
        // Find the settings document for welcome bonus coins
        const settings = await Settings.findOne({ setting_slug: 'welcome_bonus_coins' }).lean();

        // Return the configured number of bonus coins or 0 if not found

        return {
            bonus_coins: settings.values.bonus_coins || 0,
            expiry_duration: settings.values.expiry_duration || 30
        };
    } catch (err) {
        // Log any errors that occur during the retrieval
        logger.error(`Error at settings service from function getWelcomeBonusCoinsConfig${err}`);
        throw err;
    }
};

// Function to retrieve the GitLab master admin's access token.
const getGitlabMasterAccessToken = async () => {
    try {
        // Find the GitLab admin user and select only the personal access tokens.
        const gitlabUser = await GitlabUsers.findOne({ is_super_admin: true }, '_id').lean();
        // If the GitLab admin user is found, decrypt and return the access token.
        if (gitlabUser) {
            const gitlabToken = await GitlabUserTokens.findOne({ user_id: gitlabUser._id }).lean();
            return decryptDataWithAES(gitlabToken.personal_access_tokens);
        }
        // If no admin user is found, return null or handle accordingly.
        return null;
    } catch (err) {
        // Log any errors that occur during the retrieval process.
        logger.error(`Error at settings service from function getGitlabMasterAccessToken: ${err}`);
        throw err;
    }
};

const getUserGitlabAccessToken = async (user_id) => {
    try {
        // Find the GitLab admin user and select only the personal access tokens.
        const gitlabUser = await GitlabUsers.findOne({ _id: user_id }, '_id').lean();
        // If the GitLab admin user is found, decrypt and return the access token.
        if (gitlabUser) {
            const gitlabToken = await GitlabUserTokens.findOne({ user_id: gitlabUser._id }).sort({ created_at: -1 }).lean();
            return decryptDataWithAES(gitlabToken.personal_access_tokens);
        }
        // If no admin user is found, return null or handle accordingly.
        return null;
    } catch (err) {
        // Log any errors that occur during the retrieval process.
        logger.error(`Error at settings service from function getUserGitlabAccessToken: ${err}`);
        throw err;
    }
};

const getParityConfig = async () => {
    try {
        // Find the settings document for mpn_parity
        const settings = await Settings.findOne({ setting_slug: 'mpn_parity' }).lean();

        // Return the configured number of bonus mpn_parity or 0 if not found

        return settings.values || 0;
    } catch (err) {
        // Log any errors that occur during the retrieval
        logger.error(`Error at settings service from function getParityConfig ${err}`);
        throw err;
    }
};

const getBuyerFeeConfig = async (userId, itemType) => {
    try {
        const [global, userOverride] = await Promise.all([
            GlobalItemSettings.findOne({ item_type: itemType }).lean(),
            UserItemSettings.findOne({ item_type: itemType, user_id: userId }).lean()
        ]);
        const percentage = userOverride?.fees?.buyer?.percentage ?? global?.fees?.buyer?.percentage ?? 2;
        const fixed_min = userOverride?.fees?.buyer?.fixed_min ?? global?.fees?.buyer?.fixed_min ?? 4;
        return { percentage, fixed_min };
    } catch (err) {
        // Log any errors that occur during the retrieval
        console.error(`[getBuyerFeeConfig] Error for user ${userId} and itemType ${itemType}:`, err);
        throw err;
    }
};

const getAuthorFeeConfig = async (userId, itemType) => {
    try {
        const [global, userOverride] = await Promise.all([
            GlobalItemSettings.findOne({ item_type: itemType }).lean(),
            UserItemSettings.findOne({ item_type: itemType, user_id: userId }).lean()
        ]);
        const percentage = userOverride?.fees?.author?.percentage ?? global?.fees?.author?.percentage ?? 30;
        return percentage;
    } catch (err) {
        // Log any errors that occur during the retrieval
        console.error(`[getAuthorFeeConfig] Error for user ${userId} and itemType ${itemType}:`, err);
        throw err;
    }
};

const getItemPricingTiers = async (userId, itemType) => {
    try {
        const [global, userOverride] = await Promise.all([
            GlobalItemSettings.findOne({ item_type: itemType }).lean(),
            UserItemSettings.findOne({ item_type: itemType, user_id: userId }).lean()
        ]);
        const min = userOverride?.price?.min ?? global?.price?.min ?? 1;
        const max = userOverride?.price?.max ?? global?.price?.max ?? 99;
        return { min, max };
    } catch (err) {
        console.error(`[getItemPricingTiers] Error for user ${userId} and itemType ${itemType}:`, err);
        throw err;
    }
};


module.exports = {
    getWelcomeBonusCoinsConfig,
    getGitlabMasterAccessToken,
    getUserGitlabAccessToken,
    getParityConfig,
    getBuyerFeeConfig,
    getAuthorFeeConfig,
    getItemPricingTiers
};