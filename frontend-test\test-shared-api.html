<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Shared API</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="form-header">
                <h1>🧪 Test Shared Components API</h1>
                <p>Testing the shared-with-me API endpoint</p>
            </div>

            <div class="card">
                <h3>API Test Results</h3>
                <div class="button-group">
                    <button onclick="testSharedAPI()" class="success">Test Shared API</button>
                    <button onclick="testStatisticsAPI()" class="primary">Test Statistics API</button>
                    <button onclick="clearLogs()" class="secondary">Clear Logs</button>
                </div>
                
                <div id="results" style="margin-top: 20px;"></div>
            </div>

            <div class="logs">
                <h3>📋 API Response Logs</h3>
                <div id="logContainer"></div>
            </div>
        </div>
    </div>

    <script src="config.js"></script>
    <script>
        const API_BASE_URL = 'http://localhost:7898/api/front';
        
        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type}] ${message}`);
        }
        
        function showResult(title, success, data) {
            const resultsDiv = document.getElementById('results');
            const resultClass = success ? 'success' : 'error';
            const icon = success ? '✅' : '❌';
            
            const resultHtml = `
                <div class="test-result ${resultClass}" style="margin: 10px 0; padding: 15px; border-radius: 5px; background: ${success ? '#f0fff4' : '#fed7d7'};">
                    <h4>${icon} ${title}</h4>
                    <pre style="background: #f7fafc; padding: 10px; border-radius: 5px; overflow-x: auto; white-space: pre-wrap;">${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
            
            resultsDiv.innerHTML += resultHtml;
        }
        
        async function testSharedAPI() {
            logMessage('Testing shared components API...', 'info');
            
            const authToken = localStorage.getItem('authToken');
            if (!authToken) {
                showResult('Authentication Check', false, { error: 'No auth token found. Please login first.' });
                return;
            }
            
            try {
                const requestBody = {
                    skip: 0,
                    limit: 12
                };

                const response = await fetch(`${API_BASE_URL}/v1/component/shared-with-me`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`,
                        'token': authToken
                    },
                    credentials: 'include',
                    body: JSON.stringify(requestBody)
                });
                
                logMessage(`Response status: ${response.status}`, 'info');
                logMessage(`Response headers: ${JSON.stringify([...response.headers.entries()], null, 2)}`, 'info');
                
                const data = await response.json();
                logMessage(`Response data: ${JSON.stringify(data, null, 2)}`, 'info');
                
                if (response.ok && data.status === 200) {
                    const components = data.data.list || [];
                    showResult('Shared Components API', true, {
                        status: data.status,
                        message: data.message,
                        totalRecords: data.data.recordsTotal,
                        filteredRecords: data.data.recordsFiltered,
                        componentsFound: components.length,
                        dataKeys: Object.keys(data.data),
                        firstComponent: components[0] || null
                    });
                } else {
                    showResult('Shared Components API', false, data);
                }
            } catch (error) {
                logMessage(`API test error: ${error.message}`, 'error');
                showResult('Shared Components API', false, { error: error.message, stack: error.stack });
            }
        }
        
        async function testStatisticsAPI() {
            logMessage('Testing statistics API...', 'info');
            
            const authToken = localStorage.getItem('authToken');
            if (!authToken) {
                showResult('Statistics API', false, { error: 'No auth token found. Please login first.' });
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/v1/component/shared-with-me/statistics`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`,
                        'token': authToken
                    },
                    credentials: 'include'
                });
                
                const data = await response.json();
                logMessage(`Statistics response: ${JSON.stringify(data, null, 2)}`, 'info');
                
                if (response.ok && data.status === 200) {
                    showResult('Statistics API', true, data.data);
                } else {
                    showResult('Statistics API', false, data);
                }
            } catch (error) {
                logMessage(`Statistics API error: ${error.message}`, 'error');
                showResult('Statistics API', false, { error: error.message });
            }
        }
        
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            document.getElementById('results').innerHTML = '';
        }
        
        // Show auth status on load
        document.addEventListener('DOMContentLoaded', function() {
            const authToken = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');
            
            logMessage(`Auth token: ${authToken ? 'Present' : 'Missing'}`, authToken ? 'success' : 'warning');
            logMessage(`User info: ${userInfo ? 'Present' : 'Missing'}`, userInfo ? 'success' : 'warning');
            
            if (authToken && userInfo) {
                const user = JSON.parse(userInfo);
                logMessage(`Logged in as: ${user.first_name} ${user.last_name} (${user.email})`, 'info');
            }
        });
    </script>
</body>
</html>
