{"name": "private-share-frontend-test", "version": "1.0.0", "description": "Frontend test environment for private share functionality", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"Open http://localhost:8080 to test the frontend\" && npm start"}, "keywords": ["private-share", "frontend", "test", "component-sharing"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "your-repo-url"}, "dependencies": {"nodemon": "^3.1.10"}}