const logger = require('../config/logger');
const MailService = require('./email_helper.service');
const EmailTemplates = require('../models/email_templates.model').EmailTemplates;
const Users = require('../models/users.model').Users;

async function sendUserVerificationEmail(email, otp, first_name) {
    try {

        const slug = 'user-email-verification';

        const emailTemplate = await EmailTemplates.findOne({ slug: slug }).lean();

        if (!emailTemplate) {
            logger.error(`Email template not found for slug: ${slug}`);
            return;
        }

        let emailContent = emailTemplate.template_description;

        emailContent = emailContent.replace('{{product_name}}', process.env.PRODUCT_NAME);
        emailContent = emailContent.replace('{{OTP}}', otp);
        emailContent = emailContent.replace('{{first_name}}', first_name);
        emailContent = emailContent.replace(/{{support_image_url}}/g, `${process.env.AWS_STATIC_CONTENT_URL}email/help.png`);
        // send email using helper function
        const emailData = {
            template: 'email_layout/layout.ejs',
            email: email,
            template_description: emailContent,
            subject: emailTemplate.subject,
            template_banner: emailTemplate.template_banner,
            slug: slug
        };
        await MailService.sendEmailWithTemplate(emailData);
    } catch (error) {
        logger.error(`error at sendNewsLetterVerificationEmail${error}`);
        throw error;
    }
}

async function sendUserLoginOtpEmail(email, otp, first_name) {
    try {

        const slug = 'user-login-otp-verification';

        const emailTemplate = await EmailTemplates.findOne({ slug: slug }).lean();

        if (!emailTemplate) {
            logger.error(`Email template not found for slug: ${slug}`);
            return;
        }

        let emailContent = emailTemplate.template_description;
        const otpExpireLength = parseInt(process.env.OTP_EXPIRE_LENGTH);

        emailContent = emailContent.replace('{{product_name}}', process.env.PRODUCT_NAME);
        emailContent = emailContent.replace('{{OTP}}', otp);
        emailContent = emailContent.replace('{{first_name}}', first_name);
        emailContent = emailContent.replace('{{expire_time}}', otpExpireLength);
        emailContent = emailContent.replace(/{{support_image_url}}/g, `${process.env.AWS_STATIC_CONTENT_URL}email/help.png`);

        // send email using helper function
        const emailData = {
            template: 'email_layout/layout.ejs',
            email: email,
            template_description: emailContent,
            subject: emailTemplate.subject,
            template_banner: emailTemplate.template_banner,
            slug: slug
        };
        await MailService.sendEmailWithTemplate(emailData);
    } catch (error) {
        logger.error(`error at sendNewsLetterVerificationEmail${error}`);
        throw error;
    }
}

async function sendSubAdminWelcomeEmail(full_name, email, password) {
    try {
        const slug = 'cms-sub-admin-welcome-email';

        const emailTemplate = await EmailTemplates.findOne({ slug }).lean();

        if (!emailTemplate) {
            console.log(`Email template not found for slug: ${slug}`);
            return;
        }

        const emailContent = emailTemplate.template_description
            .replace('{{full_name}}', full_name || 'user')
            .replace('{{password}}', password)
            .replace(/{{cms_front_url}}/g, process.env.CMS_FRONT_URL)
            .replace(/{{support_image_url}}/g, `${process.env.AWS_STATIC_CONTENT_URL}email/help.png`)
            .replace('{{product_name}}', process.env.PRODUCT_NAME);

        const emailData = {
            template: 'email_layout/layout.ejs',
            email,
            template_description: emailContent,
            subject: emailTemplate.subject,
            template_banner: emailTemplate.template_banner,
            slug
        };

        await MailService.sendEmailWithTemplate(emailData);
    } catch (error) {
        console.log('error at sendJobApplicationAckEmail', error);
        throw error;
    }
}

async function sendUpdateEmailOtp(email, otp, username) {
    try {

        const slug = 'user-update-email-otp-verification';

        const emailTemplate = await EmailTemplates.findOne({ slug: slug }).lean();

        if (!emailTemplate) {
            logger.error(`Email template not found for slug: ${slug}`);
            return;
        }

        let emailContent = emailTemplate.template_description;
        const otpExpireLength = parseInt(process.env.OTP_EXPIRE_LENGTH);

        emailContent = emailContent.replace('{{product_name}}', process.env.PRODUCT_NAME);
        emailContent = emailContent.replace('{{OTP}}', otp);
        emailContent = emailContent.replace('{{username}}', username);
        emailContent = emailContent.replace('{{expire_time}}', otpExpireLength);
        emailContent = emailContent.replace(/{{support_image_url}}/g, `${process.env.AWS_STATIC_CONTENT_URL}email/help.png`);

        // send email using helper function
        const emailData = {
            template: 'email_layout/layout.ejs',
            email: email,
            template_description: emailContent,
            subject: emailTemplate.subject,
            template_banner: emailTemplate.template_banner,
            slug: slug
        };
        await MailService.sendEmailWithTemplate(emailData);
    } catch (error) {
        logger.error(`error at sendNewsLetterVerificationEmail${error}`);
        throw error;
    }
}

async function sendNewsLetterVerificationEmail(email, subscribeToken) {
    try {

        const slug = 'newsletter-email-verification';

        const emailTemplate = await EmailTemplates.findOne({ slug: slug }).lean();

        if (!emailTemplate) {
            console.log(`Email template not found for slug: ${slug}`);
            return;
        }

        let emailContent = emailTemplate.template_description;

        emailContent = emailContent.replace(
            '{{subs_verification_link}}',
            `${process.env.SITE_URL}verify-newsletter-subscription/${subscribeToken}`
        );
        emailContent = emailContent.replace('{{product_name}}', process.env.PRODUCT_NAME);
        // send email using helper function
        const emailData = {
            template: 'email_layout/layout.ejs',
            email: email,
            template_description: emailContent,
            subject: emailTemplate.subject,
            template_banner: emailTemplate.template_banner,
            slug: slug
        };
        await MailService.sendEmailWithTemplate(emailData);
    } catch (error) {
        console.log('error at sendNewsLetterVerificationEmail', error);
        throw error;
    }
}

async function sendNewsLetterWelcomeEmail(email) {
    try {

        const slug = 'send-newsletter-welcome-email';

        const emailTemplate = await EmailTemplates.findOne({ slug: slug }).lean();

        if (!emailTemplate) {
            console.log(`Email template not found for slug: ${slug}`);
            return;
        }

        let emailContent = emailTemplate.template_description;
        emailContent = emailContent.replace('{{product_name}}', process.env.PRODUCT_NAME);
        // send email using helper function
        const emailData = {
            template: 'email_layout/layout.ejs',
            email: email,
            template_description: emailContent,
            subject: emailTemplate.subject,
            template_banner: emailTemplate.template_banner,
            slug: slug
        };
        await MailService.sendEmailWithTemplate(emailData);
    } catch (error) {
        console.log('error at sendNewsLetterWelcomeEmail', error);
        throw error;
    }
}

async function sendRepoInviteVerificationEmail(userId, repoDetails, email, url) {
    try {

        const slug = 'repository-invite-verification';

        const emailTemplate = await EmailTemplates.findOne({ slug: slug }).lean();

        if (!emailTemplate) {
            logger.error(`Email template not found for slug: ${slug}`);
            return;
        }

        const currentUser = await Users.findOne({ _id: userId }, 'username').lean();

        let emailContent = emailTemplate.template_description;

        emailContent = emailContent.replace('{{product_name}}', process.env.PRODUCT_NAME);
        emailContent = emailContent.replace('{{invite_url}}', url);
        emailContent = emailTemplate.replace('{{repository_name}}', repoDetails?.project_name);
        emailContent = emailContent.replace('{{username}}', currentUser.username);

        // send email using helper function
        const emailData = {
            template: 'email_layout/layout.ejs',
            email: email,
            template_description: emailContent,
            subject: emailTemplate.subject,
            template_banner: emailTemplate.template_banner,
            slug: slug
        };

        console.log('emailData', emailData);
        await MailService.sendEmailWithTemplate(emailData);
    } catch (error) {
        logger.error(`error at sendRepoInviteVerificationEmail${error}`);
        throw error;
    }
}

async function sendPrivateShareInvitationEmail(email, sharer, component, accessToken, personalMessage, expiresAt) {
    try {
        // Check if recipient is already a user
        const existingUser = await Users.findOne({ email: email }).lean();
        
        const slug = existingUser ? 'component-private-share-existing-user' : 'component-private-share-new-user';
        
        const emailTemplate = await EmailTemplates.findOne({ slug: slug }).lean();

        if (!emailTemplate) {
            logger.error(`Email template not found for slug: ${slug}`);
            return;
        }

        const sharerName = `${sharer.first_name} ${sharer.last_name}`.trim() || sharer.username;
        // Use frontend URL for direct handling
        const accessUrl = `${process.env.SITE_URL}private-share/${accessToken}`;
        const signupUrl = `${process.env.SITE_URL}auth/signup?returnUrl=${encodeURIComponent(`private-share/${accessToken}`)}`;
        
        let emailContent = emailTemplate.template_description;

        // Common replacements
        emailContent = emailContent.replace(/{{product_name}}/g, process.env.PRODUCT_NAME);
        emailContent = emailContent.replace(/{{sharer_name}}/g, sharerName);
        emailContent = emailContent.replace(/{{component_title}}/g, component.title);
        emailContent = emailContent.replace(/{{expires_date}}/g, new Date(expiresAt).toLocaleDateString());
        emailContent = emailContent.replace(/{{personal_message}}/g, personalMessage || '');
        emailContent = emailContent.replace(/{{support_image_url}}/g, `${process.env.AWS_STATIC_CONTENT_URL}email/help.png`);

        if (existingUser) {
            // For existing users - direct access link
            emailContent = emailContent.replace(/{{access_url}}/g, accessUrl);
            emailContent = emailContent.replace(/{{recipient_name}}/g, `${existingUser.first_name} ${existingUser.last_name}`.trim() || existingUser.username);
        } else {
            // For new users - both signup and direct access links
            emailContent = emailContent.replace(/{{signup_url}}/g, signupUrl);
            emailContent = emailContent.replace(/{{access_url}}/g, accessUrl);
        }

        const emailData = {
            template: 'email_layout/layout.ejs',
            email: email,
            template_description: emailContent,
            subject: emailTemplate.subject.replace('{{sharer_name}}', sharerName).replace('{{component_title}}', component.title),
            template_banner: emailTemplate.template_banner,
            slug: slug
        };

        await MailService.sendEmailWithTemplate(emailData);
        
    } catch (error) {
        logger.error(`error at sendPrivateShareInvitationEmail: ${error}`);
        throw error;
    }
}

async function sendPrivateShareAcceptedNotification(sharerEmail, recipientName, componentTitle) {
    try {
        const slug = 'component-private-share-accepted';
        
        const emailTemplate = await EmailTemplates.findOne({ slug: slug }).lean();

        if (!emailTemplate) {
            logger.error(`Email template not found for slug: ${slug}`);
            return;
        }

        let emailContent = emailTemplate.template_description;

        emailContent = emailContent.replace(/{{product_name}}/g, process.env.PRODUCT_NAME);
        emailContent = emailContent.replace(/{{recipient_name}}/g, recipientName);
        emailContent = emailContent.replace(/{{component_title}}/g, componentTitle);
        emailContent = emailContent.replace(/{{support_image_url}}/g, `${process.env.AWS_STATIC_CONTENT_URL}email/help.png`);

        const emailData = {
            template: 'email_layout/layout.ejs',
            email: sharerEmail,
            template_description: emailContent,
            subject: emailTemplate.subject.replace('{{component_title}}', componentTitle),
            template_banner: emailTemplate.template_banner,
            slug: slug
        };

        await MailService.sendEmailWithTemplate(emailData);
        
    } catch (error) {
        logger.error(`error at sendPrivateShareAcceptedNotification: ${error}`);
        throw error;
    }
}

module.exports = {
    sendUserVerificationEmail,
    sendUserLoginOtpEmail,
    sendSubAdminWelcomeEmail,
    sendUpdateEmailOtp,
    sendNewsLetterVerificationEmail,
    sendNewsLetterWelcomeEmail,
    sendRepoInviteVerificationEmail,
    sendPrivateShareInvitationEmail,
    sendPrivateShareAcceptedNotification
};