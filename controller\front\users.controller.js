// Constants declaration
const constants = require('../../config/constants');
const { mpcBonusRequestStatus, coinTypes, componentState, filterTypes, userActions, salesFilterTypes } = require('../../config/component.constant');
const { gitlabUserScope } = require('../../config/gitlab.constant');

// Service declaration
const { ReS, sendError, generateGitlabTokenName, encryptDataWithAES, decryptDataWithAES, escapeRegex } = require('../../services/general.helper');
const { s3UploadStaticContent, getDateFilterConditions } = require('../../services/general.helper');
const filePath = require('path');
const { createRepositoryUserFromUser } = require('../../services/repository.service');
const { checkUserNameAvailability, createUserAccessToken } = require('./../../services/gitlab.helper');
const redis = require('../../config/redis');
const { setupCreatorProfile } = require('../../validations/front/users/userValidation');
const { saveAndSendUpdateEmailOTP } = require('../../services/auth.service');
const { fetchUserComponentStatistics } = require('../../services/component.service');

// Models declaration
const Users = require('../../models/users.model').Users;
const UserMpcBalance = require('../../models/user_mpc_balance.model').UserMpcBalance;
const ComponentUnlockHistory = require('../../models/component_unlock_history.model').ComponentUnlockHistory;
const UserCoinsHistory = require('../../models/user_coins_history.model').UserCoinsHistory;
const BonusPointRequest = require('../../models/mpc_bonus_point_request.model').BonusPointRequest;
const GitlabUsers = require('../../models/gitlab_users.model').GitlabUsers;
const GitlabUserTokens = require('../../models/gitlab_user_tokens.model').GitlabUserTokens;
const Components = require('../../models/component.model').Components;
const DraftComponents = require('../../models/draft_components.model').DraftComponents;
const UserOTPs = require('../../models/user_otps.model').UserOTPs;
const UserMpnPointLogs = require('../../models/user_mpn_point_logs.model').UserMpnPointLogs;
const UserFiatHistory = require('../../models/user_fiat_history.model').UserFiatHistory;
const ComponentSales = require('../../models/component_sales.model').ComponentSales;
const RepositoryStars = require('../../models/repository_star.model').RepositoryStars;
const GitlabRepository = require('../../models/gitlab_repository.model').GitlabRepository;

// Npm declaration
const mongoose = require('mongoose');
const logger = require('../../config/logger');
const moment = require('moment');

async function getUserDetails(req, res) {
    try {

        const pipelines = [
            {
                $match: {
                    _id: new mongoose.Types.ObjectId(req.session._id) // Match admin by user ID
                }
            },
            {
                $lookup: {
                    from: 'gitlab_users', // Join with gitlab_users collection
                    let: { user_id: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$user_id', '$$user_id'] }
                                    ]
                                }
                            }
                        },
                        {
                            $project: {
                                email: 1,
                                username: 1,
                                extern_uid: 1,
                                personal_access_tokens: 1,
                                personal_access_token_name: 1,
                                token_expires_at: 1 // Project required fields
                            }
                        }
                    ],
                    as: 'gitlab_user' // Output array field for the joined data
                }
            },
            {
                $unwind: {
                    path: '$gitlab_user',
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: 'gitlab_user_tokens', // Join with gitlab_user_tokens collection
                    let: { admin_id: '$gitlab_user._id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$user_id', '$$admin_id'] }
                                    ]
                                }
                            }
                        },
                        {
                            $project: {
                                personal_access_tokens: 1,
                                personal_access_token_name: 1,
                                created_at: 1,
                                token_expires_at: 1 // Project required fields
                            }
                        },
                        {
                            $sort: {
                                created_at: 1
                            }
                        }
                    ],
                    as: 'gitlab_user.access_tokens' // Output array field for the joined data
                }
            },
            {
                $lookup: {
                    from: 'components', // Join with components collection
                    let: { creatorId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$created_by_user', '$$creatorId'] },
                                        { $eq: ['$component_state', componentState.PUBLISHED] }
                                    ]
                                }
                            }
                        },
                        {
                            $group: {
                                _id: null,
                                total_posts: { $sum: 1 }
                            }
                        }
                    ],
                    as: 'components_count'
                }
            },
            {
                $lookup: {
                    from: 'supported_platforms', // Name of the collection
                    let: { techIds: '$technologies' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $in: ['$_id', '$$techIds']
                                }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                title: 1,
                                slug: 1,
                                image_url: 1
                            }
                        }
                    ],
                    as: 'technologies'
                }
            },
            {
                $addFields: {
                    published_files: {
                        $ifNull: [{ $arrayElemAt: ['$components_count.total_posts', 0] }, 0]
                    }
                }
            },
            {
                $project: {
                    components_count: 0 // Remove temporary field
                }
            }
        ];

        // Execute the aggregation pipeline
        const userProfile = await Users.aggregate(pipelines);

        if (!userProfile.length) {
            return ReS(res, constants.resource_not_found, 'Oops! User Not Found.');
        }

        // Get the first result from the aggregation
        const userProfileObj = (userProfile.length) ? userProfile[0] : null;

        // Decrypt personal access tokens if available
        if (userProfileObj?.gitlab_user?.access_tokens) {
            userProfileObj.gitlab_user.access_tokens.forEach((token) => {
                token.personal_access_tokens = decryptDataWithAES(token.personal_access_tokens);
            });
        }

        userProfileObj['component_statistics'] = await fetchUserComponentStatistics(req.session._id);

        return ReS(res, constants.success_code, 'Data Fetched', userProfileObj);
    } catch (err) {
        logger.error(`Error at Front Controller getUserDetails${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getUserMpcBalance(req, res) {
    try {
        // Filtering criteria based on user ID from session
        const filter = {
            user_id: req.session._id
        };

        // Fetching user balance from the database
        const userBalance = await UserMpcBalance.findOne(filter, {
            user_id: 1,
            mpn_points: 1,
            mpn_bonus_points: 1,
            fiat: 1
        }).lean();

        // If user balance is not found, return resource not found message
        if (userBalance == null) {
            return ReS(res, constants.resource_not_found, 'Oops! User Balance Not Found.');
        }

        // If user balance is found, return success message along with the balance data
        return ReS(res, constants.success_code, 'Data Fetched', userBalance);
    } catch (err) {
        // If any error occurs, log the error and return server error message
        logger.error(`Error at Front Controller getUserMpcBalance${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getUserComponentUnlockHistory(req, res) {
    try {
        // Filtering criteria to find unlock history based on user ID from session
        const filter = {
            unlock_by: req.session._id,
            cost_point: {
                $ne: 0
            }
        };
        const totalDocuments = await ComponentUnlockHistory.countDocuments(filter);
        // Set default sort
        const sort = {
            created_at: -1
        };
        const limit = (req.body.limit) ? req.body.limit : 12;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await ComponentUnlockHistory.countDocuments(filter);
        // Fetching component unlock history from the database based on the filter
        const unlockHistory = await ComponentUnlockHistory.find(filter, {
            component_id: 1,
            component_name: 1,
            component_slug: 1,
            cost_point: 1,
            expired_on: 1,
            created_at: 1,
            is_active: 1
        }).sort(sort).skip(skip).limit(limit).lean();

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: unlockHistory
        };
        // If component unlock history is found, return success message along with the data
        return ReS(res, constants.success_code, 'Component Unlock History Fetched', responseObj);
    } catch (err) {
        // If any error occurs, log the error and return a server error message
        logger.error(`Error at getUserComponentUnlockHistory function:${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getUnlockedComponents(req, res) {
    try {
        // Filtering criteria to find unlock history based on user ID from session
        const filter = {
            unlock_by: new mongoose.Types.ObjectId(req.session._id),
            is_active: true
        };
        const totalDocuments = await ComponentUnlockHistory.countDocuments(filter);
        // Set Default sort
        const sort = {
            created_at: -1
        };

        const limit = (req.body.limit) ? req.body.limit : 12;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await ComponentUnlockHistory.countDocuments(filter);

        const pipelines = [{
            $match: filter
        }, {
            $sort: sort
        }, {
            $skip: skip
        }, {
            $limit: limit
        }, {
            $lookup: {
                from: 'components',
                let: {
                    component_id: '$component_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$component_id']
                            }]
                        }
                    }
                }, {
                    $lookup: {
                        from: 'categories', // Join with categories collection
                        let: { categoryId: '$category_id' },
                        pipeline: [
                            {
                                $match: {
                                    $expr: { $eq: ['$_id', '$$categoryId'] }
                                }
                            },
                            {
                                $project: {
                                    category_name: 1,
                                    category_slug: 1
                                }
                            }
                        ],
                        as: 'category_id'
                    }
                }, {
                    $project: {
                        title: 1,
                        slug: 1,
                        image_url: 1,
                        thumbnail_url: 1,
                        short_description: 1,
                        long_description: 1,
                        views: 1,
                        likes: 1,
                        bookmarks: 1,
                        created_at: 1,
                        orientation: 1,
                        created_by_user: 1,
                        elements_data: 1,
                        category_id: { $arrayElemAt: ['$category_id', 0] },
                    }
                }],
                as: 'component'
            }
        }, {
            $unwind: {
                path: '$component',
                preserveNullAndEmptyArrays: true
            }
        }, {
            $lookup: {
                from: 'component_likes',
                let: {
                    liked_by: new mongoose.Types.ObjectId(req.session._id),
                    component_id: '$component_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$component_id', '$$component_id']
                            },
                            {
                                $eq: ['$liked_by', '$$liked_by']
                            }]
                        }
                    }
                }, {
                    $project: {
                        liked_by: 1,
                        component_id: 1
                    }
                }],
                as: 'component_likes'
            }
        }, {
            $lookup: {
                from: 'component_bookmarks',
                let: {
                    bookmarked_by: new mongoose.Types.ObjectId(req.session._id),
                    component_id: '$component_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$component_id', '$$component_id']
                            },
                            {
                                $eq: ['$bookmarked_by', '$$bookmarked_by']
                            }]
                        }
                    }
                }, {
                    $project: {
                        liked_by: 1,
                        component_id: 1
                    }
                }],
                as: 'component_bookmarks'
            }
        }, {
            $lookup: {
                from: 'users',
                let: {
                    user_id: '$component.created_by_user'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$user_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'email': 1,
                        'username': 1,
                        'first_name': 1,
                        'last_name': 1,
                        'avatar': 1
                    }
                }],
                as: 'created_by_user'
            }
        }, {
            $project: {
                title: '$component.title',
                slug: '$component.slug',
                component_type: '$component.component_type',
                image_url: '$component.image_url',
                thumbnail_url: '$component.thumbnail_url',
                short_description: '$component.short_description',
                long_description: '$component.long_description',
                created_at: '$component.created_at',
                orientation: '$component.orientation',
                views: '$component.views',
                likes: '$component.likes',
                bookmarks: '$component.bookmarks',
                is_active: 1,
                is_liked: {
                    $cond: { if: { $isArray: '$component_likes' }, then: { $gte: [{ $size: '$component_likes' }, 1] }, else: false }
                },
                is_bookmarked: {
                    $cond: { if: { $isArray: '$component_bookmarks' }, then: { $gte: [{ $size: '$component_bookmarks' }, 1] }, else: false }
                },
                created_by_user: {
                    $arrayElemAt: ['$created_by_user', 0]
                },
                elements_data: '$component.elements_data',
                category_id: '$component.category_id'
            }
        }];

        // Fetching component unlock history from the database based on the filter
        const componentList = await ComponentUnlockHistory.aggregate(pipelines);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList
        };

        // If component unlock history is found, return success message along with the data
        return ReS(res, constants.success_code, 'Component Unlock History Fetched', responseObj);
    } catch (err) {
        // If any error occurs, log the error and return a server error message
        logger.error(`Error at getUnlockedComponents function:${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getUnlockedComponentSlugs(req, res) {
    try {
        const userObjectId = new mongoose.Types.ObjectId(req.session._id);

        // Fetching unlocked components and authored components concurrently
        const [unlockedComponentSlugs, authoredComponentSlugs] = await Promise.all([
            ComponentUnlockHistory.distinct('component_slug', { unlock_by: userObjectId, is_active: true }),
            Components.distinct('slug', {
                created_by_user: userObjectId,
                component_state: { $in: [componentState.PUBLISHED, componentState.PRIVATE] }
            })
        ]);

        // Combine both arrays and remove duplicates
        const uniqueComponentSlugs = [...new Set([...unlockedComponentSlugs, ...authoredComponentSlugs])];

        return ReS(res, constants.success_code, 'Component Unlock History Fetched', uniqueComponentSlugs);
    } catch (err) {
        logger.error(`Error at getUnlockedComponentSlugs function: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getUserTopUpHistory(req, res) {
    try {
        // Set conditions based on user ID and optional type query
        const conditions = {
            user_id: new mongoose.Types.ObjectId(req.session._id)
        };
        const totalDocuments = await UserCoinsHistory.countDocuments(conditions);

        conditions['type'] = (req.body && req.body.type === coinTypes.BONUS) ? coinTypes.BONUS : coinTypes.NORMAL;

        // Default sort
        const sort = { created_at: -1 };
        const limit = (req.body.limit) ? req.body.limit : 12;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await UserCoinsHistory.countDocuments(conditions);

        // Define aggregation pipeline
        const pipelines = [
            { $match: conditions },
            {
                $project: {
                    user_id: 1,
                    points: 1,
                    type: 1,
                    expired_on: 1,
                    is_expired: 1,
                    created_at: 1
                }
            }];

        pipelines.push({
            '$sort': sort
        });
        pipelines.push({
            '$skip': skip
        });
        pipelines.push({
            '$limit': limit
        });

        // Aggregate user coins history
        const topUpHistory = await UserCoinsHistory.aggregate(pipelines);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: topUpHistory
        };

        // Return successful response with user top-up history
        return ReS(res, constants.success_code, 'User Top-Up History Fetched', responseObj);
    } catch (err) {
        // Log and handle errors
        console.error('Error at getUserTopUpHistory function:', err);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function uploadProfilePicture(req, res) {
    try {
        const filename = `${new Date().getTime()}${filePath.extname(req.files.image.name)}`;
        const documentPath = `ctn/multiplatform/profile/${filename}`;
        await s3UploadStaticContent(req.files.image, documentPath);
        const fullPath = `${process.env.AWS_FILE_URL}${documentPath}`;
        return ReS(res, constants.success_code, 'File Uploaded Successfully', { path: fullPath });
    } catch (err) {
        logger.error(`Error at Front Controller uploadFile${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

// Controller function to update user profile
async function updateUserProfile(req, res) {
    try {
        const {
            first_name,
            last_name,
            avatar,
            social_links = [],
            biography,
            become_creator,
            website,
            country,
            technologies = []
        } = req.body;

        // Find user data by session ID
        const userData = await Users.findById(req.session._id).lean();
        if (!userData) {
            return ReS(res, constants.resource_not_found, 'Oops! User Not Found.');
        }

        // Initialize update object dynamically
        const updateData = {
            ...(first_name && { first_name }),
            ...(last_name && { last_name }),
            ...((avatar !== undefined) && { avatar }), // Includes avatar even if it's blank or null
            ...((biography !== undefined) && { biography }), // Includes biography even if it's blank or null
            ...(social_links.length > 0 && { social_links })
        };

        // Handle become_creator logic
        if (become_creator) {
            const { error } = setupCreatorProfile({ biography, social_links, website, country, technologies });
            if (error) {
                return ReS(res, constants.bad_request_code, error.details[0].message, 'ValidationError');
            }

            Object.assign(updateData, {
                ...(website && { website }),
                ...(country && { country }),
                ...(technologies && { technologies }),
                is_creator: true
            });
        }

        // Update the user document
        await Users.updateOne({ _id: req.session._id }, { $set: updateData });

        return ReS(res, constants.success_code, 'Profile updated successfully.');
    } catch (err) {
        logger.error(`Error updating user profile: ${err.message}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}


async function submitBonusRequest(req, res) {
    try {
        // Extract user ID from the session
        const user_id = req.session._id;

        const { request_note } = req.body;

        // Check if there's a pending bonus request for the user
        const bonusRequest = await BonusPointRequest.findOne({
            user_id: user_id,
            status: mpcBonusRequestStatus.PENDING
        });

        // If a pending request exists, return with a bad request response
        if (bonusRequest) {
            return ReS(res, constants.bad_request_code, 'Request already submitted');
        }

        // Create a new bonus request for the user
        await BonusPointRequest.create({
            user_id: user_id,
            request_note: request_note
        });

        // Return success response
        return ReS(res, constants.success_code, 'Request sent Successfully');
    } catch (err) {
        // If an error occurs during execution, handle it
        logger.error(`Error at Front Controller submitBonusRequest${err}`); // Log the error
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.'); // Return server error response
    }
}

async function createGitlabUser(req, res) {
    try {
        // Extract user ID from the session
        const userId = req.session._id;

        const { username } = req.body;

        // Fetch user data from the database based on the user ID
        const userData = await Users.findOne({ _id: userId }, 'gitlab_user_exists').lean();

        // If user data is not found, return an error response
        if (!userData) {
            return ReS(res, constants.resource_not_found, 'User not found.');
        }

        // If the user exist on GitLab, return error
        if (userData && userData.gitlab_user_exists == true) {
            return ReS(res, constants.conflict_code, 'Oops! GitLab user already exists');
        }

        await createRepositoryUserFromUser(userId, username);

        // When a user creates a GitLab profile, update their status to is_creator as true
        await Users.updateOne({
            _id: userId
        }, {
            $set: {
                is_creator: true
            }
        });
        // Return success response with admin profile data
        return ReS(res, constants.success_code, 'Creator profile created successfully.', {});
    } catch (err) {
        // Log the error and return error response
        console.error('Error in createGitlabUser:', err);
        return sendError(res, err);
    }
}

async function downloadGitlabCredentials(req, res) {
    try {
        // Extract user ID from the session
        const userId = req.session._id;

        // Fetch user data from the database based on the user ID
        const userData = await Users.findOne({ _id: userId }, 'gitlab_user_exists').lean();

        // If user data is not found, return an error response
        if (!userData) {
            return ReS(res, constants.resource_not_found, 'User not found.');
        }

        // If the user does not exist on GitLab, return error
        if (!userData.gitlab_user_exists) {
            return ReS(res, constants.bad_request_code, 'Oops! User does not exist on GitLab.');
        }

        // Retrieve GitLab user data
        const gitlabUser = await GitlabUsers.findOne({
            user_id: userId
        }, 'email username gitlab_user_id').lean();

        // Retrieve last GitLab access token
        const lastAccessToken = await GitlabUserTokens.findOne({
            user_id: gitlabUser._id
        }).sort({
            created_at: -1
        }).lean();

        if (!lastAccessToken) {
            return ReS(res, constants.bad_request_code, 'Oops! User does not contains any active access token');
        }

        // Decrypt the personal access tokens
        const accessTokens = decryptDataWithAES(lastAccessToken.personal_access_tokens);

        // Create the content for the file
        const fileContent = `MPN Creator Email: ${gitlabUser.email}\nMPN Creator Username: ${gitlabUser.username}\nAccess Token: ${accessTokens}\nToken Expires At: ${moment(new Date(lastAccessToken.token_expires_at)).format('DD/MM/YYYY hh:mm A')}`;

        // Set the file name using the GitLab username
        const fileName = `${gitlabUser.username}.txt`;

        // Set response headers for file download
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
        res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);
        res.setHeader('Content-Type', 'text/plain');

        // Send the file content
        res.send(fileContent);
    } catch (err) {
        // Log the error and return error response
        console.error('Error in downloadGitlabCredentials:', err);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function checkGitlabUserNameAvailability(req, res) {
    const { username } = req.query;
    const isAvailable = await checkUserNameAvailability(username);
    // Return success response with admin profile data
    return ReS(res, constants.success_code, 'User profile retrieved successfully.', { isAvailable });
}

async function getUserGitlabCredentials(req, res) {
    try {
        // Extract user ID from the session
        const userId = req.session._id;

        // Fetch user data from the database based on the user ID
        const userData = await Users.findOne({ _id: userId }, 'gitlab_user_exists').lean();

        // If user data is not found, return an error response
        if (!userData) {
            return ReS(res, constants.resource_not_found, 'User not found.');
        }

        // If the user does not exist on GitLab, return error
        if (!userData.gitlab_user_exists) {
            return ReS(res, constants.bad_request_code, 'Oops! User does not exist on GitLab.');
        }

        // Retrieve GitLab user data
        const gitlabUser = await GitlabUsers.findOne({
            user_id: userId
        }, 'email username gitlab_user_id').lean();

        // Retrieve last GitLab access token
        const lastAccessToken = await GitlabUserTokens.findOne({
            user_id: gitlabUser._id
        }).sort({
            created_at: -1
        }).lean();

        if (!lastAccessToken) {
            return ReS(res, constants.bad_request_code, 'Oops! User does not contains any active access token');
        }

        // Decrypt the personal access tokens
        const accessTokens = decryptDataWithAES(lastAccessToken.personal_access_tokens);

        return ReS(res, constants.success_code, 'Creator profile fetched successfully.', {
            email: gitlabUser.email,
            username: gitlabUser.username,
            personal_access_tokens: accessTokens,
            token_expires_at: lastAccessToken.token_expires_at,
            personal_access_token_name: lastAccessToken.personal_access_token_name
        });
    } catch (err) {
        // Log the error and return error response
        console.error('Error in getUserGitlabCredentials:', err);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getProfileStatistics(req, res) {
    try {
        // Extract user ID from the session
        const userId = req.session._id;

        // Fetch user data from the database based on the user ID
        const userData = await Users.findOne({
            _id: userId
        }, 'gitlab_user_exists').populate({
            'path': 'technologies',
            'select': 'title slug image_url'
        }).lean();

        // If user data is not found, return an error response
        if (!userData) {
            return ReS(res, constants.resource_not_found, 'User not found.');
        }

        const pipelines = [
            {
                $match: {
                    component_state: componentState.PUBLISHED,
                    created_by_user: new mongoose.Types.ObjectId(req.session._id)
                }
            },
            {
                $group: {
                    _id: null,
                    totalLikes: {
                        $sum: '$likes'
                    },
                    totalViews: {
                        $sum: '$views'
                    },
                    totalPosts: {
                        $sum: 1
                    }
                }
            },
            {
                $project: {
                    _id: 0,
                    totalViews: { $ifNull: ['$totalViews', 0] },
                    totalLikes: { $ifNull: ['$totalLikes', 0] },
                    totalPosts: { $ifNull: ['$totalPosts', 0] }
                }
            }
        ];

        const [result] = await Components.aggregate(pipelines);

        // Extract the statistics from the aggregation result
        const statistics = {
            totalViews: (result?.totalViews) ? result?.totalViews : 0,
            totalLikes: (result?.totalLikes) ? result?.totalLikes : 0,
            totalPosts: (result?.totalPosts) ? result?.totalPosts : 0,
            technologies: (userData?.technologies) ? userData?.technologies : []
        };

        return ReS(res, constants.success_code, 'Creator profile statistics fetch successfully.', statistics);
    } catch (err) {
        // Log the error and return error response
        console.error('Error in getProfileStatistics:', err);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getPublishedComponents(req, res) {
    try {
        const userId = req.session._id; // Get the user ID from session

        const totalDocuments = await Components.countDocuments();

        // Create filter for components based on user
        const filter = {
            component_state: componentState.PUBLISHED,
            created_by_user: new mongoose.Types.ObjectId(userId)
        };

        if (req.body.component_state) {
            filter['component_state'] = req.body.component_state;
        }

        if (req.body.is_paid) {
            filter['is_paid'] = req.body.is_paid;
        }

        if (req.body.title) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.title);
            filter['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        const filterDocuments = await Components.countDocuments(filter);

        // Set default sort
        let sort = {
            'created_at': -1
        };

        const limit = parseInt(req.body.limit) || 12;
        const skip = parseInt(req.body.skip) || 0;

        // Check if sort_by property exists in the request body and set sort criteria accordingly
        if (req.body.sort_by) {
            if (req.body.sort_by === filterTypes.MOST_LIKED) {
                // Sort by likes in descending order
                sort = {
                    likes: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.MOST_POPULAR) {
                // Sort by views in descending order
                sort = {
                    views: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_ADDITIONS) {
                // Sort by created_at in descending order
                sort = {
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.PREMIUM) {
                sort = {
                    is_paid: -1,
                    created_at: -1
                };
            }
        }

        // Aggregation pipeline
        const pipelines = [
            {
                $match: filter // Match components created by the user
            },
            {
                $sort: sort
            },
            {
                $skip: skip
            },
            {
                $limit: limit
            },
            {
                $lookup: {
                    from: 'categories', // Join with categories collection
                    let: { categoryId: '$category_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$_id', '$$categoryId'] }
                            }
                        },
                        {
                            $project: {
                                category_name: 1,
                                category_slug: 1
                            }
                        }
                    ],
                    as: 'category_id'
                }
            },
            {
                $lookup: {
                    from: 'users', // Join with users collection
                    let: { creatorId: '$created_by_user' },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$_id', '$$creatorId'] }
                            }
                        },
                        {
                            $project: {
                                email: 1,
                                username: 1,
                                first_name: 1,
                                last_name: 1,
                                avatar: 1,
                                biography: 1
                            }
                        }
                    ],
                    as: 'created_by_user'
                }
            },
            {
                $lookup: {
                    from: 'gitlab_repositories',
                    let: {
                        public_repository_id: '$public_repository_id'
                    },
                    pipeline: [{
                        $match: {
                            $expr: {
                                $and: [{
                                    $eq: ['$_id', '$$public_repository_id']
                                }]
                            }
                        }
                    }, {
                        $project: {
                            stars: 1,
                            forks: 1,
                            gitlab_languages: 1,
                            last_pushed_at: 1
                        }
                    }],
                    as: 'public_repository_id'
                }
            },
            {
                $project: {
                    title: 1,
                    slug: 1,
                    image_url: 1,
                    thumbnail_url: 1,
                    video_url: 1,
                    short_description: 1,
                    long_description: 1,
                    orientation: 1,
                    views: 1,
                    likes: 1,
                    downloads: 1,
                    bookmarks: 1,
                    component_state: 1,
                    component_type: 1,
                    created_at: 1,
                    elements_data: 1,
                    live_preview: 1,
                    gif_status: 1,
                    linked_output: 1,
                    gif_url: 1,
                    element_container_meta: 1,
                    created_by_user: { $arrayElemAt: ['$created_by_user', 0] },
                    public_repository_id: { $arrayElemAt: ['$public_repository_id', 0] },
                    category_id: { $arrayElemAt: ['$category_id', 0] },
                    component_draft_id: 1,
                    is_paid: 1
                }
            }
        ];

        // Perform the aggregation directly on the `components` collection
        const components = await Components.aggregate(pipelines);

        // Prepare the response object
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: components
        };

        // Send response with the filtered data
        return ReS(res, constants.success_code, 'Components Fetched', responseObj);
    } catch (error) {
        // Log the error and return a server error message
        logger.error(`Error at getCreatedComponents function: ${error}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function generateGitlabAccessToken(req, res) {
    try {
        // Get user ID from the session
        const userId = req.session._id;

        // Find user data from the database
        const userData = await Users.findOne({ _id: userId }, 'gitlab_user_exists').lean();

        // If user data is not found, return error
        if (!userData) {
            return ReS(res, constants.resource_not_found, 'Oops! User Not Found.');
        }

        // If the user does not exist on GitLab, return error
        if (!userData.gitlab_user_exists) {
            return ReS(res, constants.bad_request_code, 'Oops! User Not exists on Gitlab.');
        }

        // Retrieve GitLab user data
        const gitlabUser = await GitlabUsers.findOne({ user_id: userId }, 'gitlab_user_id personal_access_token_name').lean();

        // Retrieve last Gitlab access token details
        const lastAccessToken = await GitlabUserTokens.findOne({
            user_id: gitlabUser._id
        }).sort({
            created_at: -1
        });

        // Generate a new token name for the GitLab user
        const tokenName = generateGitlabTokenName(lastAccessToken.personal_access_token_name);

        // Create a new GitLab user access token
        const gitlabUserToken = await createUserAccessToken(gitlabUser.gitlab_user_id, tokenName, gitlabUserScope);

        // Create GitLab user document with the generated token and its details
        await GitlabUserTokens.create({
            user_id: gitlabUser._id,
            personal_access_token_name: tokenName,
            personal_access_tokens: encryptDataWithAES(gitlabUserToken.token),
            token_expires_at: gitlabUserToken.expires_at,
            scopes: gitlabUserToken.scopes
        });
        // Return success response
        return ReS(res, constants.success_code, 'Gitlab access token generated successfully.', {
            personal_access_token_name: tokenName,
            personal_access_tokens: gitlabUserToken.token,
            token_expires_at: new Date(gitlabUserToken.expires_at)
        });
    } catch (err) {
        // Log and handle errors
        console.log('Error in generateGitlabAccessToken:', err);
        return sendError(res, err);
    }
}

async function getActiveDraftComponents(req, res) {
    try {
        const userId = req.session._id; // Get the user ID from session

        const { component_type } = req.body;

        const totalDocuments = await DraftComponents.countDocuments({
            component_state: {
                $in: [componentState.ACTIVE_DRAFT]
            },
            created_by_user: new mongoose.Types.ObjectId(userId)
        });

        // Create filter for components based on user
        const filter = {
            component_state: {
                $in: [componentState.ACTIVE_DRAFT]
            },
            created_by_user: new mongoose.Types.ObjectId(userId)
        };

        // Check if 'component_type' is provided in the request body
        if (component_type) {
            filter['component_type'] = component_type;
        }

        const filterDocuments = await DraftComponents.countDocuments(filter);

        const limit = parseInt(req.body.limit) || 12;
        const skip = parseInt(req.body.skip) || 0;

        // Set default sort
        let sort = {
            created_at: -1
        };

        // Check if sort_by property exists in the request body and set sort criteria accordingly
        if (req.body.sort_by) {
            if (req.body.sort_by === filterTypes.RECENT_ADDITIONS) {
                // Sort by created_at in descending order
                sort = {
                    created_at: -1
                };
            }
            else if (req.body.sort_by === filterTypes.RECENT_UPDATES) {
                // Sort by updated_at in descending order
                sort = {
                    updated_at: -1
                };
            }
        }

        // Aggregation pipeline
        const pipelines = [
            {
                $match: filter // Match components created by the user
            },
            {
                $sort: sort
            },
            {
                $skip: skip
            },
            {
                $limit: limit
            },
            {
                $lookup: {
                    from: 'users', // Join with users collection
                    let: { creatorId: '$created_by_user' },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$_id', '$$creatorId'] }
                            }
                        },
                        {
                            $project: {
                                username: 1
                            }
                        }
                    ],
                    as: 'created_by_user'
                }
            },
            {
                $lookup: {
                    from: 'categories', // Join with categories collection
                    let: { categoryId: '$category_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$_id', '$$categoryId'] }
                            }
                        },
                        {
                            $project: {
                                category_name: 1,
                                category_slug: 1
                            }
                        }
                    ],
                    as: 'category_id'
                }
            },
            {
                $lookup: {
                    from: 'components', // Join with components collection for published 
                    let: { component_draft_id: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$component_draft_id', '$$component_draft_id'] }
                            }
                        },
                        {
                            $project: {
                                title: 1,
                                slug: 1,
                                component_state: 1
                            }
                        }
                    ],
                    as: 'published_component'
                }
            },
            {
                $project: {
                    title: 1,
                    slug: 1,
                    image_url: 1,
                    thumbnail_url: 1,
                    video_url: 1,
                    short_description: 1,
                    long_description: 1,
                    orientation: 1,
                    component_state: 1,
                    component_type: 1,
                    created_at: 1,
                    updated_at: 1,
                    elements_data: 1,
                    live_preview: 1,
                    gif_status: 1,
                    linked_output: 1,
                    gif_url: 1,
                    element_container_meta: 1,
                    created_by_user: { $arrayElemAt: ['$created_by_user', 0] },
                    category_id: { $arrayElemAt: ['$category_id', 0] },
                    published_component: { $arrayElemAt: ['$published_component', 0] }
                }
            }
        ];

        // Perform the aggregation directly on the `draft_components` collection
        const draftComponents = await DraftComponents.aggregate(pipelines);
        // Prepare the response object
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: draftComponents
        };

        // Send response with the filtered data
        return ReS(res, constants.success_code, 'Components Fetched', responseObj);
    } catch (error) {
        // Log the error and return a server error message
        logger.error(`Error at getActiveDraftComponents function: ${error}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function userLogout(req, res) {
    try {
        const userId = req.session._id; // Get the user ID from session
        const sessionId = req.session.sessionId; // Get the session ID from session
        // Remove the token for the specific session from Redis
        await redis.del(`user:${userId}:session:${sessionId}`);
        // Send response with the filtered data
        return ReS(res, constants.success_code, 'Logged out successfully');
    } catch (error) {
        // Log the error and return a server error message
        logger.error(`Error at userLogout function: ${error}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function sendOTPForSecurityAction(req, res) {
    try {
        const { action, email } = req.body;

        const userId = req.session._id;

        const currentUser = await Users.findById(userId, 'username first_name').lean();

        // Check if the provided email matches the current session email
        if (currentUser?.email === email) {
            return ReS(res, constants.bad_request_code, 'The provided email is the same as your current email. Please select a different email to update.');
        }

        // Check if the email is already in use by another user
        const existingUser = await Users.findOne({
            email,
            _id: { $ne: userId }
        }, 'email username first_name').lean();

        if (existingUser) {
            return ReS(res, constants.bad_request_code, 'The provided email is already in use. Please choose a different email to update.');
        }

        // Handle update email action
        if (action === userActions.UPDATE_EMAIL) {

            const username = currentUser?.username || currentUser?.first_name;

            await saveAndSendUpdateEmailOTP(userId, email, username);
        }

        return ReS(res, constants.success_code, 'OTP has been successfully sent.');
    } catch (error) {
        // Log the error and send a generic server error response
        logger.error(`Error in sendOTPForSecurityAction: ${error}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function verifyNewEmail(req, res) {
    try {
        const { new_email, otp } = req.body;

        const userId = req.session._id;

        const currentUser = await Users.findById(userId, 'username first_name is_social_login email').lean();

        if (currentUser?.is_social_login) {
            return ReS(res, constants.bad_request_code, 'Your account is connected to Google');
        }

        // Check if the provided email matches the current session email
        if (currentUser?.email === new_email) {
            return ReS(res, constants.bad_request_code, 'The provided email is the same as your current email. Please select a different email to update.');
        }

        const existingUser = await Users.findOne({
            email: new_email,
            _id: {
                $ne: userId
            }
        }, 'email username first_name').lean();

        if (existingUser) {
            return ReS(res, constants.bad_request_code, 'The provided email is already in use. Please choose a different email to update.');
        }

        // Get OTP expiry length from environment variables
        const otpExpireLength = parseInt(process.env.OTP_EXPIRE_LENGTH);

        // Find OTP record within the expiry period
        const findOTP = await UserOTPs.findOne({
            user_id: userId,
            action: userActions.UPDATE_EMAIL,
            created_at: {
                $gte: moment(moment().subtract(otpExpireLength, 'minutes'))
            },
            is_expired: false,
            otp: otp.toString()
        }).sort({ 'created_at': -1 }).lean();

        // Check if OTP record exists
        if (!findOTP) {
            return ReS(res, constants.bad_request_code, 'Oops! Verification failed. The OTP entered is incorrect.');
        }

        await Users.updateOne({
            _id: userId
        }, {
            $set: {
                email: new_email
            }
        });

        return ReS(res, constants.success_code, 'Email changed successfully');
    } catch (error) {
        // Log the error and send a generic server error response
        logger.error(`Error in verifyNewEmail: ${error}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function fetchUserPointHistory(req, res) {
    try {

        const userId = req.session._id;

        const conditions = {
            'user_id': new mongoose.Types.ObjectId(userId)
        };

        const pipelines = [{
            $match: conditions
        }, {
            $sort: {
                created_at: -1
            }
        }, {
            $lookup: {
                from: 'components', // Join with components collection
                let: { component_id: '$component_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$component_id'] }
                        }
                    },
                    {
                        $project: {
                            title: 1,
                            slug: 1
                        }
                    }
                ],
                as: 'component'
            }
        }, {
            $project: {
                user_id: 1,
                points: 1,
                activity: 1,
                created_at: 1,
                description: 1,
                component: {
                    $arrayElemAt: ['$component', 0]
                }
            }
        }];

        const pointHistory = await UserMpnPointLogs.aggregate(pipelines);

        return ReS(res, constants.success_code, 'Point History Fetched', pointHistory);
    } catch (error) {
        // Log the error and send a generic server error response
        logger.error(`Error in fetchUserPointHistory: ${error}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function fetchUserFiatHistory(req, res) {
    try {

        const userId = req.session._id;

        const conditions = {
            'user_id': new mongoose.Types.ObjectId(userId)
        };

        const pipelines = [{
            $match: conditions
        }, {
            $sort: {
                created_at: -1
            }
        }, {
            $lookup: {
                from: 'components', // Join with components collection
                let: { component_id: '$component_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$component_id'] }
                        }
                    },
                    {
                        $project: {
                            title: 1,
                            slug: 1
                        }
                    }
                ],
                as: 'component'
            }
        }, {
            $project: {
                user_id: 1,
                points: 1,
                activity: 1,
                created_at: 1,
                component: {
                    $arrayElemAt: ['$component', 0]
                }
            }
        }];

        const fiatHistory = await UserFiatHistory.aggregate(pipelines);

        return ReS(res, constants.success_code, 'Fiat History Fetched', fiatHistory);
    } catch (error) {
        // Log the error and send a generic server error response
        logger.error(`Error in fetchUserFiatHistory: ${error}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function fetchUserSalesHistory(req, res) {
    try {

        const creatorId = req.session._id;

        const { duration = salesFilterTypes.ALL } = req.body;

        const conditions = {
            'creator_id': new mongoose.Types.ObjectId(creatorId),
            ...(duration !== salesFilterTypes.ALL && {
                created_at: getDateFilterConditions(duration)
            })
        };

        const filterDocuments = await ComponentSales.countDocuments(conditions);

        const pipelines = [{
            $match: conditions
        }, {
            $sort: {
                created_at: -1
            }
        }, {
            $lookup: {
                from: 'components',
                let: { component_id: '$component_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$component_id'] }
                        }
                    },
                    {
                        $project: {
                            title: 1,
                            slug: 1
                        }
                    }
                ],
                as: 'component'
            }
        }, {
            $lookup: {
                from: 'users',
                let: { purchaser_id: '$purchaser_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$purchaser_id'] }
                        }
                    },
                    {
                        $project: {
                            first_name: 1,
                            last_name: 1,
                            username: 1
                        }
                    }
                ],
                as: 'purchaser'
            }
        }, {
            $project: {
                user_id: 1,
                purchase_price: 1,
                item_price: 1,
                buyer_fee: 1,
                author_fee: 1,
                mpn_parity: 1,
                total_earning: 1,
                created_at: 1,
                purchaser: {
                    $arrayElemAt: ['$purchaser', 0]
                },
                component: {
                    $arrayElemAt: ['$component', 0]
                }
            }
        }];

        const salesHistory = await ComponentSales.aggregate(pipelines);

        // Prepare the response object
        const responseObj = {
            recordsFiltered: filterDocuments,
            list: salesHistory
        };

        return ReS(res, constants.success_code, 'Fiat History Fetched', responseObj);
    } catch (error) {
        // Log the error and send a generic server error response
        logger.error(`Error in fetchUserSalesHistory: ${error}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function fetchUserCodeSpaceHistory(req, res) {
    try {
        const userId = req.session._id;

        const { codeSpaces } = req.body;

        if (!Array.isArray(codeSpaces) || codeSpaces.length === 0) {
            return ReS(res, constants.success_code, 'No codespaces provided', {});
        }

        // Fetch all starred and forked statuses in parallel
        const [starredRepos, forkedRepos] = await Promise.all([
            RepositoryStars.find({ user_id: userId, repository_id: { $in: codeSpaces } }).lean(),
            GitlabRepository.find({ user_id: userId, is_active: true, fork_id: { $in: codeSpaces } }).lean()
        ]);

        // Convert results into a Set for fast lookup
        const starredSet = new Set(starredRepos.map((repo) => repo.repository_id.toString()));
        const forkedSet = new Set(forkedRepos.map((repo) => repo.fork_id.toString()));

        // Construct the response object
        const repository = {};
        for (const repositoryId of codeSpaces) {
            if (!repositoryId) continue;
            repository[repositoryId] = {
                starred: starredSet.has(repositoryId),
                forked: forkedSet.has(repositoryId)
            };
        }

        return ReS(res, constants.success_code, 'Codespace History Fetched', repository);
    } catch (error) {
        logger.error(`Error in fetchUserCodeSpaceHistory: ${error}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}



module.exports = {
    getUserDetails,
    getUserMpcBalance,
    getUserComponentUnlockHistory,
    getUnlockedComponents,
    getUnlockedComponentSlugs,
    getUserTopUpHistory,
    uploadProfilePicture,
    updateUserProfile,
    submitBonusRequest,
    createGitlabUser,
    downloadGitlabCredentials,
    checkGitlabUserNameAvailability,
    getUserGitlabCredentials,
    getProfileStatistics,
    getPublishedComponents,
    generateGitlabAccessToken,
    getActiveDraftComponents,
    userLogout,
    sendOTPForSecurityAction,
    verifyNewEmail,
    fetchUserPointHistory,
    fetchUserFiatHistory,
    fetchUserSalesHistory,
    fetchUserCodeSpaceHistory
};
