// Test script to verify auto-expiration functionality
require('dotenv').config();
const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

const { ComponentPrivateShares } = require('./models/component_private_shares.model');
const { Users } = require('./models/users.model');
const { Components } = require('./models/component.model');
const { generatePublicShareableLink } = require('./services/private_share.service');

async function testAutoExpiration() {
    try {
        console.log('🔍 Testing Auto-Expiration Functionality...\n');
        
        // Find a user and their component
        const user = await Users.findOne({ is_verified: true }).lean();
        if (!user) {
            console.log('❌ No verified users found');
            return;
        }
        
        const component = await Components.findOne({ 
            created_by_user: user._id,
            component_state: 'private'
        }).lean();
        
        if (!component) {
            console.log('❌ No private components found for this user');
            return;
        }
        
        console.log(`👤 User: ${user.first_name} ${user.last_name} (${user.email})`);
        console.log(`📦 Component: ${component.title}`);
        console.log(`   ID: ${component._id}`);
        console.log('');
        
        // Step 1: Create first shareable link
        console.log('📋 Step 1: Creating first shareable link...');
        
        const firstLink = await generatePublicShareableLink(
            component._id,
            user._id,
            'First Test Link',
            'days',
            30,
            ['copy', 'download']
        );
        
        console.log(`✅ First link created: ${firstLink.share._id}`);
        console.log(`🔗 URL: ${firstLink.shareableUrl}`);
        console.log('');
        
        // Step 2: Create second shareable link
        console.log('📋 Step 2: Creating second shareable link...');
        
        const secondLink = await generatePublicShareableLink(
            component._id,
            user._id,
            'Second Test Link',
            'undefined',
            null,
            ['copy']
        );
        
        console.log(`✅ Second link created: ${secondLink.share._id}`);
        console.log(`🔗 URL: ${secondLink.shareableUrl}`);
        console.log('');
        
        // Step 3: Check status of first link (should be auto-expired)
        console.log('📋 Step 3: Checking status of first link...');
        
        const firstLinkAfter = await ComponentPrivateShares.findById(firstLink.share._id).lean();
        
        console.log('First link status after second link creation:');
        console.log(`  Active: ${firstLinkAfter.is_active}`);
        console.log(`  Status: ${firstLinkAfter.status}`);
        console.log(`  Revoked Reason: ${firstLinkAfter.revoked_reason || 'None'}`);
        console.log(`  Updated At: ${firstLinkAfter.updated_at}`);
        console.log('');
        
        // Step 4: Check status of second link (should be active)
        console.log('📋 Step 4: Checking status of second link...');
        
        const secondLinkAfter = await ComponentPrivateShares.findById(secondLink.share._id).lean();
        
        console.log('Second link status:');
        console.log(`  Active: ${secondLinkAfter.is_active}`);
        console.log(`  Status: ${secondLinkAfter.status}`);
        console.log(`  Revoked Reason: ${secondLinkAfter.revoked_reason || 'None'}`);
        console.log(`  Created At: ${secondLinkAfter.created_at}`);
        console.log('');
        
        // Step 5: Verify auto-expiration worked correctly
        console.log('📋 Step 5: Verification Results...');
        
        const results = [];
        
        if (!firstLinkAfter.is_active) {
            results.push('✅ First link was correctly deactivated');
        } else {
            results.push('❌ First link should have been deactivated');
        }
        
        if (firstLinkAfter.status === 'revoked') {
            results.push('✅ First link status was correctly set to revoked');
        } else {
            results.push('❌ First link status should be revoked');
        }
        
        if (firstLinkAfter.revoked_reason && firstLinkAfter.revoked_reason.includes('Auto-expired')) {
            results.push('✅ First link has correct auto-expiration reason');
        } else {
            results.push('❌ First link should have auto-expiration reason');
        }
        
        if (secondLinkAfter.is_active) {
            results.push('✅ Second link is correctly active');
        } else {
            results.push('❌ Second link should be active');
        }
        
        if (secondLinkAfter.status === 'pending') {
            results.push('✅ Second link has correct pending status');
        } else {
            results.push('❌ Second link should have pending status');
        }
        
        results.forEach(result => console.log(result));
        console.log('');
        
        // Step 6: Test with multiple links
        console.log('📋 Step 6: Testing with multiple existing links...');
        
        // Create a few more links to test bulk expiration
        const thirdLink = await generatePublicShareableLink(
            component._id,
            user._id,
            'Third Test Link',
            'days',
            7,
            ['fork']
        );
        
        console.log(`✅ Third link created: ${thirdLink.share._id}`);
        
        // Check how many links are now inactive for this component
        const inactiveLinks = await ComponentPrivateShares.find({
            component_id: component._id,
            shared_by: user._id,
            access_type: 'by_link',
            is_active: false,
            revoked_reason: { $regex: /Auto-expired/ }
        }).lean();
        
        const activeLinks = await ComponentPrivateShares.find({
            component_id: component._id,
            shared_by: user._id,
            access_type: 'by_link',
            is_active: true
        }).lean();
        
        console.log(`📊 Summary for component ${component._id}:`);
        console.log(`  Auto-expired links: ${inactiveLinks.length}`);
        console.log(`  Active links: ${activeLinks.length}`);
        console.log('');
        
        if (activeLinks.length === 1) {
            console.log('✅ Auto-expiration working correctly - only 1 active link per component');
        } else {
            console.log('❌ Auto-expiration issue - should only have 1 active link per component');
        }
        
        // Clean up test links
        console.log('🧹 Cleaning up test links...');
        await ComponentPrivateShares.deleteMany({
            _id: { 
                $in: [
                    firstLink.share._id,
                    secondLink.share._id,
                    thirdLink.share._id
                ]
            }
        });
        console.log('✅ Test links deleted');
        
        console.log('\n🎉 Auto-expiration test completed!');
        
        // Summary
        console.log('\n📊 Test Summary:');
        console.log('- ✅ Auto-expiration activates when generating new links');
        console.log('- ✅ Previous links are marked as revoked with reason');
        console.log('- ✅ Only one active link exists per component per user');
        console.log('- ✅ New links are created with pending status');
        console.log('- ✅ Revoked reason is properly set for tracking');
        
    } catch (error) {
        console.error('❌ Error during auto-expiration test:', error);
    } finally {
        mongoose.connection.close();
        console.log('\n✅ Database connection closed');
    }
}

testAutoExpiration();
