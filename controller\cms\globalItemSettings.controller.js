// Constants declaration
const constants = require('../../config/constants');

// Service declaration
const { ReS, sendError } = require('../../services/general.helper');
const { componentType } = require('../../config/component.constant');

// Models declaration
const GlobalItemSettings = require('../../models/global_item_settings.model').GlobalItemSettings;

async function updateGlobalItemSettings(req, res) {
    try {
        const { item_type } = req.params;
        const { price, fees } = req.body;
        const validItemTypes = [componentType.ELEMENTS, componentType.REPOSITORY, componentType.MOBILE];

        // Validate input
        if (!validItemTypes.includes(item_type)) {
            return ReS(res, constants.bad_request_code, 'Oops! Invalid item type provided for global settings');
        }
        // Check if settings exist
        const globalSettings = await GlobalItemSettings.findOne({ item_type });
        if (!globalSettings) {
            return ReS(res, constants.resource_not_found, 'Oops! Global item settings not found.');
        }

        // Update settings
        await GlobalItemSettings.updateOne({
            item_type
        }, {
            $set: {
                price,
                fees
            }
        });
        return ReS(res, constants.success_code, 'Global item settings updated successfully');
    } catch (err) {
        console.error(`Error updating global item settings: ${err.message}`);
        return sendError(res, err);
    }
}

async function listGlobalItemSettings(req, res) {
    try {
        const { skip = 0, limit = 10 } = req.body;
        // Get total count of documents
        const totalDocuments = await GlobalItemSettings.countDocuments();
        // Set default conditions
        const conditions = {};
        // Set default sort
        const sort = {
            'item_type': 1
        };
        const filterDocuments = await GlobalItemSettings.countDocuments(conditions);
        const settingsList = await GlobalItemSettings.find(conditions).sort(sort).skip(skip).limit(limit);
        return ReS(res, constants.success_code, 'Global item settings updated successfully', {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: settingsList
        });
    } catch (err) {
        console.error(`Error fetching global item settings: ${err.message}`);
        return sendError(res, err);
    }
}

async function getGlobalItemSettings(req, res) {
    try {
        const { item_type } = req.params;
        // Check if settings exist
        const globalSettings = await GlobalItemSettings.findOne({ item_type });
        if (!globalSettings) {
            return ReS(res, constants.resource_not_found, 'Oops! Global item settings not found.');
        }
        return ReS(res, constants.success_code, 'Global item settings fetched successfully', globalSettings);
    } catch (err) {
        console.error(`Error fetching global item settings: ${err.message}`);
        return sendError(res, err);
    }
}

module.exports = {
    updateGlobalItemSettings,
    listGlobalItemSettings,
    getGlobalItemSettings
};