const createError = require('http-errors');
const express = require('express');
const path = require('path');
const cookieParser = require('cookie-parser');
const logger = require('morgan');
require('dotenv').config();
require('./config/redis');
const http = require('http');
const database = require('./config/database');
const cors = require('cors');
const fileUpload = require('express-fileupload');
const statusCode = require('./config/constants');
const pino = require('./config/logger');
const { setupGifWorker } = require('./workers/gifWorker');
const { initializeSocketServer } = require('./services/socket.service');

const app = express();
app.use(logger('dev'));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));
app.set('port', process.env.PORT);
app.use(cors());
app.use(fileUpload({
    limits: {
        fileSize: 50 * 1024 * 1024 // 50MB limit
    },
    abortOnLimit: true,
    useTempFiles: true,
    tempFileDir: '/tmp/'
}));


const server = http.createServer(app);

// Initialize Socket.IO server
initializeSocketServer(server);

//server maintenance check middleware.
app.all('/api/front/*', [require('./middlewares/checkMaintenance')]);
/* auth middleware */
app.all('/api/cms/v1/*', [require('./middlewares/validateRequest')]);
app.all('/api/front/v1/*', [require('./middlewares/validateRequestFront')]);

//Routes
// Config Router Grouping
express.application.prefix = express.Router.prefix = function (path, configure) {
    const router = express.Router();
    this.use(path, router);
    configure(router);
    this.disable('x-powered-by');
    return router;
};

app.use('/', require('./routes'));

// catch 404 and forward to error handler
app.use(function (req, res, next) {
    next(createError(statusCode.resource_not_found));
});

// error handler
app.use(function (err, req, res) {
    // set locals, only providing error in development
    res.locals.message = err.message;
    res.locals.error = req.app.get('env') === 'development' ? err : {};

    // render the error page
    res.status(err.status || statusCode.server_error_code);
    res.json('error');
});


//Promise Handler
process.on('unhandledRejection', (error) => {
    console.trace('unhandledRejection Error', error);
    pino.error(`unhandledRejection Error: ${error}`);
});

process.on('uncaughtException', (error) => {
    console.error('uncaughtException Error', error);
    pino.error(`uncaughtException Error: ${error}`);
}); // Ignore error

database();

// Start worker in same process
setupGifWorker();
server.listen(process.env.PORT);
server.on('listening', () => {
    pino.info(`${process.env.PRODUCT_NAME} Application is running on ${process.env.PORT} port....`);
});

require('./services/cron.service');

(async () => {
    const { createAdminUser, checkDefaultSiteSettings, checkDefaultGitlabAdminUser, createDefaultLicense, checkDefaultItemSettings } = require('./services/initial.service');
    await createAdminUser();
    await checkDefaultSiteSettings();
    await checkDefaultGitlabAdminUser();
    await createDefaultLicense();
    await checkDefaultItemSettings();
})();

module.exports = app;