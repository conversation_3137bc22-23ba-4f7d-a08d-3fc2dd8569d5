const { GitlabRepository } = require('../models/gitlab_repository.model');
const { Users } = require('../models/users.model');
const { fetchGitlabProjectStatistics } = require('./gitlab.helper');


async function fetchAndUpdateProjectStorageSize(projectId) {
    try {
        const statistics = await fetchGitlabProjectStatistics(projectId);
        if (statistics?.storage_size) {
            await GitlabRepository.updateOne({
                project_id: projectId
            }, {
                $set: {
                    storage_size: statistics?.storage_size
                }
            });
            console.log(`Repository with project_id => ${projectId} updated with storage_size => ${statistics?.storage_size}`);
        }
    } catch (error) {
        throw error;
    }
}

async function fetchAndSyncUserUsedStorage(projectId) {
    try {
        // Step 1: Find the repository with the given project ID to get the user_id
        const repo = await GitlabRepository.findOne({ project_id: projectId }, 'user_id').lean();

        if (repo?.user_id) {
            // Step 2: Find all repositories belonging to the user and sum their storage_size
            const repositories = await GitlabRepository.find({ user_id: repo.user_id, is_active: true }, 'storage_size').lean();

            const totalStorageSize = repositories.reduce((sum, repo) => sum + (repo.storage_size || 0), 0);

            // Step 3: Update the total storage size in the User collection
            await Users.updateOne({
                _id: repo.user_id
            }, {
                $set: {
                    used_storage: totalStorageSize,
                    last_storage_sync: new Date()
                }
            });
            console.log(`User => ${repo?.user_id} updated with used_storage => ${totalStorageSize}`);
        }
    } catch (error) {
        throw error;
    }

}

module.exports = {
    fetchAndUpdateProjectStorageSize,
    fetchAndSyncUserUsedStorage
};  