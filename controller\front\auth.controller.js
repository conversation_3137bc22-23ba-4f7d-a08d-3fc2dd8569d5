/* eslint-disable no-undef */
// Npm declaration
const Jwt = require('jsonwebtoken');
const moment = require('moment');

// Models declaration
const Users = require('../../models/users.model').Users;
const UserOTPs = require('../../models/user_otps.model').UserOTPs;

// Service declaration
const constants = require('../../config/constants');
const { ReS, sendResponse, convertExpirationToSeconds } = require('../../services/general.helper');
const { saveAndSendSignupOTP, saveAndSendLoginOTP } = require('../../services/auth.service');
const { getUserProfile } = require('../../services/google_helper.service');
const { addFingerprintInDb } = require('../../services/fingerprint_helper.service');
const { addSignUpBonusToWallet } = require('../../services/mpc_balance.service');
const { linkPendingSharesOnSignup } = require('../../services/private_share.service');
const { userActions } = require('../../config/component.constant');
const logger = require('../../config/logger');
const { v4: uuidv4 } = require('uuid');
const redis = require('../../config/redis');

async function signUpWithEmail(req, res) {
    try {
        // Extract required fields from request body
        const { email, first_name, last_name, terms_and_conditions, guest_id, otp, username } = req.body;

        // Extract request ID and visitor ID from headers
        const requestId = req.headers['x-request-id'];
        const visitorId = req.headers['x-visitor-id'];

        // Check if the user with provided email already exists
        const platformUser = await Users.findOne({ email }).lean();

        if (platformUser && platformUser.is_verified) {
            // If user already exists, return conflict status with user details
            return res.status(constants.accepted_code).json({
                'status': 409, 'message': 'Oops! Your account already registered please login', 'data': {
                    user_id: platformUser._id,
                    is_verified: platformUser.is_verified
                }
            });
        }

        if (!otp) {
            // Save and send sign-up OTP
            await saveAndSendSignupOTP(email, first_name);
            return ReS(res, constants.accepted_code, 'OTP has been successfully sent. Please check your inbox.', {
                email,
                first_name,
                last_name,
                guest_id,
                username
            });
        }

        const foundUserOTP = await UserOTPs.findOne({
            action: userActions.SIGNUP,
            email: email
        }).sort({ 'created_at': -1 }).lean();

        if (!foundUserOTP || foundUserOTP.otp !== otp.toString()) {
            return ReS(res, constants.bad_request_code, 'Oops! Verification failed. The OTP entered is incorrect.');
        }

        // Prepare user data for creation or update
        const postData = {
            email,
            first_name,
            last_name,
            terms_and_conditions,
            username: username,
            guest_id: guest_id,
            is_verified: true,
            last_login: new Date()
        };

        const newUser = await Users.findOneAndUpdate({
            guest_id: guest_id
        }, {
            $set: postData,
            $setOnInsert: {
                createdOn: new Date()
            }
        }, {
            upsert: true,
            new: true
        }).lean();

        // Save signup bonus coins
        await addSignUpBonusToWallet(newUser._id);
        
        // Link any pending private shares for this email
        try {
            // Get pending share token from session if user accessed via link
            const pendingShareToken = req.session?.pendingShareToken || null;
            const linkedCount = await linkPendingSharesOnSignup(newUser._id, email, pendingShareToken);
            if (linkedCount > 0) {
                logger.info(`Successfully linked ${linkedCount} pending shares for new user ${newUser._id} (${email})`);
            }
            // Clear the pending token from session after use
            if (pendingShareToken) {
                delete req.session.pendingShareToken;
            }
        } catch (error) {
            logger.error(`Error linking pending shares for user ${newUser._id} (${email}): ${error.message}`, {
                error: error.stack,
                userId: newUser._id,
                email
            });
            // Continue with signup even if linking fails
        }
        
        // Generate unique sessionId
        const sessionId = await uuidv4();
        // Generate JWT token for user authentication
        newUser['sessionId'] = sessionId;
        const token = Jwt.sign({ data: newUser }, process.env.JWT_FRONT_SECRET_KEY, { expiresIn: process.env.JWT_FRONT_EXPIRATION });
        // Store the JWT in Redis (with an expiration)
        const redisExpiration = await convertExpirationToSeconds(process.env.JWT_FRONT_EXPIRATION || '12h');
        await redis.set(`user:${newUser._id}:session:${sessionId}`, token, 'EX', redisExpiration); // expiration
        newUser['token'] = token;
        newUser['is_guest_user'] = false;
        // If request ID and visitor ID are available, add fingerprint data in the database
        if (requestId && visitorId) {
            try {
                await addFingerprintInDb(newUser._id, visitorId, requestId);
            } catch (error) {
                logger.error(`Error while storing fingerprint data while signup${error}`);
            }
        }
        // Return success response with user details
        return res.status(200).json({
            'status': 200, 'message': 'Successfully registered', 'data': newUser
        });
    } catch (err) {
        // Handle errors
        logger.error(`Error at Front Controller signUpWithEmail ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function loginWithEmail(req, res) {
    try {
        const { email } = req.body;

        // Set default conditions
        const conditions = {
            'email': email
        };

        const platformUser = await Users.findOne(conditions, {
            created_at: 0,
            updated_at: 0,
            is_active: 0,
            __v: 0
        }).lean();

        if (platformUser === null) {
            return ReS(
                res,
                constants.bad_request_code,
                'The provided email address does not match any account. Please double-check and try again.'
            );
        }

        if (platformUser.is_verified === false) {
            return ReS(
                res,
                constants.bad_request_code,
                'Your account is not verified. Please verify your email to proceed.',
                { is_verified: false }
            );
        }

        if (platformUser.is_active === false) {
            return ReS(
                res,
                constants.bad_request_code,
                'Your account has been disabled. Please contact support for assistance.'
            );
        }

        await saveAndSendLoginOTP(platformUser._id, email, platformUser.first_name);

        return ReS(
            res,
            constants.accepted_code,
            'A one-time password (OTP) has been sent to your email. Please check your inbox to continue.',
            {
                user_id: platformUser._id,
                email: platformUser.email
            }
        );
    } catch (err) {
        logger.error(`Error at Front Controller loginWithEmail: ${err}`);
        return ReS(
            res,
            constants.server_error_code,
            'An unexpected error occurred. Please try again later.'
        );
    }
}


async function verifyEmailOTP(req, res) {
    try {
        const { otp } = req.body;
        const { user_id } = req.params;

        const platformUser = await Users.findOne({ _id: user_id }, {
            is_verified: 1,
            is_active: 1
        }).lean();

        if (platformUser === null) {
            return ReS(res, constants.resource_not_found, 'Oops! User not found.');
        }

        if (platformUser.is_verified === true) {
            return sendResponse(res, constants.success_code, constants.conflict_code, 'Oops! User is already verified');
        }

        const foundUserOTP = await UserOTPs.findOne({
            user_id: user_id
        }).sort({ 'created_at': -1 }).lean();

        if (!foundUserOTP || foundUserOTP.otp !== otp.toString()) {
            return ReS(res, constants.bad_request_code, 'Oops! Verification failed. The OTP entered is incorrect or has expired');
        }

        await Users.updateOne({
            _id: user_id
        }, {
            $set: {
                is_verified: true
            }
        });
        return ReS(res, constants.success_code, 'Verification Success', { user_id: user_id, is_verified: true });
    } catch (err) {
        logger.error(`Error at Front Controller verifyPhoneOTP${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function checkUsernameAvailability(req, res) {
    try {
        const { username } = req.body;

        // Find user with the given username
        const platformUser = await Users.findOne({ username }).lean();

        // Check if the user exists
        const isAvailable = !platformUser;

        // Return response
        return ReS(
            res,
            constants.success_code,
            isAvailable ? 'Username is available.' : 'Username is already taken.',
            { isAvailable }
        );
    } catch (err) {
        logger.error(`Error in checkUsernameAvailability: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function checkEmailAvailability(req, res) {
    try {
        const { email } = req.body;

        // Find user with the given email
        const platformUser = await Users.findOne({ email }).lean();

        // Check if the user exists
        const isAvailable = (platformUser != null) ? true : false;
        // Return response
        return ReS(res, constants.success_code, isAvailable ? 'Email is already taken' : 'Email is available', { isAvailable });
    } catch (err) {
        logger.error(`Error at Front Controller checkEmailAvailability${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function resendOTPEmail(req, res) {
    try {

        const { email, first_name } = req.body;

        const platformUser = await Users.findOne({ email: email }, {
            is_verified: 1,
            is_active: 1,
            email: 1
        }).lean();

        if (platformUser && platformUser.is_verified == true) {
            return sendResponse(res, constants.success_code, constants.conflict_code, 'Oops! User is already verified');
        }
        await saveAndSendSignupOTP(email, first_name);
        return ReS(res, constants.success_code, 'OTP sent successfully');
    } catch (err) {
        logger.error(`Error at Front Controller resendOTPEmail${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function signUpWithGoogle(req, res) {
    try {
        // Extract idToken from request body
        const { idToken, provider_name, guest_id, username, terms_and_conditions } = req.body;

        // Extract request ID and visitor ID from headers
        const requestId = req.headers['x-request-id'];
        const visitorId = req.headers['x-visitor-id'];

        // Get user profile from Google using idToken
        const googleProfile = await getUserProfile(idToken);

        // Set default conditions for querying user
        const conditions = {
            'email': googleProfile.email
        };

        let platformUser = {};

        // Query user from database based on email
        platformUser = await Users.findOne(conditions, {
            created_at: 0,
            updated_at: 0,
            __v: 0
        }).lean();

        // Check if user account is active
        if (platformUser && platformUser.is_active === false) {
            return ReS(res, constants.unauthorized_code, 'Oops! Your account has been disabled');
        }

        if ((!platformUser || (platformUser && !platformUser.username)) && !username) {
            return ReS(res, constants.success_code, 'Username is missing ask username', {
                username_not_exists: true,
                idToken,
                provider_name,
                guest_id,
                username,
                terms_and_conditions
            });
        }
        // Extract first name, last name, and email from Google profile
        const fullName = googleProfile.name;
        const email = googleProfile.email;
        const names = fullName.split(' ');
        const first_name = names[0];
        const last_name = names[names.length - 1];

        // Prepare data for user creation or update
        const postData = {
            guest_id: guest_id,
            first_name: first_name,
            last_name: last_name,
            email: email,
            is_social_login: true,
            is_verified: true,
            social_id: googleProfile.sub,
            provider_name: provider_name,
            last_login: new Date(),
            avatar: googleProfile.picture
        };

        if (terms_and_conditions) {
            postData['terms_and_conditions'] = terms_and_conditions;
        }
        if (username) {
            postData['username'] = username;
        }

        // If user already exists
        if (platformUser != null) {
            // If user has signed up with social login
            if (platformUser.is_social_login) {
                // Update user information
                platformUser = await Users.findOneAndUpdate({
                    email: postData.email
                }, {
                    $set: postData
                }, {
                    new: true
                }).lean();
                
                // Link any pending private shares for this email when updating existing user
                try {
                    const pendingShareToken = req.session?.pendingShareToken || null;
                    const linkedCount = await linkPendingSharesOnSignup(platformUser._id, platformUser.email, pendingShareToken);
                    if (linkedCount > 0) {
                        logger.info(`Successfully linked ${linkedCount} pending shares for existing user ${platformUser._id} (${platformUser.email})`);
                    }
                    // Clear the pending token from session after use
                    if (pendingShareToken) {
                        delete req.session.pendingShareToken;
                    }
                } catch (error) {
                    logger.error(`Error linking pending shares for existing user ${platformUser._id} (${platformUser.email}): ${error.message}`, {
                        error: error.stack,
                        userId: platformUser._id,
                        email: platformUser.email
                    });
                    // Continue with signup even if linking fails
                }
            } else {
                // If user is logged in via email and password
                return ReS(res, constants.bad_request_code, 'You signed up using email. Please continue with your email.');
            }
        } else {

            platformUser = await Users.findOneAndUpdate({
                guest_id: guest_id
            }, {
                $set: postData
            }, {
                upsert: true,
                new: true
            }).lean();
            // Save signup bonus coins
            await addSignUpBonusToWallet(platformUser._id);
            
            // Link any pending private shares for this email
            try {
                const pendingShareToken = req.session?.pendingShareToken || null;
                const linkedCount = await linkPendingSharesOnSignup(platformUser._id, platformUser.email, pendingShareToken);
                if (linkedCount > 0) {
                    logger.info(`Successfully linked ${linkedCount} pending shares for new Google user ${platformUser._id} (${platformUser.email})`);
                }
                // Clear the pending token from session after use
                if (pendingShareToken) {
                    delete req.session.pendingShareToken;
                }
            } catch (error) {
                logger.error(`Error linking pending shares for new Google user ${platformUser._id} (${platformUser.email}): ${error.message}`, {
                    error: error.stack,
                    userId: platformUser._id,
                    email: platformUser.email
                });
                // Continue with signup even if linking fails
            }
        }

        // Convert user data to plain JSON object
        const platformUserData = JSON.parse(JSON.stringify(platformUser));
        // Generate unique sessionId
        const sessionId = await uuidv4();
        // Generate JWT token for authentication
        const token = Jwt.sign({
            data: {
                first_name: platformUserData.first_name,
                last_name: platformUserData.last_name,
                username: platformUserData.username,
                email: platformUserData.email,
                _id: platformUserData._id,
                sessionId: sessionId
            }
        }, process.env.JWT_FRONT_SECRET_KEY, { expiresIn: process.env.JWT_FRONT_EXPIRATION });
        // Store the JWT in Redis (with an expiration)
        const redisExpiration = await convertExpirationToSeconds(process.env.JWT_FRONT_EXPIRATION || '12h');
        await redis.set(`user:${platformUserData._id}:session:${sessionId}`, token, 'EX', redisExpiration); // expiration
        // Prepare success response object
        const responseObj = {
            first_name: platformUserData.first_name,
            last_name: platformUserData.last_name,
            email: platformUserData.email,
            username: platformUserData.username,
            _id: platformUserData._id,
            token: token,
            is_guest_user: false
        };
        // If request ID and visitor ID are available, add fingerprint data in the database
        if (requestId && visitorId) {
            try {
                await addFingerprintInDb(platformUserData._id, visitorId, requestId);
            } catch (error) {
                logger.error(`Error while storing fingerprint data while social login${error}`);
            }
        }
        // Send success response with user data and token
        return ReS(res, constants.success_code, 'Login Success', responseObj);
    } catch (err) {
        // Handle errors
        logger.error(`Error at Front Controller signUpWithGoogle${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function guestLogin(req, res) {
    try {
        const { guest_id } = req.body;

        const visitorData = await Users.findOne({
            guest_id: guest_id
        }).lean();

        if (visitorData) {
            return ReS(res, constants.bad_request_code, 'Guest already exists');
        }
        // Create a new visitor record
        const guestData = await Users.create({ guest_id: guest_id });
        // Deep cloning of the mongoose object
        const guestDataObj = JSON.parse(JSON.stringify(guestData));
        // Generate unique sessionId
        const sessionId = await uuidv4();
        guestDataObj['sessionId'] = sessionId;
        const token = Jwt.sign({ data: guestDataObj }, process.env.JWT_FRONT_SECRET_KEY, { expiresIn: '30d' });
        // Store the JWT in Redis (with an expiration)
        const redisExpiration = await convertExpirationToSeconds('30d');
        await redis.set(`user:${guestDataObj._id}:session:${sessionId}`, token, 'EX', redisExpiration); // expiration
        // Return success response with token and guest_id
        return ReS(res, constants.success_code, 'Login Success', { token: token, guest_id: guest_id, is_guest_user: true });
    } catch (err) {
        logger.error(`Error at Front Controller guestLogin${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function verifyLoginOTP(req, res) {
    try {
        // Extract OTP and user ID from request
        const { otp } = req.body;
        const { user_id } = req.params;

        // Define conditions to find user
        const conditions = { _id: user_id };

        // Extract request ID and visitor ID from headers
        const requestId = req.headers['x-request-id'];
        const visitorId = req.headers['x-visitor-id'];

        // Find user in the database excluding sensitive information
        const platformUser = await Users.findOne(conditions, {
            password: 0,
            is_deleted: 0,
            forgot_password_token: 0,
            forgot_password_token_expire_time: 0,
            updated_at: 0,
            last_login: 0,
            __v: 0
        }).lean();

        // Check if user exists
        if (platformUser === null) {
            return ReS(res, constants.resource_not_found, 'Oops! User not found.');
        }

        // Check if user account is active
        if (platformUser.is_active === false) {
            return ReS(res, constants.unauthorized_code, 'Oops! Your account has been disabled');
        }

        // Get OTP expiry length from environment variables
        const otpExpireLength = parseInt(process.env.OTP_EXPIRE_LENGTH);

        // Find OTP record within the expiry period
        const findOTP = await UserOTPs.findOne({
            user_id: user_id,
            action: userActions.LOGIN,
            created_at: {
                $gte: moment(moment().subtract(otpExpireLength, 'minutes'))
            },
            is_expired: false,
            otp: otp.toString()
        }).sort({ 'created_at': -1 }).lean();

        // Check if OTP record exists
        if (!findOTP) {
            return ReS(res, constants.bad_request_code, 'Oops! Verification failed. The OTP entered is incorrect.');
        }
        // Mark OTP expired after the one time use
        await UserOTPs.updateOne({
            _id: findOTP._id
        }, {
            $set: {
                is_expired: true
            }
        });
        const sessionId = await uuidv4();
        platformUser['sessionId'] = sessionId;
        // Generate JWT token for user authentication
        const token = Jwt.sign({
            data: {
                _id: platformUser._id,
                first_name: platformUser.first_name,
                last_name: platformUser.last_name,
                email: platformUser.email,
                sessionId: sessionId
            }
        }, process.env.JWT_FRONT_SECRET_KEY, { expiresIn: process.env.JWT_FRONT_EXPIRATION });

        // Store the JWT in Redis (with an expiration)
        const redisExpiration = await convertExpirationToSeconds(process.env.JWT_FRONT_EXPIRATION || '12h');
        await redis.set(`user:${platformUser._id}:session:${sessionId}`, token, 'EX', redisExpiration); // expiration

        platformUser['token'] = token;
        platformUser['is_guest_user'] = false;

        // Update user's last login data in the database
        await Users.updateOne(conditions, {
            $set: {
                last_login: new Date()
            }
        });

        // Link any pending private shares for this email (for login after accessing link)
        try {
            const pendingShareToken = req.session?.pendingShareToken || null;
            if (pendingShareToken) {
                const linkedCount = await linkPendingSharesOnSignup(platformUser._id, platformUser.email, pendingShareToken);
                if (linkedCount > 0) {
                    logger.info(`Successfully linked ${linkedCount} pending shares for logged-in user ${platformUser._id} (${platformUser.email})`);
                }
                // Clear the pending token from session after use
                delete req.session.pendingShareToken;
            }
        } catch (error) {
            logger.error(`Error linking pending shares for logged-in user ${platformUser._id} (${platformUser.email}): ${error.message}`, {
                error: error.stack,
                userId: platformUser._id,
                email: platformUser.email
            });
            // Continue with login even if linking fails
        }

        // If request ID and visitor ID are available, add fingerprint data in the database
        if (requestId && visitorId) {
            try {
                await addFingerprintInDb(platformUser._id, visitorId, requestId);
            } catch (error) {
                logger.error(`Error while storing fingerprint data while login ${error}`);
            }
        }

        // Return success response with user data and token
        return ReS(res, constants.success_code, 'Login Success', platformUser);
    } catch (err) {
        // Log and return error response in case of an exception
        logger.error(`Error at Front Controller verifyLoginOTP ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function resendLoginOTP(req, res) {
    try {

        const { user_id } = req.params;

        const platformUser = await Users.findOne({ _id: user_id }, {
            is_verified: 1,
            is_active: 1,
            email: 1,
            first_name: 1
        }).lean();

        if (platformUser === null) {
            return ReS(res, constants.resource_not_found, 'Oops! User not found.');
        }

        await saveAndSendLoginOTP(platformUser._id, platformUser.email, platformUser.first_name);
        return ReS(res, constants.success_code, 'OTP sent successfully');
    } catch (err) {
        logger.error(`Error at Front Controller resendLoginOTP${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}


module.exports = {
    signUpWithEmail,
    loginWithEmail,
    verifyEmailOTP,
    checkUsernameAvailability,
    checkEmailAvailability,
    resendOTPEmail,
    signUpWithGoogle,
    guestLogin,
    verifyLoginOTP,
    resendLoginOTP
};