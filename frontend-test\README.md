# 🔗 Private Share Frontend Test Environment

This is a complete frontend test environment for the private share functionality. It simulates the entire user flow from email invitations to component access.

## 🚀 Quick Start

### 1. Start the Frontend Server
```bash
cd frontend-test
node server.js
```

The server will start at `http://localhost:8080`

### 2. Configure Backend URL
1. Open `http://localhost:8080` in your browser
2. Update the "Backend URL" field to point to your backend (e.g., `http://localhost:3000`)
3. Click "Save"

### 3. Test the Flow

## 📋 Complete Test Scenarios

### **Scenario 1: Private Email Invitation Flow**

1. **Create Account/Login**
   - Go to `http://localhost:8080/signup.html`
   - Create a test account with your email
   - Verify OTP from email

2. **Share Component Privately**
   - Go to `http://localhost:8080/share-component.html`
   - Enter a component ID (use any valid MongoDB ObjectId format)
   - Select "Private Email Invitation"
   - Enter recipient email addresses
   - Add personal message
   - Set access controls (download, fork)
   - Click "Share Component"

3. **Recipient Access**
   - Check recipient's email for invitation
   - Click the access link in email
   - If recipient has account: Direct access
   - If new user: Signup flow with return URL

### **Scenario 2: Public Shareable Link Flow**

1. **Create Public Link**
   - Go to `http://localhost:8080/share-component.html`
   - Enter component ID
   - Select "Public Shareable Link"
   - Enter link name
   - Set expiration and access controls
   - Click "Share Component"
   - Copy the generated shareable URL

2. **Access Public Link**
   - Open the shareable URL in any browser
   - Should work for anyone (logged in or not)
   - For paid components: Payment flow

### **Scenario 3: Token-Based Access**

1. **Direct Token Access**
   - Go to `http://localhost:8080`
   - Enter access token in "Access Token" field
   - Click "Test Token Access"
   - Enter component slug
   - Click "Access Component"

## 🔗 URL Patterns & Redirection

### **Email Link Format**
The backend sends emails with links in this format:
```
https://multiplatform.blockverse.tech/private-share/{accessToken}
```

### **Frontend Routing**
The test frontend handles these URL patterns:

1. **Private Share Access**: `/private-share/{token}`
   - Maps to: `private-share.html`
   - Extracts token from URL path
   - Validates token and shows appropriate UI

2. **Component View**: `/component-view.html?slug={slug}&token={token}`
   - Shows component content after successful access
   - Displays success message and next steps

3. **Auth with Return URL**: `/login.html?returnUrl={encodedUrl}`
   - Handles authentication flow
   - Redirects back to original URL after login

## 📧 Email Integration Testing

### **Backend Email Service**
The backend uses these email templates:
- `component-private-share-existing-user` - For registered users
- `component-private-share-new-user` - For new users

### **Email URLs**
Emails contain these URLs:
```javascript
// For existing users (direct access)
const accessUrl = `${process.env.SITE_URL}private-share/${accessToken}`;

// For new users (signup with return)
const signupUrl = `${process.env.SITE_URL}auth/signup?returnUrl=${encodeURIComponent(`private-share/${accessToken}`)}`;
```

### **Testing Email Flow**
1. Set up your backend email service
2. Use real email addresses in the share form
3. Check email delivery and click links
4. Verify proper redirection and token handling

## 🛠️ API Endpoints Used

### **Authentication**
- `POST /auth/signup-with-email` - User signup
- `POST /auth/login-with-email` - Send login OTP
- `POST /auth/verify-login-otp/:userId` - Verify OTP

### **Private Sharing**
- `POST /v1/component/:id/share` - Share privately via email
- `POST /v1/component/:id/generate-link` - Create public link
- `GET /component/private-share/:token/validate` - Validate token
- `GET /component/private/:slug?token=xxx` - Access component
- `GET /v1/component/shared-with-me` - List shared components
- `GET /v1/component/shared-with-me/statistics` - Share statistics

## 🎯 Key Features Tested

### **Access Control**
- ✅ Public links accessible by anyone
- ✅ Private invites only for specific users
- ✅ Authentication requirements
- ✅ Payment flow for paid components

### **Token Validation**
- ✅ Valid token access
- ✅ Expired token handling
- ✅ Invalid token errors
- ✅ User-specific validation

### **User Experience**
- ✅ Seamless signup/login flow
- ✅ Return URL handling
- ✅ Success messages and next steps
- ✅ Error handling and feedback

### **Share Management**
- ✅ Create private invitations
- ✅ Generate public links
- ✅ View shared components
- ✅ Access statistics

## 🔧 Configuration

### **Backend URL**
Update the API base URL in the frontend:
1. Open the main page
2. Change "Backend URL" field
3. Click "Save"

### **Environment Variables**
Make sure your backend has these set:
```env
SITE_URL=http://localhost:8080/
JWT_FRONT_SECRET_KEY=your-secret
MAIL_FROM_EMAIL=<EMAIL>
```

## 📱 Mobile Testing

The frontend is responsive and works on mobile devices. Test the complete flow on:
- Desktop browsers
- Mobile browsers
- Different screen sizes

## 🐛 Debugging

### **Check Browser Console**
All API calls and responses are logged to the browser console.

### **View Logs**
Each page has a "Logs" section showing:
- API requests/responses
- Authentication status
- Error messages
- Success confirmations

### **Common Issues**
1. **CORS Errors**: Make sure your backend allows requests from `http://localhost:8080`
2. **Token Extraction**: Check URL format matches expected pattern
3. **Authentication**: Verify JWT tokens are properly set in cookies
4. **Email Delivery**: Check spam folder and email service configuration

## 🎉 Success Indicators

### **Successful Test**
You should see:
1. ✅ Email invitations sent successfully
2. ✅ Recipients receive emails with working links
3. ✅ Token validation works correctly
4. ✅ Component access granted appropriately
5. ✅ Success page shows with "Shared with Me" link
6. ✅ Public links work for anyone
7. ✅ Private invites only work for intended recipients

### **Expected Flow**
```
Share Component → Email Sent → Recipient Clicks Link → 
Authentication (if needed) → Token Validation → 
Component Access → Success Page → Shared with Me Section
```

## 📞 Support

If you encounter issues:
1. Check the browser console for errors
2. Verify API endpoints are working
3. Test with simple curl commands first
4. Check email service configuration
5. Verify database connections

This test environment provides a complete simulation of the private share functionality and helps visualize the entire user journey! 🚀
