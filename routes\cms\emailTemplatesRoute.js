const express = require('express');
const router = express.Router();

const { updateEmailTemplatesFromConfig, addEmailTemplate, getEmailTemplateById, getAllEmailTemplates } = require('../../controller/cms/emailTemplates.controller');
const { addEmailTemplateValidation } = require('../../middlewares/validations/cms/email_templates/emailTemplateValidation');


router.post('/update-email-templates', updateEmailTemplatesFromConfig);
router.post('/create', addEmailTemplateValidation, addEmailTemplate);
router.post('/list', getAllEmailTemplates);
router.get('/details/:id', getEmailTemplateById);

module.exports = router;