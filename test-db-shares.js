// Quick test to check database shares
require('dotenv').config();
const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

const { ComponentPrivateShares } = require('./models/component_private_shares.model');

async function testShares() {
    try {
        console.log('🔍 Testing database shares...');
        
        // Get total count of shares
        const totalShares = await ComponentPrivateShares.countDocuments();
        console.log(`📊 Total shares in database: ${totalShares}`);
        
        // Get active shares
        const activeShares = await ComponentPrivateShares.countDocuments({ is_active: true });
        console.log(`✅ Active shares: ${activeShares}`);
        
        // Get shares by access type
        const byInvite = await ComponentPrivateShares.countDocuments({ access_type: 'by_invite', is_active: true });
        const byLink = await ComponentPrivateShares.countDocuments({ access_type: 'by_link', is_active: true });
        console.log(`📧 By invite: ${byInvite}`);
        console.log(`🔗 By link: ${byLink}`);
        
        // Get sample shares
        const sampleShares = await ComponentPrivateShares.find({ is_active: true })
            .populate('component_id', 'title slug')
            .populate('shared_by', 'first_name last_name email')
            .limit(5)
            .lean();
            
        console.log('\n📋 Sample shares:');
        sampleShares.forEach((share, index) => {
            console.log(`${index + 1}. ${share.component_id?.title || 'Unknown Component'}`);
            console.log(`   Shared by: ${share.shared_by?.first_name} ${share.shared_by?.last_name} (${share.shared_by?.email})`);
            console.log(`   Type: ${share.access_type}`);
            console.log(`   Status: ${share.status}`);
            console.log(`   Email: ${share.shared_with_email || 'N/A'}`);
            console.log(`   Created: ${share.created_at}`);
            console.log('   ---');
        });
        
        // Test the aggregation pipeline conditions
        console.log('\n🧪 Testing aggregation conditions...');
        
        // Test with a sample user (you can replace with actual user ID and email)
        const testUserId = new mongoose.Types.ObjectId('507f1f77bcf86cd799439011'); // Replace with actual user ID
        const testUserEmail = '<EMAIL>'; // Replace with actual email
        
        const conditions = {
            $or: [
                // User-specific private invites
                {
                    access_type: 'by_invite',
                    $or: [
                        { shared_with_user: testUserId },
                        { shared_with_email: testUserEmail }
                    ]
                },
                // Public shareable links (accessible by anyone)
                {
                    access_type: 'by_link',
                    shared_with_user: null,
                    shared_with_email: null
                }
            ],
            is_active: true,
            status: { $in: ['pending', 'accepted'] },
            $or: [
                { expires_at: { $gt: new Date() } },
                { expires_at: null }
            ]
        };
        
        const matchingShares = await ComponentPrivateShares.countDocuments(conditions);
        console.log(`🎯 Shares matching test conditions: ${matchingShares}`);
        
        // Test public links specifically
        const publicLinkConditions = {
            access_type: 'by_link',
            shared_with_user: null,
            shared_with_email: null,
            is_active: true,
            status: { $in: ['pending', 'accepted'] },
            $or: [
                { expires_at: { $gt: new Date() } },
                { expires_at: null }
            ]
        };
        
        const publicLinks = await ComponentPrivateShares.countDocuments(publicLinkConditions);
        console.log(`🔗 Public links available: ${publicLinks}`);
        
        if (publicLinks > 0) {
            const samplePublicLinks = await ComponentPrivateShares.find(publicLinkConditions)
                .populate('component_id', 'title slug')
                .populate('shared_by', 'first_name last_name')
                .limit(3)
                .lean();
                
            console.log('\n🔗 Sample public links:');
            samplePublicLinks.forEach((link, index) => {
                console.log(`${index + 1}. ${link.component_id?.title || 'Unknown Component'}`);
                console.log(`   Link name: ${link.link_name || 'Unnamed'}`);
                console.log(`   Token: ${link.access_token}`);
                console.log(`   Status: ${link.status}`);
                console.log(`   Expires: ${link.expires_at || 'Never'}`);
                console.log('   ---');
            });
        }
        
    } catch (error) {
        console.error('❌ Error testing shares:', error);
    } finally {
        mongoose.connection.close();
        console.log('\n✅ Database connection closed');
    }
}

testShares();
