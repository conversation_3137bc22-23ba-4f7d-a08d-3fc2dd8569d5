// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

// Service declaration
const { ReS, generateSlug, escapeRegex, sendError } = require('../../services/general.helper');

// Models declaration
const Tags = require('../../models/tag.model').Tags;

async function createTag(req, res) {
    try {
        const {
            title,
            description,
            is_active
        } = req.body;

        const tagSlug = generateSlug(title);

        const response = await Tags.create({
            title: title,
            slug: tagSlug,
            description: description,
            is_active: is_active
        });

        return ReS(res, constants.success_code, 'Success', response);
    } catch (err) {
        logger.error(`Error at CMS Controller createTag${err}`);
        return sendError(res, err);
    }
}

async function updateTag(req, res) {
    try {
        const postData = req.body;

        if (postData.title) {
            postData['slug'] = generateSlug(postData.title);
        }

        await Tags.updateOne({
            _id: req.params.id
        }, {
            '$set': postData
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateTag${err}`);
        return sendError(res, err);
    }
}

async function updateTagStatus(req, res) {
    try {
        const { is_active } = req.body;

        await Tags.updateOne({
            _id: req.params.id
        }, {
            '$set': {
                is_active: is_active
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateTagStatus${err}`);
        return sendError(res, err);
    }
}

async function getAllTags(req, res) {
    try {
        const totalDocuments = await Tags.countDocuments();
        const filter = {};
        if (req.body.is_active != undefined) {
            filter.is_active = req.body.is_active;
        }
        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            filter['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        // Set default sort
        const sort = {
            is_active: -1,
            created_at: -1
        };
        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;
        // Count filtered documents after filter apply
        const filterDocuments = await Tags.countDocuments(filter);
        // Aggregation pipeline
        const pipelines = [
            {
                $match: filter // Filter tags by is_active status
            },
            {
                $project: {
                    title: 1, // Include title
                    slug: 1, // Include slug
                    is_active: 1, // Include is_active status
                    project_popularity: 1,
                    elements_popularity: 1,
                    mobile_popularity: 1,
                    updated_at: 1,
                    created_at: 1,
                    total_popularity: { $add: ['$project_popularity', '$elements_popularity', '$mobile_popularity'] } // Add the two fields
                }
            },
            {
                $sort: sort // Sort by the number of total_popularity
            },
            {
                $skip: skip
            },
            {
                $limit: limit
            }];

        const tagList = await Tags.aggregate(pipelines);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: tagList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllTags${err}`);
        return sendError(res, err);
    }
}

async function getTagDetails(req, res) {
    try {
        const filter = {
            _id: req.params.id
        };
        const tagData = await Tags.findOne(filter, {
            'title': 1,
            'slug': 1,
            'description': 1,
            'is_active': 1,
            'created_at': 1
        }).lean();

        return ReS(res, constants.success_code, 'Data Fetched', tagData);
    } catch (err) {
        logger.error(`Error at CMS Controller getTagDetails${err}`);
        return sendError(res, err);
    }
}

async function deleteTags(req, res) {
    try {
        const filter = {
            _id: req.params.id
        };
        const tagData = await Tags.findOne(filter);
        if (tagData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Tag Not Found.');
        }
        await Tags.deleteOne(filter);
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller deleteTags${err}`);
        return sendError(res, err);
    }
}

async function getAllTagsSortList(req, res) {
    try {
        // Define the filter to select active tags only
        const filter = { is_active: true };

        // Default sort order: by the number of total_popularity in descending order
        const sort = {
            total_popularity: -1,
            updated_at: -1
        };

        // Aggregation pipeline
        const pipelines = [
            {
                $match: filter // Filter tags by is_active status
            },
            {
                $project: {
                    title: 1, // Include title
                    slug: 1, // Include slug
                    is_active: 1, // Include is_active status
                    project_popularity: 1,
                    elements_popularity: 1,
                    mobile_popularity: 1,
                    updated_at: 1,
                    created_at: 1,
                    total_popularity: { $add: ['$project_popularity', '$elements_popularity', '$mobile_popularity'] } // Add the two fields
                }
            },
            {
                $sort: sort // Sort by the number of total_popularity
            }
        ];

        // Execute the aggregation query on the Tags collection
        const tagList = await Tags.aggregate(pipelines);

        // Return the successful response with the fetched data
        return ReS(res, constants.success_code, 'Data Fetched', tagList);
    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at CMS Controller getAllTagsSortList: ${err}`);
        return sendError(res, err);
    }
}


module.exports = {
    createTag,
    updateTag,
    getAllTags,
    getTagDetails,
    deleteTags,
    updateTagStatus,
    getAllTagsSortList
};