const { ReS } = require('../services/general.helper');
const constants = require('../config/constants');
const { checkPrivateAccess } = require('../services/private_share.service');
const { GitlabRepository } = require('../models/gitlab_repository.model');
const { Components } = require('../models/component.model');
const { ComponentUnlockHistory } = require('../models/component_unlock_history.model');
const { repositoryState, publishState } = require('../config/component.constant');
const mongoose = require('mongoose');
const logger = require('../config/logger');

/**
 * Enhanced middleware that checks both regular access and private share access for codespace routes
 * This extends the existing validateCodeSpace middleware to support private shares
 */
async function checkCodeSpaceAccessWithPrivateShare(req, res, next) {
    try {
        // Extract the repository ID from request parameters
        const { id, path } = req.params;
        const { token } = req.query; // Private share token

        // Proceed if 'path' exists and includes '.md' or '.markdown' (case-insensitive)
        if (path && (path.toLowerCase().includes('.md') || path.toLowerCase().includes('.markdown'))) {
            return next(); // Move to the next middleware or function
        }

        if (!mongoose.isValidObjectId(id)) {
            return res.status(constants.bad_request_code).json({
                status: constants.bad_request_code,
                message: 'Invalid code-space ID provided.'
            });
        }

        // Find the repository in the GitLabRepository collection by its ID
        const repository = await GitlabRepository.findOne(
            { _id: id },
            'project_id component_id gitlab_user_id state published_state'
        ).lean();

        // If the repository is not found, return a 404 error
        if (!repository) {
            return res.status(constants.resource_not_found).json({
                status: constants.resource_not_found,
                message: 'Oops! code-space not found.'
            });
        }

        // Check if the repository public & published for end-user
        if (repository?.state == repositoryState.PUBLIC && repository?.published_state == publishState.PUBLISHED) {
            return next();
        }

        const userId = req.session?._id;
        const userEmail = req.session?.email;

        // If no component is linked, use original logic
        if (!repository.component_id) {
            const GitlabUsers = require('../models/gitlab_users.model').GitlabUsers;
            const gitlabUser = await GitlabUsers.findOne({
                user_id: userId
            }).lean();

            if (gitlabUser?._id?.toString() == repository?.gitlab_user_id?.toString()) {
                return next();
            } else {
                return res.status(constants.bad_request_code).json({
                    status: constants.bad_request_code,
                    message: 'You do not have access to this code-space.'
                });
            }
        }

        // Fetch the associated component data using the component_id from the repository
        const componentData = await Components.findOne(
            { _id: repository.component_id },
            'component_type is_paid created_by_user component_state'
        ).lean();

        if (!componentData) {
            return res.status(constants.resource_not_found).json({
                status: constants.resource_not_found,
                message: 'Associated component not found.'
            });
        }

        // If user is the component creator, allow access
        if (componentData?.created_by_user?.toString() === userId) {
            return next();
        }

        // Check for private share access if token is provided
        if (token) {
            try {
                logger.info(`🔍 Checking private share access for codespace ${id} with token ${token.substring(0, 8)}...`);
                
                const privateAccess = await checkPrivateAccess(componentData._id, userId, userEmail, token);
                
                if (privateAccess.hasAccess) {
                    // Check if component requires payment and user hasn't unlocked it
                    if (componentData.is_paid) {
                        const unlockHistory = await ComponentUnlockHistory.findOne({
                            component_id: componentData._id,
                            unlock_by: userId,
                            is_active: true
                        });

                        if (!unlockHistory) {
                            return res.status(constants.bad_request_code).json({
                                status: constants.bad_request_code,
                                message: 'This component requires payment. Please unlock it to access the code-space.',
                                requires_payment: true,
                                component_id: componentData._id
                            });
                        }
                    }

                    // Add private share info to request for potential use in controllers
                    req.privateShareAccess = {
                        hasAccess: true,
                        accessType: privateAccess.accessType,
                        sharedBy: privateAccess.sharedBy,
                        accessControls: privateAccess.accessControls || []
                    };

                    logger.info(`✅ Private share access granted for codespace ${id}`);
                    return next();
                }
            } catch (error) {
                logger.error(`❌ Error checking private share access: ${error.message}`);
                // Continue to regular access check if private share fails
            }
        }

        // Regular access check for paid components
        if (componentData?.is_paid) {
            // Find an active unlock history for this component by the current user
            const unlockHistory = await ComponentUnlockHistory.findOne({
                component_id: componentData._id,
                unlock_by: userId,
                is_active: true
            });

            // If no unlock history is found, return a 400 error indicating restricted access
            if (!unlockHistory) {
                return res.status(constants.bad_request_code).json({
                    status: constants.bad_request_code,
                    message: 'Oops! You do not have access to this code-space.',
                    requires_payment: componentData.is_paid,
                    component_id: componentData._id
                });
            }
        }

        // If all checks pass, move on to the next middleware or route handler
        return next();

    } catch (error) {
        logger.error(`Error in checkCodeSpaceAccessWithPrivateShare: ${error.message}`, {
            error: error.stack,
            repositoryId: req.params.id,
            token: req.query.token?.substring(0, 8) + '...',
            userId: req.session?._id
        });
        return res.status(constants.server_error_code).json({
            status: constants.server_error_code,
            message: 'Internal server error while checking access.'
        });
    }
}

/**
 * Lightweight middleware for routes that don't need full access validation
 * Just checks if private share token is valid and adds info to request
 */
async function addPrivateShareContext(req, res, next) {
    try {
        const { token } = req.query;
        const userId = req.session?._id;
        const userEmail = req.session?.email;

        if (token) {
            try {
                const privateAccess = await checkPrivateAccess(null, userId, userEmail, token);
                if (privateAccess.hasAccess) {
                    req.privateShareAccess = {
                        hasAccess: true,
                        accessType: privateAccess.accessType,
                        sharedBy: privateAccess.sharedBy,
                        accessControls: privateAccess.accessControls || [],
                        component: privateAccess.component
                    };
                }
            } catch (error) {
                // Silently fail - this is just context addition
                logger.debug(`Private share context check failed: ${error.message}`);
            }
        }

        next();
    } catch (error) {
        // Don't block the request if context addition fails
        logger.error(`Error in addPrivateShareContext: ${error.message}`);
        next();
    }
}

module.exports = {
    checkCodeSpaceAccessWithPrivateShare,
    addPrivateShareContext
};
